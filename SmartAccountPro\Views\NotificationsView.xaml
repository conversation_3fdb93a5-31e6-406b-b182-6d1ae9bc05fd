<UserControl x:Class="SmartAccountPro.Views.NotificationsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:SmartAccountPro.Views"
             xmlns:vm="clr-namespace:SmartAccountPro.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000"
             FlowDirection="RightToLeft">
    
    <UserControl.DataContext>
        <vm:NotificationsViewModel />
    </UserControl.DataContext>
    
    <Grid Background="{DynamicResource BackgroundColor}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان الصفحة -->
        <Border Grid.Row="0" 
                Background="{DynamicResource HeaderBackgroundColor}" 
                Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="الإشعارات" 
                           FontSize="24" 
                           FontWeight="Bold" 
                           Foreground="{DynamicResource HeaderTextColor}"/>
                
                <StackPanel Grid.Column="1" 
                            Orientation="Horizontal">
                    <Button Content="تحديد الكل كمقروء" 
                            Command="{Binding MarkAllAsReadCommand}" 
                            Style="{StaticResource ModernButton}" 
                            Margin="0,0,10,0"/>
                    <Button Content="حذف المقروءة" 
                            Command="{Binding DeleteReadCommand}" 
                            Style="{StaticResource RedButton}" 
                            Margin="0,0,10,0"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- قائمة الإشعارات -->
        <ScrollViewer Grid.Row="1" 
                      VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding Notifications}" 
                          Margin="20">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Background="{DynamicResource CardBackgroundColor}" 
                                BorderBrush="{DynamicResource BorderColor}" 
                                BorderThickness="1" 
                                CornerRadius="5" 
                                Margin="0,0,0,10" 
                                Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- أيقونة نوع الإشعار -->
                                <Border Grid.Column="0" 
                                        Width="40" 
                                        Height="40" 
                                        CornerRadius="20" 
                                        Background="{Binding TypeColor}" 
                                        Margin="0,0,15,0">
                                    <TextBlock Text="{Binding TypeIcon}" 
                                               FontFamily="Segoe MDL2 Assets" 
                                               FontSize="18" 
                                               Foreground="White" 
                                               HorizontalAlignment="Center" 
                                               VerticalAlignment="Center"/>
                                </Border>
                                
                                <!-- محتوى الإشعار -->
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="{Binding Title}" 
                                               FontWeight="Bold" 
                                               FontSize="14" 
                                               Foreground="{DynamicResource TextColor}"/>
                                    <TextBlock Text="{Binding Content}" 
                                               TextWrapping="Wrap" 
                                               Margin="0,5,0,0" 
                                               Foreground="{DynamicResource TextColor}"/>
                                    <TextBlock Text="{Binding CreatedAtFormatted}" 
                                               FontSize="11" 
                                               Foreground="{DynamicResource SecondaryTextColor}" 
                                               Margin="0,5,0,0"/>
                                </StackPanel>
                                
                                <!-- أزرار الإجراءات -->
                                <StackPanel Grid.Column="2" 
                                            Orientation="Horizontal">
                                    <Button Command="{Binding DataContext.MarkAsReadCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}" 
                                            CommandParameter="{Binding Id}" 
                                            Visibility="{Binding IsReadVisibility}" 
                                            ToolTip="تحديد كمقروء" 
                                            Width="30" 
                                            Height="30" 
                                            Margin="0,0,5,0" 
                                            Background="Transparent" 
                                            BorderThickness="0">
                                        <TextBlock Text="&#xE8FB;" 
                                                   FontFamily="Segoe MDL2 Assets" 
                                                   FontSize="16" 
                                                   Foreground="{DynamicResource PrimaryColor}"/>
                                    </Button>
                                    <Button Command="{Binding DataContext.DeleteNotificationCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}" 
                                            CommandParameter="{Binding Id}" 
                                            ToolTip="حذف" 
                                            Width="30" 
                                            Height="30" 
                                            Background="Transparent" 
                                            BorderThickness="0">
                                        <TextBlock Text="&#xE74D;" 
                                                   FontFamily="Segoe MDL2 Assets" 
                                                   FontSize="16" 
                                                   Foreground="{DynamicResource AccentColor}"/>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
                
                <!-- عرض رسالة عند عدم وجود إشعارات -->
                <ItemsControl.Template>
                    <ControlTemplate TargetType="ItemsControl">
                        <Border Background="{DynamicResource BackgroundColor}">
                            <Grid>
                                <ItemsPresenter />
                                <TextBlock Text="لا توجد إشعارات" 
                                           HorizontalAlignment="Center" 
                                           VerticalAlignment="Center" 
                                           FontSize="18" 
                                           Foreground="{DynamicResource SecondaryTextColor}" 
                                           Visibility="{Binding HasNotifications, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </ItemsControl.Template>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>
