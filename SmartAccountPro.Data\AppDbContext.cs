using System;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Data
{
    /// <summary>
    /// سياق قاعدة البيانات للتطبيق
    /// </summary>
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
            // تفعيل التحميل الكسول للبيانات
            ChangeTracker.LazyLoadingEnabled = true;

            // تفعيل تتبع التغييرات التلقائي
            ChangeTracker.AutoDetectChangesEnabled = true;
        }

        /// <summary>
        /// تكوين سياق قاعدة البيانات
        /// </summary>
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);

            // تحسين الأداء عن طريق تعطيل تتبع التغييرات للاستعلامات التي تستخدم للقراءة فقط
            optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);

            // تفعيل التحميل المتعدد للبيانات
            optionsBuilder.EnableSensitiveDataLogging();

            // تفعيل تجميع الاستعلامات
            optionsBuilder.ConfigureWarnings(warnings => warnings.Ignore(CoreEventId.RowLimitingOperationWithoutOrderByWarning));
        }

        public DbSet<Account> Accounts { get; set; }
        public DbSet<JournalEntry> JournalEntries { get; set; }
        public DbSet<JournalEntryItem> JournalEntryItems { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<TaskItem> Tasks { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين العلاقات بين الجداول

            // علاقة الحساب الأب والحسابات الفرعية
            modelBuilder.Entity<Account>()
                .HasOne(a => a.ParentAccount)
                .WithMany(a => a.ChildAccounts)
                .HasForeignKey(a => a.ParentAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة القيد المحاسبي وبنوده
            modelBuilder.Entity<JournalEntryItem>()
                .HasOne(i => i.JournalEntry)
                .WithMany(j => j.Items)
                .HasForeignKey(i => i.JournalEntryId)
                .OnDelete(DeleteBehavior.Cascade);

            // علاقة القيد المحاسبي والحساب
            modelBuilder.Entity<JournalEntryItem>()
                .HasOne(i => i.Account)
                .WithMany()
                .HasForeignKey(i => i.AccountId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة القيد المحاسبي والمستخدم الذي أنشأه
            modelBuilder.Entity<JournalEntry>()
                .HasOne(j => j.CreatedByUser)
                .WithMany()
                .HasForeignKey(j => j.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة القيد المحاسبي والمستخدم الذي رحله
            modelBuilder.Entity<JournalEntry>()
                .HasOne(j => j.PostedByUser)
                .WithMany()
                .HasForeignKey(j => j.PostedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة المستخدم والأدوار
            modelBuilder.Entity<UserRole>()
                .HasKey(ur => new { ur.UserId, ur.RoleId });

            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.User)
                .WithMany(u => u.UserRoles)
                .HasForeignKey(ur => ur.UserId);

            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.Role)
                .WithMany(r => r.UserRoles)
                .HasForeignKey(ur => ur.RoleId);

            // علاقة الدور والصلاحيات
            modelBuilder.Entity<RolePermission>()
                .HasKey(rp => new { rp.RoleId, rp.PermissionId });

            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Role)
                .WithMany(r => r.RolePermissions)
                .HasForeignKey(rp => rp.RoleId);

            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Permission)
                .WithMany(p => p.RolePermissions)
                .HasForeignKey(rp => rp.PermissionId);

            // علاقة العميل والحساب
            modelBuilder.Entity<Customer>()
                .HasOne(c => c.Account)
                .WithMany()
                .HasForeignKey(c => c.AccountId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة المورد والحساب
            modelBuilder.Entity<Supplier>()
                .HasOne(s => s.Account)
                .WithMany()
                .HasForeignKey(s => s.AccountId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة المنتج والحسابات
            modelBuilder.Entity<Product>()
                .HasOne(p => p.SalesAccount)
                .WithMany()
                .HasForeignKey(p => p.SalesAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.PurchaseAccount)
                .WithMany()
                .HasForeignKey(p => p.PurchaseAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.InventoryAccount)
                .WithMany()
                .HasForeignKey(p => p.InventoryAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة الفاتورة والعميل
            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.Customer)
                .WithMany(c => c.Invoices)
                .HasForeignKey(i => i.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة الفاتورة والمورد
            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.Supplier)
                .WithMany(s => s.Invoices)
                .HasForeignKey(i => i.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة الفاتورة والقيد المحاسبي
            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.JournalEntry)
                .WithMany()
                .HasForeignKey(i => i.JournalEntryId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة الفاتورة والمستخدم الذي أنشأها
            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.CreatedByUser)
                .WithMany()
                .HasForeignKey(i => i.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة الفاتورة وبنودها
            modelBuilder.Entity<InvoiceItem>()
                .HasOne(i => i.Invoice)
                .WithMany(i => i.Items)
                .HasForeignKey(i => i.InvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            // علاقة بند الفاتورة والمنتج
            modelBuilder.Entity<InvoiceItem>()
                .HasOne(i => i.Product)
                .WithMany(p => p.InvoiceItems)
                .HasForeignKey(i => i.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقة الإشعار والمستخدم
            modelBuilder.Entity<Notification>()
                .HasOne<User>()
                .WithMany()
                .HasForeignKey(n => n.UserId)
                .OnDelete(DeleteBehavior.Cascade);


        }
    }
}
