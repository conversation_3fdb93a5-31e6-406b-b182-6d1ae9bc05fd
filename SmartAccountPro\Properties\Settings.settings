<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="SmartAccountPro.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="Theme" Type="System.String" Scope="User">
      <Value Profile="(Default)">Light</Value>
    </Setting>
    <Setting Name="Language" Type="System.String" Scope="User">
      <Value Profile="(Default)">ar-SA</Value>
    </Setting>
    <Setting Name="FontSize" Type="System.Double" Scope="User">
      <Value Profile="(Default)">12</Value>
    </Setting>
    <Setting Name="ShowSidebar" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="AutoBackup" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="BackupPath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="BackupInterval" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">7</Value>
    </Setting>
    <Setting Name="LastBackupDate" Type="System.DateTime" Scope="User">
      <Value Profile="(Default)">1900-01-01</Value>
    </Setting>
    <Setting Name="LastLoginDate" Type="System.DateTime" Scope="User">
      <Value Profile="(Default)">1900-01-01</Value>
    </Setting>
    <Setting Name="RememberUser" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="LastUsername" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
  </Settings>
</SettingsFile>
