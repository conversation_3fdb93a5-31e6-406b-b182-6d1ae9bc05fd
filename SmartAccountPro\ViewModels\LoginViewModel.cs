using System;
using System.Windows;
using System.Windows.Input;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// نموذج عرض تسجيل الدخول
    /// </summary>
    public class LoginViewModel : ViewModelBase
    {
        private string _username = string.Empty;
        private string _password = string.Empty;
        private string _errorMessage = string.Empty;
        private bool _rememberMe;
        private Dictionary<string, List<string>> _validationErrors = new Dictionary<string, List<string>>();

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string Username
        {
            get => _username;
            set => SetProperty(ref _username, value);
        }

        /// <summary>
        /// رسالة الخطأ
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// كلمة المرور
        /// </summary>
        public string Password
        {
            get => _password;
            set => SetProperty(ref _password, value);
        }

        /// <summary>
        /// تذكرني
        /// </summary>
        public bool RememberMe
        {
            get => _rememberMe;
            set => SetProperty(ref _rememberMe, value);
        }

        /// <summary>
        /// أمر تسجيل الدخول
        /// </summary>
        public ICommand LoginCommand { get; }

        public LoginViewModel()
        {
            LoginCommand = new RelayCommand(Login, CanLogin);
        }

        /// <summary>
        /// تسجيل الدخول
        /// </summary>
        private void Login(object parameter)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateForm())
                {
                    return;
                }

                // هنا يمكن إضافة كود للتحقق من صحة بيانات تسجيل الدخول
                // مثال بسيط للتوضيح فقط (يجب استبداله بالتحقق من قاعدة البيانات)
                if (Username == "admin" && Password == "admin")
                {
                    // تسجيل الدخول بنجاح
                    ErrorMessage = string.Empty;
                    _validationErrors.Clear();

                    // حفظ بيانات المستخدم إذا تم اختيار "تذكرني"
                    // تم تعطيل هذه الميزة مؤقتاً حتى يتم إضافة إعدادات التطبيق
                    /*
                    if (RememberMe)
                    {
                        // Properties.Settings.Default.RememberUsername = true;
                        // Properties.Settings.Default.Username = Username;
                        // Properties.Settings.Default.Save();
                    }
                    else
                    {
                        // Properties.Settings.Default.RememberUsername = false;
                        // Properties.Settings.Default.Username = string.Empty;
                        // Properties.Settings.Default.Save();
                    }
                    */

                    System.Windows.MessageBox.Show("تم تسجيل الدخول بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);

                    // إرسال حدث تسجيل الدخول الناجح
                    LoginSuccessful?.Invoke(this, EventArgs.Empty);
                }
                else
                {
                    // فشل تسجيل الدخول
                    ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة";
                    AddValidationError("Username", "اسم المستخدم أو كلمة المرور غير صحيحة");
                    AddValidationError("Password", "اسم المستخدم أو كلمة المرور غير صحيحة");
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"حدث خطأ: {ex.Message}";
                Helpers.ExceptionHandler.HandleException(ex, "LoginViewModel.Login");
            }
        }

        /// <summary>
        /// حدث تسجيل الدخول الناجح
        /// </summary>
        public event EventHandler? LoginSuccessful;

        /// <summary>
        /// الحصول على أخطاء التحقق لحقل معين
        /// </summary>
        public List<string> GetValidationErrors(string propertyName)
        {
            if (_validationErrors.ContainsKey(propertyName))
                return _validationErrors[propertyName];
            return new List<string>();
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private bool ValidateForm()
        {
            _validationErrors.Clear();

            // التحقق من اسم المستخدم
            if (string.IsNullOrWhiteSpace(Username))
            {
                AddValidationError("Username", "الرجاء إدخال اسم المستخدم");
            }
            else if (Username.Length < 3)
            {
                AddValidationError("Username", "اسم المستخدم يجب أن يكون 3 أحرف على الأقل");
            }

            // التحقق من كلمة المرور
            if (string.IsNullOrWhiteSpace(Password))
            {
                AddValidationError("Password", "الرجاء إدخال كلمة المرور");
            }
            else if (Password.Length < 4)
            {
                AddValidationError("Password", "كلمة المرور يجب أن تكون 4 أحرف على الأقل");
            }

            // تحديث رسالة الخطأ الرئيسية
            if (_validationErrors.Count > 0)
            {
                foreach (var errors in _validationErrors.Values)
                {
                    if (errors.Count > 0)
                    {
                        ErrorMessage = errors[0];
                        break;
                    }
                }
            }
            else
            {
                ErrorMessage = string.Empty;
            }

            return _validationErrors.Count == 0;
        }

        /// <summary>
        /// إضافة خطأ تحقق
        /// </summary>
        private void AddValidationError(string propertyName, string error)
        {
            if (!_validationErrors.ContainsKey(propertyName))
            {
                _validationErrors[propertyName] = new List<string>();
            }

            _validationErrors[propertyName].Add(error);
        }

        /// <summary>
        /// التحقق من إمكانية تسجيل الدخول
        /// </summary>
        private bool CanLogin(object parameter)
        {
            return !string.IsNullOrEmpty(Username) && !string.IsNullOrEmpty(Password);
        }

        /// <summary>
        /// تهيئة نموذج العرض
        /// </summary>
        public void Initialize()
        {
            // استرجاع اسم المستخدم المحفوظ إذا كان خيار "تذكرني" مفعل
            // تم تعطيل هذه الميزة مؤقتاً حتى يتم إضافة إعدادات التطبيق
            /*
            if (Properties.Settings.Default.RememberUsername)
            {
                Username = Properties.Settings.Default.Username;
                RememberMe = true;
            }
            */
        }
    }
}
