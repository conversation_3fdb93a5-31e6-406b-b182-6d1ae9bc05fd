﻿<Window x:Class="SmartAccountPro.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SmartAccountPro"
        xmlns:views="clr-namespace:SmartAccountPro.Views"
        mc:Ignorable="d"
        Title="SmartAccount Pro - نظام المحاسبة الذكي"
        Height="700"
        Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource BackgroundColor}">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- الشريط الجانبي -->
        <Border Grid.Column="0"
                Background="{DynamicResource SidebarBackgroundColor}"
                BorderBrush="{DynamicResource BorderColor}"
                BorderThickness="0,0,1,0"
                CornerRadius="0,0,0,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- شعار التطبيق -->
                <StackPanel Grid.Row="0" Margin="0,20,0,20" HorizontalAlignment="Center">
                    <Image Source="/Resources/Images/logo.png" Width="80" Height="80" Margin="0,0,0,10"/>
                    <TextBlock Text="SmartAccount Pro"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="{DynamicResource PrimaryTextColor}"
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <views:SidebarView x:Name="Sidebar" Grid.Row="1"/>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان -->
            <Grid Grid.Row="0"
                  Background="{DynamicResource HeaderBackgroundColor}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <ToggleButton Grid.Column="0"
                              x:Name="ToggleSidebarButton"
                              Click="ToggleSidebarButton_Click"
                              Width="40"
                              Height="40"
                              Margin="10,0,0,0"
                              Background="Transparent"
                              BorderThickness="0">
                    <TextBlock Text="&#xE700;"
                               FontFamily="Segoe MDL2 Assets"
                               FontSize="16"
                               Foreground="{DynamicResource HeaderTextColor}"/>
                    <ToggleButton.Template>
                        <ControlTemplate TargetType="ToggleButton">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="5">
                                <ContentPresenter HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </ToggleButton.Template>
                </ToggleButton>

                <TextBlock Grid.Column="1"
                           Text="SmartAccount Pro - نظام المحاسبة الذكي"
                           Foreground="{DynamicResource HeaderTextColor}"
                           FontSize="18"
                           FontWeight="Bold"
                           Margin="20,15"/>

                <StackPanel Grid.Column="2"
                            Orientation="Horizontal"
                            Margin="0,0,20,0">
                    <TextBlock x:Name="DateTimeText"
                               Foreground="{DynamicResource HeaderTextColor}"
                               VerticalAlignment="Center"
                               Margin="0,0,20,0"/>

                    <!-- زر الإشعارات -->
                    <Button x:Name="NotificationsButton"
                            Click="NotificationsButton_Click"
                            Width="40"
                            Height="40"
                            Background="Transparent"
                            BorderThickness="0"
                            Margin="0,0,10,0">
                        <Grid>
                            <TextBlock Text="&#xE7E7;"
                                       FontFamily="Segoe MDL2 Assets"
                                       FontSize="16"
                                       Foreground="{DynamicResource HeaderTextColor}"/>
                            <Border x:Name="NotificationBadge"
                                    Width="16"
                                    Height="16"
                                    Background="#FF4081"
                                    CornerRadius="8"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Top"
                                    Margin="0,-5,-5,0"
                                    Visibility="Collapsed">
                                <TextBlock x:Name="NotificationCount"
                                           Text="0"
                                           FontSize="10"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                            </Border>
                        </Grid>
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="5">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <Button x:Name="TestNotificationButton"
                            Click="TestNotificationButton_Click"
                            Width="40"
                            Height="40"
                            Background="Transparent"
                            BorderThickness="0"
                            Margin="0,0,10,0">
                        <TextBlock Text="&#xE789;"
                                   FontFamily="Segoe MDL2 Assets"
                                   FontSize="16"
                                   Foreground="{DynamicResource HeaderTextColor}"/>
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="5">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <Button x:Name="ThemeToggleButton"
                            Click="ThemeToggleButton_Click"
                            Width="40"
                            Height="40"
                            Background="Transparent"
                            BorderThickness="0"
                            Margin="0,0,10,0">
                        <TextBlock x:Name="ThemeIcon"
                                   Text="&#xE706;"
                                   FontFamily="Segoe MDL2 Assets"
                                   FontSize="16"
                                   Foreground="{DynamicResource HeaderTextColor}"/>
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="5">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>
                </StackPanel>
            </Grid>

            <!-- المحتوى الرئيسي -->
            <ContentControl Grid.Row="1"
                            x:Name="MainContent"
                            Margin="20"/>

            <!-- شريط الحالة -->
            <StatusBar Grid.Row="2"
                       Background="{DynamicResource CardBackgroundColor}">
                <StatusBarItem>
                    <TextBlock Text="جميع الحقوق محفوظة © 2023 SmartAccount Pro"
                               Foreground="{DynamicResource SecondaryTextColor}"/>
                </StatusBarItem>
                <Separator/>
                <StatusBarItem>
                    <TextBlock Text="الإصدار 1.0.0"
                               Foreground="{DynamicResource SecondaryTextColor}"/>
                </StatusBarItem>
                <StatusBarItem HorizontalAlignment="Right">
                    <TextBlock x:Name="StatusDateTimeText"
                               Foreground="{DynamicResource SecondaryTextColor}"/>
                </StatusBarItem>
            </StatusBar>
        </Grid>
    </Grid>
</Window>
