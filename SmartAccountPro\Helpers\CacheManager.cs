using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة ذاكرة التخزين المؤقت
    /// </summary>
    public class CacheManager<TKey, TValue> where TKey : notnull
    {
        private readonly ConcurrentDictionary<TKey, CacheItem<TValue>> _cache;
        private readonly Timer _cleanupTimer;
        private readonly TimeSpan _defaultExpiration;
        private readonly string _cacheName;
        private readonly int _maxItems;

        /// <summary>
        /// إنشاء نسخة جديدة من مدير ذاكرة التخزين المؤقت
        /// </summary>
        /// <param name="cacheName">اسم ذاكرة التخزين المؤقت</param>
        /// <param name="defaultExpiration">مدة الصلاحية الافتراضية</param>
        /// <param name="cleanupInterval">فترة التنظيف</param>
        /// <param name="maxItems">الحد الأقصى لعدد العناصر</param>
        public CacheManager(string cacheName, TimeSpan defaultExpiration, TimeSpan cleanupInterval, int maxItems = 1000)
        {
            _cache = new ConcurrentDictionary<TKey, CacheItem<TValue>>();
            _defaultExpiration = defaultExpiration;
            _cacheName = cacheName;
            _maxItems = maxItems;
            _cleanupTimer = new Timer(CleanupCallback, null, cleanupInterval, cleanupInterval);
        }

        /// <summary>
        /// إضافة عنصر إلى ذاكرة التخزين المؤقت
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="value">القيمة</param>
        /// <param name="expiration">مدة الصلاحية</param>
        /// <returns>نجاح العملية</returns>
        public bool Add(TKey key, TValue value, TimeSpan? expiration = null)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime($"CacheManager.Add.{_cacheName}", () =>
                {
                    // التحقق من عدد العناصر
                    if (_cache.Count >= _maxItems)
                    {
                        // إزالة أقدم عنصر
                        RemoveOldestItem();
                    }

                    // تحديد وقت انتهاء الصلاحية
                    var expirationTime = DateTime.Now.Add(expiration ?? _defaultExpiration);

                    // إضافة العنصر إلى ذاكرة التخزين المؤقت
                    var cacheItem = new CacheItem<TValue>(value, expirationTime);
                    return _cache.TryAdd(key, cacheItem);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, $"CacheManager.Add.{_cacheName}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على عنصر من ذاكرة التخزين المؤقت
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="value">القيمة</param>
        /// <returns>نجاح العملية</returns>
        public bool TryGetValue(TKey key, out TValue? value)
        {
            try
            {
                // التحقق من وجود العنصر
                if (_cache.TryGetValue(key, out var cacheItem))
                {
                    // التحقق من صلاحية العنصر
                    if (cacheItem.ExpirationTime > DateTime.Now)
                    {
                        // تحديث وقت الوصول
                        cacheItem.LastAccessTime = DateTime.Now;

                        // إرجاع القيمة
                        value = cacheItem.Value;
                        return true;
                    }
                    else
                    {
                        // إزالة العنصر منتهي الصلاحية
                        _cache.TryRemove(key, out _);
                    }
                }

                // العنصر غير موجود أو منتهي الصلاحية
                value = default;
                return false;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, $"CacheManager.TryGetValue.{_cacheName}");
                value = default;
                return false;
            }
        }

        /// <summary>
        /// إزالة عنصر من ذاكرة التخزين المؤقت
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <returns>نجاح العملية</returns>
        public bool Remove(TKey key)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime($"CacheManager.Remove.{_cacheName}", () =>
                {
                    // إزالة العنصر
                    return _cache.TryRemove(key, out _);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, $"CacheManager.Remove.{_cacheName}");
                return false;
            }
        }

        /// <summary>
        /// مسح ذاكرة التخزين المؤقت
        /// </summary>
        public void Clear()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime($"CacheManager.Clear.{_cacheName}", () =>
                {
                    // مسح ذاكرة التخزين المؤقت
                    _cache.Clear();
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, $"CacheManager.Clear.{_cacheName}");
            }
        }

        /// <summary>
        /// الحصول على عدد العناصر في ذاكرة التخزين المؤقت
        /// </summary>
        /// <returns>عدد العناصر</returns>
        public int Count()
        {
            try
            {
                return _cache.Count;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, $"CacheManager.Count.{_cacheName}");
                return 0;
            }
        }

        /// <summary>
        /// التحقق من وجود عنصر في ذاكرة التخزين المؤقت
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <returns>وجود العنصر</returns>
        public bool Contains(TKey key)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime($"CacheManager.Contains.{_cacheName}", () =>
                {
                    // التحقق من وجود العنصر
                    if (_cache.TryGetValue(key, out var cacheItem))
                    {
                        // التحقق من صلاحية العنصر
                        return cacheItem.ExpirationTime > DateTime.Now;
                    }

                    return false;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, $"CacheManager.Contains.{_cacheName}");
                return false;
            }
        }

        /// <summary>
        /// تنظيف ذاكرة التخزين المؤقت
        /// </summary>
        private void CleanupCallback(object? state)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime($"CacheManager.CleanupCallback.{_cacheName}", () =>
                {
                    // الحصول على الوقت الحالي
                    var now = DateTime.Now;

                    // البحث عن العناصر منتهية الصلاحية
                    var expiredItems = _cache.Where(kvp => kvp.Value.ExpirationTime <= now)
                                             .Select(kvp => kvp.Key)
                                             .ToList();

                    // إزالة العناصر منتهية الصلاحية
                    foreach (var key in expiredItems)
                    {
                        _cache.TryRemove(key, out _);
                    }

                    // تسجيل عدد العناصر التي تمت إزالتها
                    if (expiredItems.Count > 0)
                    {
                        Debug.WriteLine($"[CACHE] تم إزالة {expiredItems.Count} عنصر منتهي الصلاحية من ذاكرة التخزين المؤقت '{_cacheName}'.");
                    }

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, $"CacheManager.CleanupCallback.{_cacheName}");
            }
        }

        /// <summary>
        /// إزالة أقدم عنصر
        /// </summary>
        private void RemoveOldestItem()
        {
            try
            {
                // البحث عن أقدم عنصر
                var oldestItem = _cache.OrderBy(kvp => kvp.Value.LastAccessTime)
                                      .FirstOrDefault();

                // إزالة أقدم عنصر
                if (oldestItem.Key != null)
                {
                    _cache.TryRemove(oldestItem.Key, out _);
                    Debug.WriteLine($"[CACHE] تم إزالة أقدم عنصر من ذاكرة التخزين المؤقت '{_cacheName}'.");
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, $"CacheManager.RemoveOldestItem.{_cacheName}");
            }
        }

        /// <summary>
        /// التخلص من الموارد
        /// </summary>
        public void Dispose()
        {
            try
            {
                // إيقاف مؤقت التنظيف
                _cleanupTimer.Dispose();

                // مسح ذاكرة التخزين المؤقت
                _cache.Clear();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, $"CacheManager.Dispose.{_cacheName}");
            }
        }
    }

    /// <summary>
    /// عنصر ذاكرة التخزين المؤقت
    /// </summary>
    /// <typeparam name="T">نوع القيمة</typeparam>
    internal class CacheItem<T>
    {
        /// <summary>
        /// القيمة
        /// </summary>
        public T Value { get; }

        /// <summary>
        /// وقت انتهاء الصلاحية
        /// </summary>
        public DateTime ExpirationTime { get; }

        /// <summary>
        /// وقت آخر وصول
        /// </summary>
        public DateTime LastAccessTime { get; set; }

        /// <summary>
        /// إنشاء عنصر ذاكرة تخزين مؤقت جديد
        /// </summary>
        /// <param name="value">القيمة</param>
        /// <param name="expirationTime">وقت انتهاء الصلاحية</param>
        public CacheItem(T value, DateTime expirationTime)
        {
            Value = value;
            ExpirationTime = expirationTime;
            LastAccessTime = DateTime.Now;
        }
    }
}
