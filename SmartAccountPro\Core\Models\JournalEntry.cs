using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// نموذج القيد المحاسبي
    /// </summary>
    public class JournalEntry
    {
        /// <summary>
        /// معرف القيد
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// رقم القيد
        /// </summary>
        [Required]
        [StringLength(20)]
        public string EntryNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ القيد
        /// </summary>
        public DateTime EntryDate { get; set; } = DateTime.Now;

        /// <summary>
        /// الوصف
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// المرجع
        /// </summary>
        [StringLength(100)]
        public string? Reference { get; set; }

        /// <summary>
        /// إجمالي المبلغ
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// حالة القيد
        /// </summary>
        [StringLength(20)]
        public string Status { get; set; } = "Draft";

        /// <summary>
        /// معرف المستخدم المنشئ
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ التعديل
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// بنود القيد
        /// </summary>
        public virtual ICollection<JournalEntryItem> Items { get; set; } = new List<JournalEntryItem>();
    }

    /// <summary>
    /// نموذج بند القيد المحاسبي
    /// </summary>
    public class JournalEntryItem
    {
        /// <summary>
        /// معرف البند
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// معرف القيد
        /// </summary>
        public int JournalEntryId { get; set; }

        /// <summary>
        /// معرف الحساب
        /// </summary>
        public int AccountId { get; set; }

        /// <summary>
        /// المبلغ المدين
        /// </summary>
        public decimal DebitAmount { get; set; }

        /// <summary>
        /// المبلغ الدائن
        /// </summary>
        public decimal CreditAmount { get; set; }

        /// <summary>
        /// الوصف
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// القيد المحاسبي
        /// </summary>
        public virtual JournalEntry JournalEntry { get; set; } = null!;

        /// <summary>
        /// الحساب
        /// </summary>
        public virtual Account Account { get; set; } = null!;
    }
}
