using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// نموذج عرض الإشعارات
    /// </summary>
    public class NotificationsViewModel : ViewModelBase
    {
        private ObservableCollection<NotificationItemViewModel> _notifications;
        private bool _hasNotifications;

        /// <summary>
        /// قائمة الإشعارات
        /// </summary>
        public ObservableCollection<NotificationItemViewModel> Notifications
        {
            get => _notifications;
            set => SetProperty(ref _notifications, value);
        }

        /// <summary>
        /// هل توجد إشعارات
        /// </summary>
        public bool HasNotifications
        {
            get => _hasNotifications;
            set => SetProperty(ref _hasNotifications, value);
        }

        /// <summary>
        /// أمر تحديد إشعار كمقروء
        /// </summary>
        public ICommand MarkAsReadCommand { get; }

        /// <summary>
        /// أمر تحديد جميع الإشعارات كمقروءة
        /// </summary>
        public ICommand MarkAllAsReadCommand { get; }

        /// <summary>
        /// أمر حذف إشعار
        /// </summary>
        public ICommand DeleteNotificationCommand { get; }

        /// <summary>
        /// أمر حذف جميع الإشعارات المقروءة
        /// </summary>
        public ICommand DeleteReadCommand { get; }

        public NotificationsViewModel()
        {
            // تهيئة القائمة
            _notifications = new ObservableCollection<NotificationItemViewModel>();

            // تهيئة الأوامر
            MarkAsReadCommand = new RelayCommand<int>(MarkAsRead);
            MarkAllAsReadCommand = new RelayCommand(MarkAllAsRead);
            DeleteNotificationCommand = new RelayCommand<int>(DeleteNotification);
            DeleteReadCommand = new RelayCommand(DeleteReadNotifications);

            // الاشتراك في حدث تغيير الإشعارات
            NotificationManager.Instance.NotificationsChanged += OnNotificationsChanged;

            // تحميل الإشعارات
            LoadNotifications();
        }

        /// <summary>
        /// تحميل الإشعارات
        /// </summary>
        private void LoadNotifications()
        {
            try
            {
                Notifications.Clear();

                foreach (var notification in NotificationManager.Instance.Notifications)
                {
                    Notifications.Add(new NotificationItemViewModel(notification));
                }

                HasNotifications = Notifications.Count > 0;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationsViewModel.LoadNotifications");
            }
        }

        /// <summary>
        /// معالج حدث تغيير الإشعارات
        /// </summary>
        private void OnNotificationsChanged(object sender, EventArgs e)
        {
            LoadNotifications();
        }

        /// <summary>
        /// تحديد إشعار كمقروء
        /// </summary>
        private async void MarkAsRead(int notificationId)
        {
            try
            {
                await NotificationManager.Instance.MarkAsReadAsync(notificationId);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationsViewModel.MarkAsRead");
            }
        }

        /// <summary>
        /// تحديد جميع الإشعارات كمقروءة
        /// </summary>
        private async void MarkAllAsRead(object parameter)
        {
            try
            {
                await NotificationManager.Instance.MarkAllAsReadAsync();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationsViewModel.MarkAllAsRead");
            }
        }

        /// <summary>
        /// حذف إشعار
        /// </summary>
        private async void DeleteNotification(int notificationId)
        {
            try
            {
                await NotificationManager.Instance.DeleteNotificationAsync(notificationId);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationsViewModel.DeleteNotification");
            }
        }

        /// <summary>
        /// حذف جميع الإشعارات المقروءة
        /// </summary>
        private async void DeleteReadNotifications(object parameter)
        {
            try
            {
                await NotificationManager.Instance.DeleteReadNotificationsAsync();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationsViewModel.DeleteReadNotifications");
            }
        }
    }

    /// <summary>
    /// نموذج عرض عنصر الإشعار
    /// </summary>
    public class NotificationItemViewModel : ViewModelBase
    {
        private readonly Notification _notification;

        public NotificationItemViewModel(Notification notification)
        {
            _notification = notification;
        }

        /// <summary>
        /// معرف الإشعار
        /// </summary>
        public int Id => _notification.Id;

        /// <summary>
        /// عنوان الإشعار
        /// </summary>
        public string Title => _notification.Title;

        /// <summary>
        /// محتوى الإشعار
        /// </summary>
        public string Content => _notification.Content;

        /// <summary>
        /// تاريخ الإشعار
        /// </summary>
        public DateTime CreatedAt => _notification.CreatedAt;

        /// <summary>
        /// تاريخ الإشعار بتنسيق مناسب
        /// </summary>
        public string CreatedAtFormatted => FormatDateTime(_notification.CreatedAt);

        /// <summary>
        /// هل تمت قراءة الإشعار
        /// </summary>
        public bool IsRead => _notification.IsRead;

        /// <summary>
        /// ظهور زر تحديد كمقروء
        /// </summary>
        public Visibility IsReadVisibility => IsRead ? Visibility.Collapsed : Visibility.Visible;

        /// <summary>
        /// نوع الإشعار
        /// </summary>
        public NotificationType Type => _notification.Type;

        /// <summary>
        /// أيقونة نوع الإشعار
        /// </summary>
        public string TypeIcon
        {
            get
            {
                return Type switch
                {
                    NotificationType.Info => "\uE946",     // معلومات
                    NotificationType.Warning => "\uE7BA",  // تحذير
                    NotificationType.Error => "\uE783",    // خطأ
                    NotificationType.Success => "\uE930",  // نجاح
                    NotificationType.Reminder => "\uE823", // تذكير
                    NotificationType.Invoice => "\uE71D",  // فاتورة
                    NotificationType.Payment => "\uE8C7",  // دفعة
                    NotificationType.Task => "\uE8FB",     // مهمة
                    _ => "\uE946"                          // افتراضي
                };
            }
        }

        /// <summary>
        /// لون نوع الإشعار
        /// </summary>
        public Brush TypeColor
        {
            get
            {
                return Type switch
                {
                    NotificationType.Info => new SolidColorBrush(Color.FromRgb(33, 150, 243)),    // أزرق
                    NotificationType.Warning => new SolidColorBrush(Color.FromRgb(255, 152, 0)),  // برتقالي
                    NotificationType.Error => new SolidColorBrush(Color.FromRgb(244, 67, 54)),    // أحمر
                    NotificationType.Success => new SolidColorBrush(Color.FromRgb(76, 175, 80)),  // أخضر
                    NotificationType.Reminder => new SolidColorBrush(Color.FromRgb(156, 39, 176)),// بنفسجي
                    NotificationType.Invoice => new SolidColorBrush(Color.FromRgb(0, 188, 212)),  // سماوي
                    NotificationType.Payment => new SolidColorBrush(Color.FromRgb(255, 87, 34)),  // برتقالي محمر
                    NotificationType.Task => new SolidColorBrush(Color.FromRgb(63, 81, 181)),     // أزرق داكن
                    _ => new SolidColorBrush(Color.FromRgb(33, 150, 243))                         // افتراضي
                };
            }
        }

        /// <summary>
        /// تنسيق التاريخ والوقت
        /// </summary>
        private string FormatDateTime(DateTime dateTime)
        {
            var now = DateTime.Now;
            var diff = now - dateTime;

            if (diff.TotalMinutes < 1)
            {
                return "الآن";
            }
            else if (diff.TotalMinutes < 60)
            {
                return $"منذ {(int)diff.TotalMinutes} دقيقة";
            }
            else if (diff.TotalHours < 24)
            {
                return $"منذ {(int)diff.TotalHours} ساعة";
            }
            else if (diff.TotalDays < 30)
            {
                return $"منذ {(int)diff.TotalDays} يوم";
            }
            else
            {
                return dateTime.ToString("yyyy/MM/dd HH:mm");
            }
        }
    }
}
