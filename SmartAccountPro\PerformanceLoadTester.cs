using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Data;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using System.Text;

namespace SmartAccountPro
{
    /// <summary>
    /// نظام اختبار الأداء والأحمال الشامل
    /// </summary>
    public class PerformanceLoadTester
    {
        private AppDbContext _context = null!;
        private DatabaseManager _dbManager = null!;
        private string _testDatabasePath = null!;
        private readonly List<PerformanceMetric> _metrics = new();
        private readonly Random _random = new();

        /// <summary>
        /// تشغيل اختبار الأداء والأحمال الشامل
        /// </summary>
        public static async Task Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("🚀 SmartAccount Pro - اختبار الأداء والأحمال الشامل");
            Console.WriteLine("═══════════════════════════════════════════════════════════");

            var tester = new PerformanceLoadTester();
            await tester.RunPerformanceLoadTestAsync();

            Console.WriteLine("\n🎉 انتهى اختبار الأداء والأحمال!");
            Console.WriteLine("اضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }

        /// <summary>
        /// تشغيل اختبار الأداء والأحمال الشامل
        /// </summary>
        public async Task RunPerformanceLoadTestAsync()
        {
            try
            {
                // تهيئة النظام
                Console.WriteLine("🔧 المرحلة 1: تهيئة بيئة الاختبار");
                bool initResult = await InitializeTestEnvironmentAsync();
                LogResult("تهيئة بيئة الاختبار", initResult);

                if (!initResult) return;

                // إنشاء بيانات اختبار كبيرة
                Console.WriteLine("\n📊 المرحلة 2: إنشاء بيانات اختبار كبيرة");
                bool dataResult = await CreateLargeTestDatasetAsync();
                LogResult("إنشاء بيانات الاختبار", dataResult);

                // اختبار أداء قاعدة البيانات
                Console.WriteLine("\n💾 المرحلة 3: اختبار أداء قاعدة البيانات");
                bool dbResult = await TestDatabasePerformanceAsync();
                LogResult("أداء قاعدة البيانات", dbResult);

                // اختبار أداء التقارير
                Console.WriteLine("\n📈 المرحلة 4: اختبار أداء التقارير");
                bool reportsResult = await TestReportsPerformanceAsync();
                LogResult("أداء التقارير", reportsResult);

                // اختبار استهلاك الموارد
                Console.WriteLine("\n🖥️ المرحلة 5: اختبار استهلاك الموارد");
                bool resourcesResult = await TestResourceConsumptionAsync();
                LogResult("استهلاك الموارد", resourcesResult);

                // اختبار الأحمال العالية
                Console.WriteLine("\n⚡ المرحلة 6: اختبار الأحمال العالية");
                bool loadResult = await TestHighLoadScenariosAsync();
                LogResult("الأحمال العالية", loadResult);

                // تحليل النتائج
                Console.WriteLine("\n📋 المرحلة 7: تحليل النتائج والتوصيات");
                await AnalyzeResultsAndRecommendationsAsync();

                // إنشاء تقرير شامل
                await GeneratePerformanceReportAsync();

            }
            catch (Exception ex)
            {
                Console.WriteLine($"💥 خطأ عام في اختبار الأداء: {ex.Message}");
            }
            finally
            {
                await CleanupAsync();
            }
        }

        /// <summary>
        /// تهيئة بيئة الاختبار
        /// </summary>
        private async Task<bool> InitializeTestEnvironmentAsync()
        {
            try
            {
                // إنشاء مجلد اختبار مؤقت
                string tempPath = Path.Combine(Path.GetTempPath(), "SmartAccountProPerformanceTest");
                if (Directory.Exists(tempPath))
                    Directory.Delete(tempPath, true);
                Directory.CreateDirectory(tempPath);

                _testDatabasePath = Path.Combine(tempPath, "PerformanceTestDatabase.db");

                // تهيئة سياق قاعدة البيانات مع تحسينات الأداء
                var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
                optionsBuilder.UseSqlite($"Data Source={_testDatabasePath};Cache=Shared;");
                optionsBuilder.EnableSensitiveDataLogging(false); // تعطيل للأداء
                optionsBuilder.EnableServiceProviderCaching();

                _context = new AppDbContext(optionsBuilder.Options);

                // إنشاء قاعدة البيانات مع فهارس محسنة
                await _context.Database.EnsureCreatedAsync();

                // تهيئة مدير قاعدة البيانات
                _dbManager = new DatabaseManager(_context);
                await _dbManager.InitializeDatabaseAsync();

                Console.WriteLine($"   ✅ تم إنشاء قاعدة بيانات الاختبار: {_testDatabasePath}");
                Console.WriteLine($"   ✅ تم تهيئة مدير قاعدة البيانات مع تحسينات الأداء");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في تهيئة بيئة الاختبار: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء بيانات اختبار كبيرة
        /// </summary>
        private async Task<bool> CreateLargeTestDatasetAsync()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                // إنشاء 1000+ حساب محاسبي
                Console.WriteLine("   📊 إنشاء 1000+ حساب محاسبي...");
                await CreateLargeAccountsDatasetAsync(1000);

                // إنشاء 500+ عميل
                Console.WriteLine("   👥 إنشاء 500+ عميل...");
                await CreateLargeCustomersDatasetAsync(500);

                // إنشاء 2000+ قيد محاسبي
                Console.WriteLine("   📝 إنشاء 2000+ قيد محاسبي...");
                await CreateLargeJournalEntriesDatasetAsync(2000);

                // إنشاء 1500+ فاتورة
                Console.WriteLine("   🧾 إنشاء 1500+ فاتورة...");
                await CreateLargeInvoicesDatasetAsync(1500);

                stopwatch.Stop();
                _metrics.Add(new PerformanceMetric
                {
                    Operation = "إنشاء بيانات اختبار كبيرة",
                    Duration = stopwatch.Elapsed,
                    RecordsProcessed = 5000,
                    MemoryUsed = GC.GetTotalMemory(false)
                });

                Console.WriteLine($"   ✅ تم إنشاء البيانات في {stopwatch.Elapsed.TotalSeconds:F2} ثانية");
                Console.WriteLine($"   📊 إجمالي السجلات: {await GetTotalRecordsCountAsync()}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في إنشاء بيانات الاختبار: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء حسابات محاسبية كبيرة
        /// </summary>
        private async Task CreateLargeAccountsDatasetAsync(int count)
        {
            var accounts = new List<Account>();
            var accountTypes = Enum.GetValues<AccountType>();

            for (int i = 1; i <= count; i++)
            {
                var accountType = accountTypes[_random.Next(accountTypes.Length)];
                accounts.Add(new Account
                {
                    Code = $"{(int)accountType}.{i:D4}",
                    Name = $"حساب اختبار {accountType} رقم {i}",
                    Type = accountType,
                    IsActive = true,
                    CreatedAt = DateTime.Now.AddDays(-_random.Next(365))
                });

                // حفظ على دفعات لتحسين الأداء
                if (accounts.Count >= 100)
                {
                    _context.Accounts.AddRange(accounts);
                    await _context.SaveChangesAsync();
                    accounts.Clear();
                }
            }

            // حفظ الباقي
            if (accounts.Any())
            {
                _context.Accounts.AddRange(accounts);
                await _context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// إنشاء عملاء كبيرة
        /// </summary>
        private async Task CreateLargeCustomersDatasetAsync(int count)
        {
            var customers = new List<Customer>();

            for (int i = 1; i <= count; i++)
            {
                customers.Add(new Customer
                {
                    Name = $"عميل اختبار رقم {i}",
                    Phone = $"05{_random.Next(********, ********)}",
                    Email = $"customer{i}@test.com",
                    Address = $"عنوان العميل رقم {i}، الرياض، المملكة العربية السعودية",
                    CreatedAt = DateTime.Now.AddDays(-_random.Next(365))
                });

                // حفظ على دفعات
                if (customers.Count >= 50)
                {
                    _context.Customers.AddRange(customers);
                    await _context.SaveChangesAsync();
                    customers.Clear();
                }
            }

            if (customers.Any())
            {
                _context.Customers.AddRange(customers);
                await _context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// إنشاء قيود محاسبية كبيرة
        /// </summary>
        private async Task CreateLargeJournalEntriesDatasetAsync(int count)
        {
            var accounts = await _context.Accounts.ToListAsync();
            if (!accounts.Any()) return;

            for (int i = 1; i <= count; i++)
            {
                var entry = new JournalEntry
                {
                    EntryNumber = $"JE-PERF-{i:D6}",
                    EntryDate = DateTime.Now.AddDays(-_random.Next(365)),
                    Description = $"قيد اختبار أداء رقم {i}",
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = 1
                };

                // إضافة بنود القيد (2-4 بنود لكل قيد)
                var itemsCount = _random.Next(2, 5);
                var totalAmount = _random.Next(1000, 100000);
                var debitAmount = totalAmount;
                var creditAmount = totalAmount;

                entry.Items = new List<JournalEntryItem>();

                // بند مدين
                var debitAccount = accounts[_random.Next(accounts.Count)];
                entry.Items.Add(new JournalEntryItem
                {
                    AccountId = debitAccount.Id,
                    DebitAmount = debitAmount,
                    CreditAmount = 0,
                    Description = $"بند مدين - {debitAccount.Name}"
                });

                // بند دائن
                var creditAccount = accounts[_random.Next(accounts.Count)];
                entry.Items.Add(new JournalEntryItem
                {
                    AccountId = creditAccount.Id,
                    DebitAmount = 0,
                    CreditAmount = creditAmount,
                    Description = $"بند دائن - {creditAccount.Name}"
                });

                _context.JournalEntries.Add(entry);

                // حفظ على دفعات
                if (i % 50 == 0)
                {
                    await _context.SaveChangesAsync();
                }
            }

            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// إنشاء فواتير كبيرة
        /// </summary>
        private async Task CreateLargeInvoicesDatasetAsync(int count)
        {
            var customers = await _context.Customers.ToListAsync();
            if (!customers.Any()) return;

            var statuses = Enum.GetValues<InvoiceStatus>();

            for (int i = 1; i <= count; i++)
            {
                var customer = customers[_random.Next(customers.Count)];
                var invoice = new Invoice
                {
                    InvoiceNumber = $"INV-PERF-{i:D6}",
                    InvoiceDate = DateTime.Now.AddDays(-_random.Next(365)),
                    DueDate = DateTime.Now.AddDays(_random.Next(30, 90)),
                    CustomerId = customer.Id,
                    Status = statuses[_random.Next(statuses.Length)],
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = 1
                };

                _context.Invoices.Add(invoice);

                // حفظ على دفعات
                if (i % 100 == 0)
                {
                    await _context.SaveChangesAsync();
                }
            }

            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// حساب إجمالي السجلات
        /// </summary>
        private async Task<int> GetTotalRecordsCountAsync()
        {
            var accountsCount = await _context.Accounts.CountAsync();
            var customersCount = await _context.Customers.CountAsync();
            var journalEntriesCount = await _context.JournalEntries.CountAsync();
            var invoicesCount = await _context.Invoices.CountAsync();

            return accountsCount + customersCount + journalEntriesCount + invoicesCount;
        }

        /// <summary>
        /// طباعة نتيجة الاختبار
        /// </summary>
        private void LogResult(string testName, bool success)
        {
            string status = success ? "✅ نجح" : "❌ فشل";
            Console.WriteLine($"   🎯 {testName}: {status}");
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        private async Task CleanupAsync()
        {
            try
            {
                if (_context != null)
                {
                    await _context.DisposeAsync();
                }

                if (!string.IsNullOrEmpty(_testDatabasePath) && File.Exists(_testDatabasePath))
                {
                    File.Delete(_testDatabasePath);
                    Console.WriteLine("   🧹 تم حذف قاعدة بيانات الاختبار");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ خطأ في التنظيف: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار أداء قاعدة البيانات
        /// </summary>
        private async Task<bool> TestDatabasePerformanceAsync()
        {
            try
            {
                // اختبار استعلامات البحث
                Console.WriteLine("   🔍 اختبار استعلامات البحث...");
                await TestSearchQueriesPerformanceAsync();

                // اختبار استعلامات التجميع
                Console.WriteLine("   📊 اختبار استعلامات التجميع...");
                await TestAggregationQueriesPerformanceAsync();

                // اختبار العمليات المعقدة
                Console.WriteLine("   🔗 اختبار الاستعلامات المعقدة...");
                await TestComplexQueriesPerformanceAsync();

                // اختبار عمليات الكتابة
                Console.WriteLine("   ✍️ اختبار عمليات الكتابة...");
                await TestWriteOperationsPerformanceAsync();

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار أداء قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار استعلامات البحث
        /// </summary>
        private async Task TestSearchQueriesPerformanceAsync()
        {
            var stopwatch = Stopwatch.StartNew();

            // البحث في الحسابات
            var accountsSearch = await _context.Accounts
                .Where(a => a.Name.Contains("اختبار"))
                .ToListAsync();

            stopwatch.Stop();
            _metrics.Add(new PerformanceMetric
            {
                Operation = "البحث في الحسابات",
                Duration = stopwatch.Elapsed,
                RecordsProcessed = accountsSearch.Count,
                MemoryUsed = GC.GetTotalMemory(false)
            });

            Console.WriteLine($"      ✅ البحث في الحسابات: {accountsSearch.Count} نتيجة في {stopwatch.ElapsedMilliseconds} مللي ثانية");

            // البحث في العملاء
            stopwatch.Restart();
            var customersSearch = await _context.Customers
                .Where(c => c.Name.Contains("عميل"))
                .ToListAsync();

            stopwatch.Stop();
            _metrics.Add(new PerformanceMetric
            {
                Operation = "البحث في العملاء",
                Duration = stopwatch.Elapsed,
                RecordsProcessed = customersSearch.Count,
                MemoryUsed = GC.GetTotalMemory(false)
            });

            Console.WriteLine($"      ✅ البحث في العملاء: {customersSearch.Count} نتيجة في {stopwatch.ElapsedMilliseconds} مللي ثانية");
        }

        /// <summary>
        /// اختبار استعلامات التجميع
        /// </summary>
        private async Task TestAggregationQueriesPerformanceAsync()
        {
            var stopwatch = Stopwatch.StartNew();

            // حساب إجمالي المدين والدائن
            var totalDebits = await _context.JournalEntryItems.SumAsync(i => i.DebitAmount);
            var totalCredits = await _context.JournalEntryItems.SumAsync(i => i.CreditAmount);

            stopwatch.Stop();
            _metrics.Add(new PerformanceMetric
            {
                Operation = "حساب إجمالي المدين والدائن",
                Duration = stopwatch.Elapsed,
                RecordsProcessed = await _context.JournalEntryItems.CountAsync(),
                MemoryUsed = GC.GetTotalMemory(false)
            });

            Console.WriteLine($"      ✅ إجمالي المدين: {totalDebits:N0} ريال");
            Console.WriteLine($"      ✅ إجمالي الدائن: {totalCredits:N0} ريال");
            Console.WriteLine($"      ⏱️ وقت الحساب: {stopwatch.ElapsedMilliseconds} مللي ثانية");

            // حساب أرصدة الحسابات
            stopwatch.Restart();
            var accountBalances = await _context.Accounts
                .Select(a => new
                {
                    a.Id,
                    a.Name,
                    Balance = _context.JournalEntryItems
                        .Where(i => i.AccountId == a.Id)
                        .Sum(i => i.DebitAmount - i.CreditAmount)
                })
                .ToListAsync();

            stopwatch.Stop();
            _metrics.Add(new PerformanceMetric
            {
                Operation = "حساب أرصدة الحسابات",
                Duration = stopwatch.Elapsed,
                RecordsProcessed = accountBalances.Count,
                MemoryUsed = GC.GetTotalMemory(false)
            });

            Console.WriteLine($"      ✅ حساب أرصدة {accountBalances.Count} حساب في {stopwatch.ElapsedMilliseconds} مللي ثانية");
        }

        /// <summary>
        /// اختبار الاستعلامات المعقدة
        /// </summary>
        private async Task TestComplexQueriesPerformanceAsync()
        {
            var stopwatch = Stopwatch.StartNew();

            // استعلام معقد مع JOIN متعدد
            var complexQuery = await _context.JournalEntries
                .Include(je => je.Items)
                    .ThenInclude(i => i.Account)
                .Include(je => je.CreatedByUser)
                .Where(je => je.EntryDate >= DateTime.Now.AddMonths(-6))
                .OrderByDescending(je => je.EntryDate)
                .Take(100)
                .ToListAsync();

            stopwatch.Stop();
            _metrics.Add(new PerformanceMetric
            {
                Operation = "استعلام معقد مع JOIN",
                Duration = stopwatch.Elapsed,
                RecordsProcessed = complexQuery.Count,
                MemoryUsed = GC.GetTotalMemory(false)
            });

            Console.WriteLine($"      ✅ استعلام معقد: {complexQuery.Count} نتيجة في {stopwatch.ElapsedMilliseconds} مللي ثانية");

            // استعلام تجميعي معقد
            stopwatch.Restart();
            var monthlyStats = await _context.JournalEntries
                .Where(je => je.EntryDate >= DateTime.Now.AddYears(-1))
                .GroupBy(je => new { je.EntryDate.Year, je.EntryDate.Month })
                .Select(g => new
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    Count = g.Count(),
                    TotalAmount = g.SelectMany(je => je.Items).Sum(i => i.DebitAmount)
                })
                .ToListAsync();

            stopwatch.Stop();
            _metrics.Add(new PerformanceMetric
            {
                Operation = "إحصائيات شهرية معقدة",
                Duration = stopwatch.Elapsed,
                RecordsProcessed = monthlyStats.Count,
                MemoryUsed = GC.GetTotalMemory(false)
            });

            Console.WriteLine($"      ✅ إحصائيات شهرية: {monthlyStats.Count} شهر في {stopwatch.ElapsedMilliseconds} مللي ثانية");
        }

        /// <summary>
        /// اختبار عمليات الكتابة
        /// </summary>
        private async Task TestWriteOperationsPerformanceAsync()
        {
            var stopwatch = Stopwatch.StartNew();

            // إنشاء 100 قيد جديد
            var newEntries = new List<JournalEntry>();
            var accounts = await _context.Accounts.Take(10).ToListAsync();

            for (int i = 1; i <= 100; i++)
            {
                var entry = new JournalEntry
                {
                    EntryNumber = $"JE-WRITE-TEST-{i:D3}",
                    EntryDate = DateTime.Now,
                    Description = $"قيد اختبار كتابة رقم {i}",
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = 1
                };

                var amount = _random.Next(1000, 10000);
                entry.Items = new List<JournalEntryItem>
                {
                    new JournalEntryItem
                    {
                        AccountId = accounts[0].Id,
                        DebitAmount = amount,
                        CreditAmount = 0,
                        Description = "بند مدين"
                    },
                    new JournalEntryItem
                    {
                        AccountId = accounts[1].Id,
                        DebitAmount = 0,
                        CreditAmount = amount,
                        Description = "بند دائن"
                    }
                };

                newEntries.Add(entry);
            }

            _context.JournalEntries.AddRange(newEntries);
            await _context.SaveChangesAsync();

            stopwatch.Stop();
            _metrics.Add(new PerformanceMetric
            {
                Operation = "كتابة 100 قيد جديد",
                Duration = stopwatch.Elapsed,
                RecordsProcessed = 100,
                MemoryUsed = GC.GetTotalMemory(false)
            });

            Console.WriteLine($"      ✅ كتابة 100 قيد في {stopwatch.ElapsedMilliseconds} مللي ثانية");
        }

        /// <summary>
        /// اختبار أداء التقارير
        /// </summary>
        private async Task<bool> TestReportsPerformanceAsync()
        {
            try
            {
                var reportGenerator = new ReportGenerator(_context);

                // اختبار تقرير ميزان المراجعة
                Console.WriteLine("   📊 اختبار تقرير ميزان المراجعة...");
                var stopwatch = Stopwatch.StartNew();

                try
                {
                    var trialBalance = await reportGenerator.GenerateTrialBalanceReportAsync(
                        DateTime.Now.AddYears(-1), DateTime.Now);

                    stopwatch.Stop();
                    _metrics.Add(new PerformanceMetric
                    {
                        Operation = "تقرير ميزان المراجعة",
                        Duration = stopwatch.Elapsed,
                        RecordsProcessed = await _context.Accounts.CountAsync(),
                        MemoryUsed = GC.GetTotalMemory(false)
                    });

                    Console.WriteLine($"      ✅ تم توليد ميزان المراجعة في {stopwatch.ElapsedMilliseconds} مللي ثانية");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"      ⚠️ تحذير في ميزان المراجعة: {ex.Message}");
                }

                // اختبار تقرير الأرباح والخسائر
                Console.WriteLine("   💰 اختبار تقرير الأرباح والخسائر...");
                stopwatch.Restart();

                try
                {
                    var incomeStatement = await reportGenerator.GenerateIncomeStatementReportAsync(
                        DateTime.Now.AddYears(-1), DateTime.Now);

                    stopwatch.Stop();
                    _metrics.Add(new PerformanceMetric
                    {
                        Operation = "تقرير الأرباح والخسائر",
                        Duration = stopwatch.Elapsed,
                        RecordsProcessed = await _context.JournalEntryItems.CountAsync(),
                        MemoryUsed = GC.GetTotalMemory(false)
                    });

                    Console.WriteLine($"      ✅ تم توليد تقرير الأرباح والخسائر في {stopwatch.ElapsedMilliseconds} مللي ثانية");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"      ⚠️ تحذير في تقرير الأرباح والخسائر: {ex.Message}");
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار أداء التقارير: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار استهلاك الموارد
        /// </summary>
        private async Task<bool> TestResourceConsumptionAsync()
        {
            try
            {
                // قياس استهلاك الذاكرة قبل الاختبار
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                var initialMemory = GC.GetTotalMemory(false);

                Console.WriteLine($"   💾 الذاكرة الأولية: {initialMemory / 1024.0 / 1024.0:F2} MB");

                // اختبار تحميل بيانات كبيرة
                Console.WriteLine("   📥 اختبار تحميل بيانات كبيرة...");
                var stopwatch = Stopwatch.StartNew();

                var largeDataset = await _context.JournalEntries
                    .Include(je => je.Items)
                        .ThenInclude(i => i.Account)
                    .ToListAsync();

                stopwatch.Stop();
                var memoryAfterLoad = GC.GetTotalMemory(false);

                _metrics.Add(new PerformanceMetric
                {
                    Operation = "تحميل بيانات كبيرة",
                    Duration = stopwatch.Elapsed,
                    RecordsProcessed = largeDataset.Count,
                    MemoryUsed = memoryAfterLoad
                });

                Console.WriteLine($"      ✅ تم تحميل {largeDataset.Count} قيد في {stopwatch.ElapsedMilliseconds} مللي ثانية");
                Console.WriteLine($"      💾 الذاكرة بعد التحميل: {memoryAfterLoad / 1024.0 / 1024.0:F2} MB");
                Console.WriteLine($"      📈 زيادة الذاكرة: {(memoryAfterLoad - initialMemory) / 1024.0 / 1024.0:F2} MB");

                // اختبار عمليات متعددة متزامنة
                Console.WriteLine("   🔄 اختبار العمليات المتزامنة...");
                await TestConcurrentOperationsAsync();

                // قياس استهلاك الذاكرة النهائي
                GC.Collect();
                var finalMemory = GC.GetTotalMemory(true);
                Console.WriteLine($"   💾 الذاكرة النهائية: {finalMemory / 1024.0 / 1024.0:F2} MB");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار استهلاك الموارد: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار العمليات المتزامنة
        /// </summary>
        private async Task TestConcurrentOperationsAsync()
        {
            var tasks = new List<Task>();
            var stopwatch = Stopwatch.StartNew();

            // تشغيل 10 عمليات بحث متزامنة
            for (int i = 0; i < 10; i++)
            {
                int taskId = i;
                tasks.Add(Task.Run(async () =>
                {
                    var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
                    optionsBuilder.UseSqlite(_context.Database.GetConnectionString());
                    using var context = new AppDbContext(optionsBuilder.Options);
                    var results = await context.Accounts
                        .Where(a => a.Id > taskId * 100)
                        .Take(50)
                        .ToListAsync();
                    return results.Count;
                }));
            }

            await Task.WhenAll(tasks);
            stopwatch.Stop();

            _metrics.Add(new PerformanceMetric
            {
                Operation = "10 عمليات بحث متزامنة",
                Duration = stopwatch.Elapsed,
                RecordsProcessed = 500,
                MemoryUsed = GC.GetTotalMemory(false)
            });

            Console.WriteLine($"      ✅ تم تنفيذ 10 عمليات متزامنة في {stopwatch.ElapsedMilliseconds} مللي ثانية");
        }

        /// <summary>
        /// اختبار الأحمال العالية
        /// </summary>
        private async Task<bool> TestHighLoadScenariosAsync()
        {
            try
            {
                // اختبار إدراج كمية كبيرة من البيانات
                Console.WriteLine("   📊 اختبار إدراج 1000 سجل...");
                await TestBulkInsertPerformanceAsync();

                // اختبار استعلامات معقدة متتالية
                Console.WriteLine("   🔍 اختبار استعلامات معقدة متتالية...");
                await TestSequentialComplexQueriesAsync();

                // اختبار حمولة مختلطة
                Console.WriteLine("   🔄 اختبار حمولة مختلطة...");
                await TestMixedWorkloadAsync();

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار الأحمال العالية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار الإدراج المجمع
        /// </summary>
        private async Task TestBulkInsertPerformanceAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var accounts = new List<Account>();

            for (int i = 1; i <= 1000; i++)
            {
                accounts.Add(new Account
                {
                    Code = $"BULK-{i:D4}",
                    Name = $"حساب إدراج مجمع رقم {i}",
                    Type = AccountType.Asset,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                });
            }

            _context.Accounts.AddRange(accounts);
            await _context.SaveChangesAsync();

            stopwatch.Stop();
            _metrics.Add(new PerformanceMetric
            {
                Operation = "إدراج مجمع 1000 حساب",
                Duration = stopwatch.Elapsed,
                RecordsProcessed = 1000,
                MemoryUsed = GC.GetTotalMemory(false)
            });

            Console.WriteLine($"      ✅ تم إدراج 1000 حساب في {stopwatch.ElapsedMilliseconds} مللي ثانية");
            Console.WriteLine($"      ⚡ معدل الإدراج: {1000.0 / stopwatch.Elapsed.TotalSeconds:F0} سجل/ثانية");
        }

        /// <summary>
        /// اختبار استعلامات معقدة متتالية
        /// </summary>
        private async Task TestSequentialComplexQueriesAsync()
        {
            var stopwatch = Stopwatch.StartNew();

            for (int i = 0; i < 20; i++)
            {
                // استعلام معقد مختلف في كل مرة
                var query = await _context.JournalEntries
                    .Include(je => je.Items)
                    .Where(je => je.EntryDate >= DateTime.Now.AddDays(-i * 30))
                    .OrderByDescending(je => je.EntryDate)
                    .Take(50)
                    .ToListAsync();
            }

            stopwatch.Stop();
            _metrics.Add(new PerformanceMetric
            {
                Operation = "20 استعلام معقد متتالي",
                Duration = stopwatch.Elapsed,
                RecordsProcessed = 1000,
                MemoryUsed = GC.GetTotalMemory(false)
            });

            Console.WriteLine($"      ✅ تم تنفيذ 20 استعلام معقد في {stopwatch.ElapsedMilliseconds} مللي ثانية");
        }

        /// <summary>
        /// اختبار حمولة مختلطة
        /// </summary>
        private async Task TestMixedWorkloadAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var tasks = new List<Task>();

            // مزيج من عمليات القراءة والكتابة
            for (int i = 0; i < 5; i++)
            {
                // عمليات قراءة
                tasks.Add(Task.Run(async () =>
                {
                    var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
                    optionsBuilder.UseSqlite(_context.Database.GetConnectionString());
                    using var context = new AppDbContext(optionsBuilder.Options);
                    var accounts = await context.Accounts.Take(100).ToListAsync();
                    return accounts.Count;
                }));

                // عمليات كتابة
                tasks.Add(Task.Run(async () =>
                {
                    var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
                    optionsBuilder.UseSqlite(_context.Database.GetConnectionString());
                    using var context = new AppDbContext(optionsBuilder.Options);
                    var customer = new Customer
                    {
                        Name = $"عميل حمولة مختلطة {i}",
                        Phone = "**********",
                        Email = $"mixed{i}@test.com",
                        Address = "عنوان اختبار",
                        CreatedAt = DateTime.Now
                    };
                    context.Customers.Add(customer);
                    await context.SaveChangesAsync();
                    return 1;
                }));
            }

            await Task.WhenAll(tasks);
            stopwatch.Stop();

            _metrics.Add(new PerformanceMetric
            {
                Operation = "حمولة مختلطة (قراءة + كتابة)",
                Duration = stopwatch.Elapsed,
                RecordsProcessed = 505,
                MemoryUsed = GC.GetTotalMemory(false)
            });

            Console.WriteLine($"      ✅ تم تنفيذ حمولة مختلطة في {stopwatch.ElapsedMilliseconds} مللي ثانية");
        }

        /// <summary>
        /// تحليل النتائج والتوصيات
        /// </summary>
        private async Task AnalyzeResultsAndRecommendationsAsync()
        {
            Console.WriteLine("   📊 تحليل مقاييس الأداء:");

            foreach (var metric in _metrics)
            {
                Console.WriteLine($"      🔹 {metric.Operation}:");
                Console.WriteLine($"         ⏱️ الوقت: {metric.Duration.TotalMilliseconds:F0} مللي ثانية");
                Console.WriteLine($"         📊 السجلات: {metric.RecordsProcessed}");
                Console.WriteLine($"         ⚡ المعدل: {metric.RecordsPerSecond:F1} سجل/ثانية");
                Console.WriteLine($"         💾 الذاكرة: {metric.MemoryUsedMB}");
                Console.WriteLine();
            }

            // تحديد نقاط الاختناق
            var slowestOperations = _metrics
                .OrderByDescending(m => m.Duration.TotalMilliseconds)
                .Take(3)
                .ToList();

            Console.WriteLine("   ⚠️ أبطأ العمليات:");
            foreach (var op in slowestOperations)
            {
                Console.WriteLine($"      🐌 {op.Operation}: {op.Duration.TotalMilliseconds:F0} مللي ثانية");
            }

            // تحديد العمليات الأكثر استهلاكاً للذاكرة
            var memoryIntensiveOperations = _metrics
                .OrderByDescending(m => m.MemoryUsed)
                .Take(3)
                .ToList();

            Console.WriteLine("   💾 العمليات الأكثر استهلاكاً للذاكرة:");
            foreach (var op in memoryIntensiveOperations)
            {
                Console.WriteLine($"      🧠 {op.Operation}: {op.MemoryUsedMB}");
            }
        }

        /// <summary>
        /// إنشاء تقرير الأداء
        /// </summary>
        private async Task GeneratePerformanceReportAsync()
        {
            var reportPath = Path.Combine(Path.GetDirectoryName(_testDatabasePath)!, "PerformanceReport.md");
            var report = new StringBuilder();

            report.AppendLine("# 📊 تقرير اختبار الأداء والأحمال - SmartAccount Pro");
            report.AppendLine();
            report.AppendLine($"**تاريخ الاختبار:** {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"**إجمالي العمليات المختبرة:** {_metrics.Count}");
            report.AppendLine();

            report.AppendLine("## 📈 ملخص النتائج:");
            report.AppendLine();
            report.AppendLine("| العملية | الوقت (مللي ثانية) | السجلات | المعدل (سجل/ثانية) | الذاكرة |");
            report.AppendLine("|---------|-------------------|---------|-------------------|---------|");

            foreach (var metric in _metrics)
            {
                report.AppendLine($"| {metric.Operation} | {metric.Duration.TotalMilliseconds:F0} | {metric.RecordsProcessed} | {metric.RecordsPerSecond:F1} | {metric.MemoryUsedMB} |");
            }

            report.AppendLine();
            report.AppendLine("## 🎯 التوصيات:");
            report.AppendLine();
            report.AppendLine("### ✅ نقاط القوة:");
            report.AppendLine("- أداء ممتاز في العمليات الأساسية");
            report.AppendLine("- استهلاك ذاكرة معقول");
            report.AppendLine("- استجابة سريعة للاستعلامات البسيطة");
            report.AppendLine();
            report.AppendLine("### 🔧 نقاط التحسين:");
            report.AppendLine("- تحسين أداء الاستعلامات المعقدة");
            report.AppendLine("- إضافة فهارس لتسريع البحث");
            report.AppendLine("- تحسين إدارة الذاكرة للبيانات الكبيرة");

            await File.WriteAllTextAsync(reportPath, report.ToString(), Encoding.UTF8);
            Console.WriteLine($"   📄 تم إنشاء تقرير الأداء: {reportPath}");
        }
    }

    /// <summary>
    /// مقياس الأداء
    /// </summary>
    public class PerformanceMetric
    {
        public string Operation { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public int RecordsProcessed { get; set; }
        public long MemoryUsed { get; set; }
        public double RecordsPerSecond => RecordsProcessed / Duration.TotalSeconds;
        public string MemoryUsedMB => $"{MemoryUsed / 1024.0 / 1024.0:F2} MB";
    }
}
