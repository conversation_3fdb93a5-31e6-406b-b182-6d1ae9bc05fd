using System;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// نموذج الإشعارات
    /// </summary>
    public class Notification
    {
        /// <summary>
        /// معرف الإشعار
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// عنوان الإشعار
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// محتوى الإشعار
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// تاريخ الإشعار
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// تاريخ قراءة الإشعار
        /// </summary>
        public DateTime? ReadAt { get; set; }

        /// <summary>
        /// نوع الإشعار
        /// </summary>
        public NotificationType Type { get; set; }

        /// <summary>
        /// معرف المستخدم المستلم
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// معرف الكيان المرتبط بالإشعار (مثل معرف الفاتورة)
        /// </summary>
        public int? EntityId { get; set; }

        /// <summary>
        /// نوع الكيان المرتبط بالإشعار
        /// </summary>
        public string EntityType { get; set; }

        /// <summary>
        /// رابط الإشعار
        /// </summary>
        public string Link { get; set; }

        /// <summary>
        /// هل تمت قراءة الإشعار
        /// </summary>
        public bool IsRead => ReadAt.HasValue;

        /// <summary>
        /// هل الإشعار مهم
        /// </summary>
        public bool IsImportant { get; set; }
    }

    /// <summary>
    /// أنواع الإشعارات
    /// </summary>
    public enum NotificationType
    {
        /// <summary>
        /// معلومات
        /// </summary>
        Info,

        /// <summary>
        /// تحذير
        /// </summary>
        Warning,

        /// <summary>
        /// خطأ
        /// </summary>
        Error,

        /// <summary>
        /// نجاح
        /// </summary>
        Success,

        /// <summary>
        /// تذكير
        /// </summary>
        Reminder,

        /// <summary>
        /// فاتورة
        /// </summary>
        Invoice,

        /// <summary>
        /// دفعة
        /// </summary>
        Payment,

        /// <summary>
        /// مهمة
        /// </summary>
        Task
    }
}
