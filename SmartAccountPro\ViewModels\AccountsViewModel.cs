using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// نموذج عرض الحسابات
    /// </summary>
    public class AccountsViewModel : ViewModelBase
    {
        private Account _selectedAccount;
        private ObservableCollection<Account> _accounts;
        private ObservableCollection<AccountLedgerItem> _accountLedger;
        private bool _isLoading;

        /// <summary>
        /// الحساب المحدد
        /// </summary>
        public Account SelectedAccount
        {
            get => _selectedAccount;
            set
            {
                if (SetProperty(ref _selectedAccount, value))
                {
                    LoadAccountLedger();
                }
            }
        }

        /// <summary>
        /// قائمة الحسابات
        /// </summary>
        public ObservableCollection<Account> Accounts
        {
            get => _accounts;
            set => SetProperty(ref _accounts, value);
        }

        /// <summary>
        /// حركة الحساب
        /// </summary>
        public ObservableCollection<AccountLedgerItem> AccountLedger
        {
            get => _accountLedger;
            set => SetProperty(ref _accountLedger, value);
        }

        /// <summary>
        /// مؤشر التحميل
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// أمر إضافة حساب
        /// </summary>
        public ICommand AddAccountCommand { get; }

        /// <summary>
        /// أمر تعديل حساب
        /// </summary>
        public ICommand EditAccountCommand { get; }

        /// <summary>
        /// أمر حذف حساب
        /// </summary>
        public ICommand DeleteAccountCommand { get; }

        /// <summary>
        /// أمر تحديث
        /// </summary>
        public ICommand RefreshCommand { get; }

        /// <summary>
        /// أمر طباعة
        /// </summary>
        public ICommand PrintCommand { get; }

        public AccountsViewModel()
        {
            // تهيئة المجموعات
            Accounts = new ObservableCollection<Account>();
            AccountLedger = new ObservableCollection<AccountLedgerItem>();
            _selectedAccount = null!;

            // تهيئة الأوامر
            AddAccountCommand = new RelayCommand(AddAccount);
            EditAccountCommand = new RelayCommand(EditAccount, CanEditAccount);
            DeleteAccountCommand = new RelayCommand(DeleteAccount, CanDeleteAccount);
            RefreshCommand = new RelayCommand(Refresh);
            PrintCommand = new RelayCommand(Print);

            // تحميل الحسابات
            LoadAccountsAsync();
        }

        /// <summary>
        /// تحميل الحسابات
        /// </summary>
        private async void LoadAccountsAsync()
        {
            try
            {
                IsLoading = true;

                // قياس وقت تنفيذ العملية
                await PerformanceHelper.MeasureExecutionTimeAsync("LoadAccounts", async () =>
                {
                    // محاكاة تأخير الشبكة
                    await Task.Delay(500);

                    // هنا يمكن إضافة كود لتحميل الحسابات من قاعدة البيانات
                    // مثال بسيط للتوضيح فقط
                    Accounts.Clear();
                    Accounts.Add(new Account { Id = 1, Code = "1", Name = "الأصول", Type = AccountType.Asset });
                    Accounts.Add(new Account { Id = 2, Code = "2", Name = "الخصوم", Type = AccountType.Liability });
                    Accounts.Add(new Account { Id = 3, Code = "3", Name = "حقوق الملكية", Type = AccountType.Equity });
                    Accounts.Add(new Account { Id = 4, Code = "4", Name = "الإيرادات", Type = AccountType.Revenue });
                    Accounts.Add(new Account { Id = 5, Code = "5", Name = "المصروفات", Type = AccountType.Expense });
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الحسابات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                ExceptionHandler.HandleException(ex, "AccountsViewModel.LoadAccountsAsync");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// تحميل حركة الحساب
        /// </summary>
        private async void LoadAccountLedger()
        {
            try
            {
                if (SelectedAccount == null)
                {
                    AccountLedger.Clear();
                    return;
                }

                IsLoading = true;

                // قياس وقت تنفيذ العملية
                await PerformanceHelper.MeasureExecutionTimeAsync("LoadAccountLedger", async () =>
                {
                    // محاكاة تأخير الشبكة
                    await Task.Delay(300);

                    // هنا يمكن إضافة كود لتحميل حركة الحساب من قاعدة البيانات
                    // مثال بسيط للتوضيح فقط
                    AccountLedger.Clear();
                    AccountLedger.Add(new AccountLedgerItem { Date = DateTime.Now.AddDays(-5), EntryNumber = "JE-00001", Description = "قيد افتتاحي", Debit = 1000, Credit = 0, Balance = 1000 });
                    AccountLedger.Add(new AccountLedgerItem { Date = DateTime.Now.AddDays(-3), EntryNumber = "JE-00002", Description = "فاتورة مبيعات", Debit = 500, Credit = 0, Balance = 1500 });
                    AccountLedger.Add(new AccountLedgerItem { Date = DateTime.Now.AddDays(-1), EntryNumber = "JE-00003", Description = "سند صرف", Debit = 0, Credit = 300, Balance = 1200 });
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل حركة الحساب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                ExceptionHandler.HandleException(ex, "AccountsViewModel.LoadAccountLedger");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// إضافة حساب
        /// </summary>
        private void AddAccount(object parameter)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة حساب جديد", "إضافة حساب", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// تعديل حساب
        /// </summary>
        private void EditAccount(object parameter)
        {
            MessageBox.Show($"سيتم فتح نافذة تعديل الحساب: {SelectedAccount.Name}", "تعديل حساب", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// التحقق من إمكانية تعديل حساب
        /// </summary>
        private bool CanEditAccount(object parameter)
        {
            return SelectedAccount != null;
        }

        /// <summary>
        /// حذف حساب
        /// </summary>
        private void DeleteAccount(object parameter)
        {
            MessageBoxResult result = MessageBox.Show(
                $"هل أنت متأكد من حذف الحساب: {SelectedAccount.Name}؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("تم حذف الحساب بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                Accounts.Remove(SelectedAccount);
                SelectedAccount = null;
            }
        }

        /// <summary>
        /// التحقق من إمكانية حذف حساب
        /// </summary>
        private bool CanDeleteAccount(object parameter)
        {
            return SelectedAccount != null;
        }

        /// <summary>
        /// تحديث
        /// </summary>
        private void Refresh(object parameter)
        {
            LoadAccountsAsync();
        }

        /// <summary>
        /// طباعة
        /// </summary>
        private void Print(object parameter)
        {
            MessageBox.Show("سيتم طباعة شجرة الحسابات", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    /// <summary>
    /// عنصر في حركة الحساب
    /// </summary>
    public class AccountLedgerItem
    {
        public DateTime Date { get; set; }
        public string EntryNumber { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Debit { get; set; }
        public decimal Credit { get; set; }
        public decimal Balance { get; set; }
    }
}
