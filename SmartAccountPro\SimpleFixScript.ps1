# SmartAccount Pro - Simple Fix Script
# سكريبت إصلاح مبسط

param(
    [string]$ProjectPath = "."
)

Write-Host "🔧 SmartAccount Pro - Simple Fix Script" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# 1. Create missing directories
Write-Host "`n📁 Creating missing directories..." -ForegroundColor Yellow

$requiredDirectories = @(
    "$ProjectPath\Core",
    "$ProjectPath\Core\Models",
    "$ProjectPath\Core\Services", 
    "$ProjectPath\Core\Interfaces",
    "$ProjectPath\Data",
    "$ProjectPath\Data\Entities",
    "$ProjectPath\Data\Repositories",
    "$ProjectPath\ViewModels",
    "$ProjectPath\Views",
    "$ProjectPath\Views\Dialogs",
    "$ProjectPath\Views\Reports",
    "$ProjectPath\Converters",
    "$ProjectPath\Resources",
    "$ProjectPath\Resources\Images",
    "$ProjectPath\Resources\Styles"
)

foreach ($dir in $requiredDirectories) {
    if (-not (Test-Path $dir)) {
        try {
            New-Item -Path $dir -ItemType Directory -Force | Out-Null
            Write-Host "   ✅ Created: $dir" -ForegroundColor Green
        }
        catch {
            Write-Host "   ❌ Failed to create: $dir" -ForegroundColor Red
        }
    }
    else {
        Write-Host "   ℹ️ Already exists: $dir" -ForegroundColor Cyan
    }
}

# 2. Fix PowerShell script issues
Write-Host "`n🔧 Fixing PowerShell script issues..." -ForegroundColor Yellow

$psScriptPath = "$ProjectPath\deployment\scripts\DeploymentTestFramework.ps1"
if (Test-Path $psScriptPath) {
    try {
        # Create backup
        $backupPath = "$psScriptPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        Copy-Item $psScriptPath $backupPath -Force
        Write-Host "   ✅ Backup created: $backupPath" -ForegroundColor Green
        
        # Read content
        $content = Get-Content $psScriptPath -Raw -Encoding UTF8
        
        # Fix Arabic text with colons
        $content = $content -replace 'Write-Log "دقة الشاشة \$resolution: متوافقة"', 'Write-Log "دقة الشاشة $resolution متوافقة"'
        $content = $content -replace 'Write-Log "دقة الشاشة \$resolution: غير متوافقة', 'Write-Log "دقة الشاشة $resolution غير متوافقة'
        $content = $content -replace 'Write-Log "اللغة \$language: متوافقة"', 'Write-Log "اللغة $language متوافقة"'
        $content = $content -replace 'Write-Log "اللغة \$language: غير متوافقة', 'Write-Log "اللغة $language غير متوافقة'
        $content = $content -replace 'Write-Log "برنامج مكافحة الفيروسات \$antivirus: متوافق"', 'Write-Log "برنامج مكافحة الفيروسات $antivirus متوافق"'
        $content = $content -replace 'Write-Log "برنامج مكافحة الفيروسات \$antivirus: غير متوافق"', 'Write-Log "برنامج مكافحة الفيروسات $antivirus غير متوافق"'
        
        # Fix function names to follow PowerShell conventions
        $content = $content -replace 'function Load-TestConfiguration', 'function Get-TestConfiguration'
        $content = $content -replace 'Load-TestConfiguration', 'Get-TestConfiguration'
        $content = $content -replace 'function Run-EnvironmentTests', 'function Start-EnvironmentTests'
        $content = $content -replace 'Run-EnvironmentTests', 'Start-EnvironmentTests'
        $content = $content -replace 'function Generate-HTMLReport', 'function New-HTMLReport'
        $content = $content -replace 'Generate-HTMLReport', 'New-HTMLReport'
        $content = $content -replace 'function Generate-MarkdownReport', 'function New-MarkdownReport'
        $content = $content -replace 'Generate-MarkdownReport', 'New-MarkdownReport'
        
        # Fix switch parameters with default values
        $content = $content -replace '\[switch\]\$TestAllInstallers = \$true,', '[switch]$TestAllInstallers,'
        $content = $content -replace '\[switch\]\$TestPerformance = \$true,', '[switch]$TestPerformance,'
        $content = $content -replace '\[switch\]\$TestCompatibility = \$true,', '[switch]$TestCompatibility,'
        $content = $content -replace '\[switch\]\$GenerateReports = \$true,', '[switch]$GenerateReports,'
        
        # Add default value assignments after param block
        $paramBlockEnd = $content.IndexOf(')')
        if ($paramBlockEnd -gt 0) {
            $beforeParam = $content.Substring(0, $paramBlockEnd + 1)
            $afterParam = $content.Substring($paramBlockEnd + 1)
            
            $defaultAssignments = @"

# Set default values for switch parameters
if (-not `$PSBoundParameters.ContainsKey('TestAllInstallers')) { `$TestAllInstallers = `$true }
if (-not `$PSBoundParameters.ContainsKey('TestPerformance')) { `$TestPerformance = `$true }
if (-not `$PSBoundParameters.ContainsKey('TestCompatibility')) { `$TestCompatibility = `$true }
if (-not `$PSBoundParameters.ContainsKey('GenerateReports')) { `$GenerateReports = `$true }
"@
            
            $content = $beforeParam + $defaultAssignments + $afterParam
        }
        
        # Save fixed content
        $content | Out-File -FilePath $psScriptPath -Encoding UTF8
        Write-Host "   ✅ PowerShell script fixed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "   ❌ Failed to fix PowerShell script: $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host "   ⚠️ PowerShell script not found: $psScriptPath" -ForegroundColor Yellow
}

# 3. Create missing AppDbContext.cs
Write-Host "`n📄 Creating missing AppDbContext.cs..." -ForegroundColor Yellow

$appDbContextPath = "$ProjectPath\Data\AppDbContext.cs"
if (-not (Test-Path $appDbContextPath)) {
    $appDbContextContent = @"
using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        // DbSets for main entities
        public DbSet<User> Users { get; set; } = null!;
        public DbSet<Account> Accounts { get; set; } = null!;
        public DbSet<JournalEntry> JournalEntries { get; set; } = null!;
        public DbSet<JournalEntryItem> JournalEntryItems { get; set; } = null!;
        public DbSet<Invoice> Invoices { get; set; } = null!;
        public DbSet<InvoiceItem> InvoiceItems { get; set; } = null!;
        public DbSet<Customer> Customers { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // Configure entity relationships and constraints here
            // This will be expanded as needed
        }
    }
}
"@
    try {
        $appDbContextContent | Out-File -FilePath $appDbContextPath -Encoding UTF8
        Write-Host "   ✅ AppDbContext.cs created successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "   ❌ Failed to create AppDbContext.cs: $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host "   ℹ️ AppDbContext.cs already exists" -ForegroundColor Cyan
}

# 4. Fix C# warnings in BackgroundTaskManager.cs
Write-Host "`n🔧 Fixing C# warnings..." -ForegroundColor Yellow

$bgTaskManagerPath = "$ProjectPath\Helpers\BackgroundTaskManager.cs"
if (Test-Path $bgTaskManagerPath) {
    try {
        # Create backup
        $backupPath = "$bgTaskManagerPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        Copy-Item $bgTaskManagerPath $backupPath -Force
        
        $content = Get-Content $bgTaskManagerPath -Raw -Encoding UTF8
        
        # Remove unnecessary using statements
        $content = $content -replace 'using System\.Text;\r?\n', ''
        $content = $content -replace 'using System\.Windows;\r?\n', ''
        
        # Fix new expressions (simplified approach)
        $content = $content -replace 'new ConcurrentDictionary<string, BackgroundTask>\(\)', 'new()'
        $content = $content -replace 'new ConcurrentDictionary<string, CancellationTokenSource>\(\)', 'new()'
        $content = $content -replace 'new SemaphoreSlim\(5, 5\)', 'new(5, 5)'
        
        $content | Out-File -FilePath $bgTaskManagerPath -Encoding UTF8
        Write-Host "   ✅ C# warnings fixed in BackgroundTaskManager.cs" -ForegroundColor Green
    }
    catch {
        Write-Host "   ❌ Failed to fix C# warnings: $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host "   ℹ️ BackgroundTaskManager.cs not found" -ForegroundColor Cyan
}

# 5. Create .editorconfig file
Write-Host "`n📄 Creating .editorconfig file..." -ForegroundColor Yellow

$editorConfigPath = "$ProjectPath\.editorconfig"
if (-not (Test-Path $editorConfigPath)) {
    $editorConfigContent = @"
root = true

[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
indent_style = space
indent_size = 4

[*.cs]
# C# formatting rules
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true

[*.ps1]
# PowerShell formatting rules
indent_size = 4

[*.json]
# JSON formatting rules
indent_size = 2

[*.md]
# Markdown formatting rules
trim_trailing_whitespace = false
"@
    try {
        $editorConfigContent | Out-File -FilePath $editorConfigPath -Encoding UTF8
        Write-Host "   ✅ .editorconfig created successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "   ❌ Failed to create .editorconfig: $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host "   ℹ️ .editorconfig already exists" -ForegroundColor Cyan
}

Write-Host "`n🎉 Simple fix script completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Run 'dotnet build' to test the project" -ForegroundColor Cyan
Write-Host "2. Review the fixed files" -ForegroundColor Cyan
Write-Host "3. Test the PowerShell script" -ForegroundColor Cyan
