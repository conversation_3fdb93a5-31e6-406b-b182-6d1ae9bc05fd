using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// يمثل منتج في النظام
    /// </summary>
    public class Product
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(20)]
        public string Code { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// وحدة القياس
        /// </summary>
        [MaxLength(20)]
        public string Unit { get; set; }

        /// <summary>
        /// سعر البيع
        /// </summary>
        public decimal SalesPrice { get; set; }

        /// <summary>
        /// سعر الشراء
        /// </summary>
        public decimal PurchasePrice { get; set; }

        /// <summary>
        /// نسبة الضريبة
        /// </summary>
        public decimal TaxPercentage { get; set; }

        /// <summary>
        /// الكمية المتوفرة في المخزون
        /// </summary>
        public decimal QuantityInStock { get; set; }

        /// <summary>
        /// الحد الأدنى للمخزون
        /// </summary>
        public decimal MinimumQuantity { get; set; }

        /// <summary>
        /// حساب المبيعات في شجرة الحسابات
        /// </summary>
        public int? SalesAccountId { get; set; }
        public virtual Account SalesAccount { get; set; }

        /// <summary>
        /// حساب المشتريات في شجرة الحسابات
        /// </summary>
        public int? PurchaseAccountId { get; set; }
        public virtual Account PurchaseAccount { get; set; }

        /// <summary>
        /// حساب المخزون في شجرة الحسابات
        /// </summary>
        public int? InventoryAccountId { get; set; }
        public virtual Account InventoryAccount { get; set; }

        /// <summary>
        /// هل المنتج نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل المنتج قابل للبيع
        /// </summary>
        public bool IsSellable { get; set; } = true;

        /// <summary>
        /// هل المنتج قابل للشراء
        /// </summary>
        public bool IsPurchasable { get; set; } = true;

        /// <summary>
        /// هل المنتج يتبع المخزون
        /// </summary>
        public bool IsInventoryItem { get; set; } = true;

        /// <summary>
        /// تاريخ إنشاء المنتج
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث للمنتج
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// بنود الفواتير المرتبطة بهذا المنتج
        /// </summary>
        public virtual ICollection<InvoiceItem> InvoiceItems { get; set; }

        public Product()
        {
            InvoiceItems = new HashSet<InvoiceItem>();
        }
    }
}
