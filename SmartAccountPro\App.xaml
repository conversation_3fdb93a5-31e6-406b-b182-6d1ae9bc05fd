﻿<Application x:Class="SmartAccountPro.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:SmartAccountPro"
             xmlns:converters="clr-namespace:SmartAccountPro.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="/Styles/ThemeColors.xaml"/>
                <ResourceDictionary Source="/Styles/SidebarStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- محولات القيم -->
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
            <converters:InvoicesTotalConverter x:Key="InvoicesTotalConverter"/>

            <!-- تأثيرات -->
            <DropShadowEffect x:Key="ShadowEffect" ShadowDepth="2" BlurRadius="5" Opacity="0.3" Color="#000000"/>

            <!-- ألوان التطبيق -->
            <SolidColorBrush x:Key="PrimaryColor" Color="#2196F3"/>
            <SolidColorBrush x:Key="PrimaryDarkColor" Color="#1976D2"/>
            <SolidColorBrush x:Key="AccentColor" Color="#FF4081"/>
            <SolidColorBrush x:Key="TextColor" Color="#212121"/>
            <SolidColorBrush x:Key="SecondaryTextColor" Color="#757575"/>
            <SolidColorBrush x:Key="BorderColor" Color="#BDBDBD"/>
            <SolidColorBrush x:Key="BackgroundColor" Color="#F5F5F5"/>
            <SolidColorBrush x:Key="CardBackgroundColor" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="SidebarBackgroundColor" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="SidebarHoverColor" Color="#E3F2FD"/>
            <SolidColorBrush x:Key="SidebarSelectedColor" Color="#BBDEFB"/>
            <SolidColorBrush x:Key="GreenColor" Color="#4CAF50"/>
            <SolidColorBrush x:Key="RedColor" Color="#F44336"/>

            <!-- أنماط الأزرار -->
            <Style x:Key="ModernButton" TargetType="Button">
                <Setter Property="Background" Value="{DynamicResource PrimaryColor}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="15,8"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="8">
                                <Border.Effect>
                                    <DropShadowEffect ShadowDepth="1" BlurRadius="4" Opacity="0.2" Color="#000000"/>
                                </Border.Effect>
                                <ContentPresenter HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"
                                                  Margin="{TemplateBinding Padding}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource PrimaryDarkColor}"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource PrimaryDarkColor}"/>
                                    <Setter Property="Opacity" Value="0.8"/>
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Opacity" Value="0.5"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="GreenButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
                <Setter Property="Background" Value="{DynamicResource GreenColor}"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#388E3C"/>
                    </Trigger>
                    <Trigger Property="IsPressed" Value="True">
                        <Setter Property="Background" Value="#388E3C"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="RedButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
                <Setter Property="Background" Value="{DynamicResource RedColor}"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#D32F2F"/>
                    </Trigger>
                    <Trigger Property="IsPressed" Value="True">
                        <Setter Property="Background" Value="#D32F2F"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- نمط أزرار الشريط الجانبي -->
            <Style x:Key="SidebarButton" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="15,12"/>
                <Setter Property="Margin" Value="0,4"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="10">
                                <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                  Margin="{TemplateBinding Padding}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{StaticResource SidebarHoverColor}"/>
                                    <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="{StaticResource SidebarSelectedColor}"/>
                                    <Setter Property="Foreground" Value="{StaticResource PrimaryDarkColor}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- أنماط مربعات النص -->
            <Style x:Key="DefaultTextBox" TargetType="TextBox">
                <Setter Property="Padding" Value="5"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Background" Value="White"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TextBox">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="3">
                                <ScrollViewer x:Name="PART_ContentHost"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsFocused" Value="True">
                                    <Setter Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
