using System;
using System.ComponentModel.DataAnnotations;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// يمثل إعدادات التطبيق
    /// </summary>
    public class Settings
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Key { get; set; }

        [Required]
        public string Value { get; set; }

        [MaxLength(255)]
        public string Description { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
        
        // إعدادات الشركة
        public string CompanyName { get; set; } = "شركة المحاسبة الذكية";
        public string CompanyAddress { get; set; } = "";
        public string CompanyPhone { get; set; } = "";
        public string CompanyEmail { get; set; } = "";
        public string CompanyLogo { get; set; } = "";
        
        // إعدادات العملة
        public string DefaultCurrency { get; set; } = "SAR";
        public string CurrencySymbol { get; set; } = "ر.س";
        public int DecimalPlaces { get; set; } = 2;
        
        // إعدادات الأمان
        public int PasswordMinLength { get; set; } = 8;
        public bool RequireUppercase { get; set; } = true;
        public bool RequireLowercase { get; set; } = true;
        public bool RequireNumbers { get; set; } = true;
        public bool RequireSpecialChars { get; set; } = false;
        public int SessionTimeoutMinutes { get; set; } = 30;
        public int MaxLoginAttempts { get; set; } = 5;
        public int LockoutDurationMinutes { get; set; } = 15;
        
        // إعدادات النسخ الاحتياطي
        public bool AutoBackupEnabled { get; set; } = true;
        public int BackupIntervalHours { get; set; } = 24;
        public string BackupPath { get; set; } = "";
        public int MaxBackupFiles { get; set; } = 30;
        
        // إعدادات التقارير
        public string DefaultReportFormat { get; set; } = "PDF";
        public bool ShowCompanyLogo { get; set; } = true;
        public bool ShowReportDate { get; set; } = true;
        public string ReportHeaderColor { get; set; } = "#2196F3";
        
        // إعدادات الواجهة
        public string Theme { get; set; } = "Light";
        public string Language { get; set; } = "ar";
        public string DateFormat { get; set; } = "dd/MM/yyyy";
        public string TimeFormat { get; set; } = "HH:mm";
        
        // إعدادات الإشعارات
        public bool EmailNotificationsEnabled { get; set; } = true;
        public bool DesktopNotificationsEnabled { get; set; } = true;
        public bool SoundNotificationsEnabled { get; set; } = true;
        
        // إعدادات التدقيق
        public bool AuditTrailEnabled { get; set; } = true;
        public int AuditRetentionDays { get; set; } = 365;
        
        // إعدادات الطباعة
        public string DefaultPrinter { get; set; } = "";
        public string PaperSize { get; set; } = "A4";
        public bool PrintInColor { get; set; } = true;
        
        // إعدادات قاعدة البيانات
        public string DatabasePath { get; set; } = "";
        public bool AutoVacuum { get; set; } = true;
        public int VacuumIntervalDays { get; set; } = 7;
        
        // إعدادات التصدير
        public string DefaultExportPath { get; set; } = "";
        public string DefaultExportFormat { get; set; } = "Excel";
        
        // إعدادات الشبكة
        public string ProxyServer { get; set; } = "";
        public int ProxyPort { get; set; } = 8080;
        public bool UseProxy { get; set; } = false;
        
        // إعدادات التحديث
        public bool AutoUpdateEnabled { get; set; } = true;
        public string UpdateServer { get; set; } = "";
        public int UpdateCheckIntervalHours { get; set; } = 24;
        
        // إعدادات الأداء
        public int CacheSize { get; set; } = 100;
        public int QueryTimeout { get; set; } = 30;
        public bool EnableLogging { get; set; } = true;
        public string LogLevel { get; set; } = "Information";
        
        // إعدادات الفواتير
        public string InvoicePrefix { get; set; } = "INV";
        public int InvoiceNumberLength { get; set; } = 6;
        public bool AutoGenerateInvoiceNumber { get; set; } = true;
        
        // إعدادات القيود
        public string JournalPrefix { get; set; } = "JE";
        public int JournalNumberLength { get; set; } = 6;
        public bool AutoGenerateJournalNumber { get; set; } = true;
        
        // إعدادات العملاء
        public string CustomerPrefix { get; set; } = "CUST";
        public int CustomerCodeLength { get; set; } = 6;
        
        // إعدادات الموردين
        public string SupplierPrefix { get; set; } = "SUPP";
        public int SupplierCodeLength { get; set; } = 6;

        // إعدادات إضافية مطلوبة
        public string Culture { get; set; } = "ar-SA";
        public string CustomThemeName { get; set; } = "Default";
    }
}
