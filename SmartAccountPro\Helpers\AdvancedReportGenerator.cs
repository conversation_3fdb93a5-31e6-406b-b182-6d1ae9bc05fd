using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Data;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لإنشاء تقارير مالية متقدمة
    /// </summary>
    public class AdvancedReportGenerator
    {
        private readonly AppDbContext _context;
        private readonly ExportManager _exportManager;

        /// <summary>
        /// إنشاء مولد التقارير المتقدم
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public AdvancedReportGenerator(AppDbContext context)
        {
            _context = context;
            _exportManager = new ExportManager();
        }

        /// <summary>
        /// إنشاء تقرير الميزانية العمومية
        /// </summary>
        /// <param name="asOfDate">تاريخ الميزانية</param>
        /// <returns>تقرير الميزانية العمومية</returns>
        public async Task<BalanceSheetReport> GenerateBalanceSheetAsync(DateTime asOfDate)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("AdvancedReportGenerator.GenerateBalanceSheet", async () =>
                {
                    var report = new BalanceSheetReport
                    {
                        AsOfDate = asOfDate,
                        GeneratedAt = DateTime.Now
                    };

                    // الحصول على أرصدة الحسابات
                    var accountBalances = await GetAccountBalancesAsync(asOfDate);

                    // تصنيف الحسابات
                    report.Assets = accountBalances.Where(a => a.AccountType == AccountType.Asset).ToList();
                    report.Liabilities = accountBalances.Where(a => a.AccountType == AccountType.Liability).ToList();
                    report.Equity = accountBalances.Where(a => a.AccountType == AccountType.Equity).ToList();

                    // حساب الإجماليات
                    report.TotalAssets = report.Assets.Sum(a => a.Balance);
                    report.TotalLiabilities = report.Liabilities.Sum(a => a.Balance);
                    report.TotalEquity = report.Equity.Sum(a => a.Balance);

                    Debug.WriteLine($"[REPORT] تم إنشاء تقرير الميزانية العمومية لتاريخ {asOfDate:yyyy-MM-dd}");
                    return report;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AdvancedReportGenerator.GenerateBalanceSheet");
                throw;
            }
        }

        /// <summary>
        /// إنشاء تقرير قائمة الدخل
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>تقرير قائمة الدخل</returns>
        public async Task<IncomeStatementReport> GenerateIncomeStatementAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("AdvancedReportGenerator.GenerateIncomeStatement", async () =>
                {
                    var report = new IncomeStatementReport
                    {
                        FromDate = fromDate,
                        ToDate = toDate,
                        GeneratedAt = DateTime.Now
                    };

                    // الحصول على حركات الحسابات للفترة
                    var accountMovements = await GetAccountMovementsAsync(fromDate, toDate);

                    // تصنيف الحسابات
                    report.Revenues = accountMovements.Where(a => a.AccountType == AccountType.Revenue).ToList();
                    report.Expenses = accountMovements.Where(a => a.AccountType == AccountType.Expense).ToList();

                    // حساب الإجماليات
                    report.TotalRevenues = report.Revenues.Sum(a => a.CreditAmount - a.DebitAmount);
                    report.TotalExpenses = report.Expenses.Sum(a => a.DebitAmount - a.CreditAmount);
                    report.NetIncome = report.TotalRevenues - report.TotalExpenses;

                    Debug.WriteLine($"[REPORT] تم إنشاء تقرير قائمة الدخل للفترة من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");
                    return report;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AdvancedReportGenerator.GenerateIncomeStatement");
                throw;
            }
        }

        /// <summary>
        /// إنشاء تقرير التدفق النقدي
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>تقرير التدفق النقدي</returns>
        public async Task<CashFlowReport> GenerateCashFlowAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("AdvancedReportGenerator.GenerateCashFlow", async () =>
                {
                    var report = new CashFlowReport
                    {
                        FromDate = fromDate,
                        ToDate = toDate,
                        GeneratedAt = DateTime.Now
                    };

                    // الحصول على حسابات النقدية
                    var cashAccounts = await _context.Accounts
                        .Where(a => a.AccountType == AccountType.Asset && a.Name.Contains("نقدية"))
                        .ToListAsync();

                    // حساب الرصيد الافتتاحي
                    report.OpeningBalance = await GetCashBalanceAsync(cashAccounts, fromDate.AddDays(-1));

                    // الحصول على حركات النقدية
                    var cashMovements = await GetCashMovementsAsync(cashAccounts, fromDate, toDate);

                    // تصنيف التدفقات
                    report.OperatingActivities = cashMovements.Where(m => IsOperatingActivity(m)).ToList();
                    report.InvestingActivities = cashMovements.Where(m => IsInvestingActivity(m)).ToList();
                    report.FinancingActivities = cashMovements.Where(m => IsFinancingActivity(m)).ToList();

                    // حساب صافي التدفقات
                    report.NetOperatingCashFlow = report.OperatingActivities.Sum(a => a.NetAmount);
                    report.NetInvestingCashFlow = report.InvestingActivities.Sum(a => a.NetAmount);
                    report.NetFinancingCashFlow = report.FinancingActivities.Sum(a => a.NetAmount);

                    // حساب الرصيد الختامي
                    report.ClosingBalance = report.OpeningBalance + report.NetOperatingCashFlow + 
                                          report.NetInvestingCashFlow + report.NetFinancingCashFlow;

                    Debug.WriteLine($"[REPORT] تم إنشاء تقرير التدفق النقدي للفترة من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");
                    return report;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AdvancedReportGenerator.GenerateCashFlow");
                throw;
            }
        }

        /// <summary>
        /// إنشاء تقرير مقارن
        /// </summary>
        /// <param name="currentPeriodStart">بداية الفترة الحالية</param>
        /// <param name="currentPeriodEnd">نهاية الفترة الحالية</param>
        /// <param name="previousPeriodStart">بداية الفترة السابقة</param>
        /// <param name="previousPeriodEnd">نهاية الفترة السابقة</param>
        /// <returns>تقرير مقارن</returns>
        public async Task<ComparativeReport> GenerateComparativeReportAsync(
            DateTime currentPeriodStart, DateTime currentPeriodEnd,
            DateTime previousPeriodStart, DateTime previousPeriodEnd)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("AdvancedReportGenerator.GenerateComparativeReport", async () =>
                {
                    var report = new ComparativeReport
                    {
                        CurrentPeriodStart = currentPeriodStart,
                        CurrentPeriodEnd = currentPeriodEnd,
                        PreviousPeriodStart = previousPeriodStart,
                        PreviousPeriodEnd = previousPeriodEnd,
                        GeneratedAt = DateTime.Now
                    };

                    // إنشاء تقارير الفترتين
                    var currentIncomeStatement = await GenerateIncomeStatementAsync(currentPeriodStart, currentPeriodEnd);
                    var previousIncomeStatement = await GenerateIncomeStatementAsync(previousPeriodStart, previousPeriodEnd);

                    // مقارنة الإيرادات
                    report.CurrentRevenues = currentIncomeStatement.TotalRevenues;
                    report.PreviousRevenues = previousIncomeStatement.TotalRevenues;
                    report.RevenueChange = report.CurrentRevenues - report.PreviousRevenues;
                    report.RevenueChangePercentage = report.PreviousRevenues != 0 ? 
                        (report.RevenueChange / report.PreviousRevenues) * 100 : 0;

                    // مقارنة المصروفات
                    report.CurrentExpenses = currentIncomeStatement.TotalExpenses;
                    report.PreviousExpenses = previousIncomeStatement.TotalExpenses;
                    report.ExpenseChange = report.CurrentExpenses - report.PreviousExpenses;
                    report.ExpenseChangePercentage = report.PreviousExpenses != 0 ? 
                        (report.ExpenseChange / report.PreviousExpenses) * 100 : 0;

                    // مقارنة صافي الدخل
                    report.CurrentNetIncome = currentIncomeStatement.NetIncome;
                    report.PreviousNetIncome = previousIncomeStatement.NetIncome;
                    report.NetIncomeChange = report.CurrentNetIncome - report.PreviousNetIncome;
                    report.NetIncomeChangePercentage = report.PreviousNetIncome != 0 ? 
                        (report.NetIncomeChange / report.PreviousNetIncome) * 100 : 0;

                    Debug.WriteLine($"[REPORT] تم إنشاء التقرير المقارن");
                    return report;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AdvancedReportGenerator.GenerateComparativeReport");
                throw;
            }
        }

        /// <summary>
        /// تصدير تقرير إلى PDF
        /// </summary>
        /// <param name="report">التقرير</param>
        /// <param name="fileName">اسم الملف</param>
        /// <returns>مسار الملف</returns>
        public async Task<string> ExportReportToPdfAsync(object report, string fileName)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("AdvancedReportGenerator.ExportReportToPdf", async () =>
                {
                    // إنشاء مستند للتقرير
                    var document = CreateReportDocument(report);

                    // تصدير المستند إلى PDF
                    return await _exportManager.ExportToPdfAsync(document, fileName);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AdvancedReportGenerator.ExportReportToPdf");
                throw;
            }
        }

        /// <summary>
        /// الحصول على أرصدة الحسابات
        /// </summary>
        private async Task<List<AccountBalance>> GetAccountBalancesAsync(DateTime asOfDate)
        {
            var query = from account in _context.Accounts
                        join detail in _context.JournalEntryDetails on account.Id equals detail.AccountId into details
                        from detail in details.DefaultIfEmpty()
                        join entry in _context.JournalEntries on detail.JournalEntryId equals entry.Id into entries
                        from entry in entries.DefaultIfEmpty()
                        where entry == null || entry.EntryDate <= asOfDate
                        group new { account, detail } by new { account.Id, account.Code, account.Name, account.AccountType } into g
                        select new AccountBalance
                        {
                            AccountId = g.Key.Id,
                            AccountCode = g.Key.Code,
                            AccountName = g.Key.Name,
                            AccountType = g.Key.AccountType,
                            Balance = g.Sum(x => x.detail != null ? x.detail.DebitAmount - x.detail.CreditAmount : 0)
                        };

            return await query.ToListAsync();
        }

        /// <summary>
        /// الحصول على حركات الحسابات
        /// </summary>
        private async Task<List<AccountMovement>> GetAccountMovementsAsync(DateTime fromDate, DateTime toDate)
        {
            var query = from account in _context.Accounts
                        join detail in _context.JournalEntryDetails on account.Id equals detail.AccountId
                        join entry in _context.JournalEntries on detail.JournalEntryId equals entry.Id
                        where entry.EntryDate >= fromDate && entry.EntryDate <= toDate
                        group new { account, detail } by new { account.Id, account.Code, account.Name, account.AccountType } into g
                        select new AccountMovement
                        {
                            AccountId = g.Key.Id,
                            AccountCode = g.Key.Code,
                            AccountName = g.Key.Name,
                            AccountType = g.Key.AccountType,
                            DebitAmount = g.Sum(x => x.detail.DebitAmount),
                            CreditAmount = g.Sum(x => x.detail.CreditAmount)
                        };

            return await query.ToListAsync();
        }

        /// <summary>
        /// الحصول على رصيد النقدية
        /// </summary>
        private async Task<decimal> GetCashBalanceAsync(List<Account> cashAccounts, DateTime asOfDate)
        {
            decimal balance = 0;
            foreach (var account in cashAccounts)
            {
                var accountBalance = await _context.JournalEntryDetails
                    .Where(d => d.AccountId == account.Id && d.JournalEntry.EntryDate <= asOfDate)
                    .SumAsync(d => d.DebitAmount - d.CreditAmount);
                balance += accountBalance;
            }
            return balance;
        }

        /// <summary>
        /// الحصول على حركات النقدية
        /// </summary>
        private async Task<List<CashFlowItem>> GetCashMovementsAsync(List<Account> cashAccounts, DateTime fromDate, DateTime toDate)
        {
            var movements = new List<CashFlowItem>();
            
            foreach (var account in cashAccounts)
            {
                var details = await _context.JournalEntryDetails
                    .Include(d => d.JournalEntry)
                    .Where(d => d.AccountId == account.Id && 
                               d.JournalEntry.EntryDate >= fromDate && 
                               d.JournalEntry.EntryDate <= toDate)
                    .ToListAsync();

                foreach (var detail in details)
                {
                    movements.Add(new CashFlowItem
                    {
                        Date = detail.JournalEntry.EntryDate,
                        Description = detail.JournalEntry.Description,
                        NetAmount = detail.DebitAmount - detail.CreditAmount
                    });
                }
            }

            return movements;
        }

        /// <summary>
        /// التحقق من كون النشاط تشغيلي
        /// </summary>
        private bool IsOperatingActivity(CashFlowItem item)
        {
            // منطق تحديد الأنشطة التشغيلية
            return item.Description.Contains("مبيعات") || item.Description.Contains("مشتريات") || 
                   item.Description.Contains("رواتب") || item.Description.Contains("إيجار");
        }

        /// <summary>
        /// التحقق من كون النشاط استثماري
        /// </summary>
        private bool IsInvestingActivity(CashFlowItem item)
        {
            // منطق تحديد الأنشطة الاستثمارية
            return item.Description.Contains("أصول") || item.Description.Contains("معدات") || 
                   item.Description.Contains("استثمار");
        }

        /// <summary>
        /// التحقق من كون النشاط تمويلي
        /// </summary>
        private bool IsFinancingActivity(CashFlowItem item)
        {
            // منطق تحديد الأنشطة التمويلية
            return item.Description.Contains("قرض") || item.Description.Contains("رأس المال") || 
                   item.Description.Contains("أرباح");
        }

        /// <summary>
        /// إنشاء مستند للتقرير
        /// </summary>
        private FlowDocument CreateReportDocument(object report)
        {
            var document = new FlowDocument();
            
            // إضافة عنوان التقرير
            var title = new Paragraph(new Run("تقرير مالي"))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            // إضافة محتوى التقرير حسب نوعه
            if (report is BalanceSheetReport balanceSheet)
            {
                AddBalanceSheetContent(document, balanceSheet);
            }
            else if (report is IncomeStatementReport incomeStatement)
            {
                AddIncomeStatementContent(document, incomeStatement);
            }
            else if (report is CashFlowReport cashFlow)
            {
                AddCashFlowContent(document, cashFlow);
            }
            else if (report is ComparativeReport comparative)
            {
                AddComparativeContent(document, comparative);
            }

            return document;
        }

        /// <summary>
        /// إضافة محتوى الميزانية العمومية
        /// </summary>
        private void AddBalanceSheetContent(FlowDocument document, BalanceSheetReport report)
        {
            // إضافة تاريخ التقرير
            var dateInfo = new Paragraph(new Run($"كما في تاريخ: {report.AsOfDate:yyyy-MM-dd}"))
            {
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            document.Blocks.Add(dateInfo);

            // إضافة الأصول
            var assetsTitle = new Paragraph(new Run("الأصول"))
            {
                FontWeight = FontWeights.Bold,
                FontSize = 14,
                Margin = new Thickness(0, 10, 0, 5)
            };
            document.Blocks.Add(assetsTitle);

            foreach (var asset in report.Assets)
            {
                var assetParagraph = new Paragraph(new Run($"{asset.AccountName}: {asset.Balance:N2}"));
                document.Blocks.Add(assetParagraph);
            }

            var totalAssets = new Paragraph(new Run($"إجمالي الأصول: {report.TotalAssets:N2}"))
            {
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 5, 0, 10)
            };
            document.Blocks.Add(totalAssets);
        }

        /// <summary>
        /// إضافة محتوى قائمة الدخل
        /// </summary>
        private void AddIncomeStatementContent(FlowDocument document, IncomeStatementReport report)
        {
            // إضافة فترة التقرير
            var periodInfo = new Paragraph(new Run($"للفترة من {report.FromDate:yyyy-MM-dd} إلى {report.ToDate:yyyy-MM-dd}"))
            {
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            document.Blocks.Add(periodInfo);

            // إضافة الإيرادات
            var revenuesTitle = new Paragraph(new Run("الإيرادات"))
            {
                FontWeight = FontWeights.Bold,
                FontSize = 14,
                Margin = new Thickness(0, 10, 0, 5)
            };
            document.Blocks.Add(revenuesTitle);

            foreach (var revenue in report.Revenues)
            {
                var revenueParagraph = new Paragraph(new Run($"{revenue.AccountName}: {(revenue.CreditAmount - revenue.DebitAmount):N2}"));
                document.Blocks.Add(revenueParagraph);
            }

            var totalRevenues = new Paragraph(new Run($"إجمالي الإيرادات: {report.TotalRevenues:N2}"))
            {
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 5, 0, 10)
            };
            document.Blocks.Add(totalRevenues);

            // إضافة صافي الدخل
            var netIncome = new Paragraph(new Run($"صافي الدخل: {report.NetIncome:N2}"))
            {
                FontWeight = FontWeights.Bold,
                FontSize = 16,
                Margin = new Thickness(0, 10, 0, 0)
            };
            document.Blocks.Add(netIncome);
        }

        /// <summary>
        /// إضافة محتوى التدفق النقدي
        /// </summary>
        private void AddCashFlowContent(FlowDocument document, CashFlowReport report)
        {
            // إضافة فترة التقرير
            var periodInfo = new Paragraph(new Run($"للفترة من {report.FromDate:yyyy-MM-dd} إلى {report.ToDate:yyyy-MM-dd}"))
            {
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            document.Blocks.Add(periodInfo);

            // إضافة الرصيد الافتتاحي
            var openingBalance = new Paragraph(new Run($"الرصيد الافتتاحي: {report.OpeningBalance:N2}"))
            {
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            document.Blocks.Add(openingBalance);

            // إضافة صافي التدفقات
            var netCashFlow = new Paragraph(new Run($"صافي التدفق النقدي من الأنشطة التشغيلية: {report.NetOperatingCashFlow:N2}"));
            document.Blocks.Add(netCashFlow);

            // إضافة الرصيد الختامي
            var closingBalance = new Paragraph(new Run($"الرصيد الختامي: {report.ClosingBalance:N2}"))
            {
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 10, 0, 0)
            };
            document.Blocks.Add(closingBalance);
        }

        /// <summary>
        /// إضافة محتوى التقرير المقارن
        /// </summary>
        private void AddComparativeContent(FlowDocument document, ComparativeReport report)
        {
            // إضافة فترات التقرير
            var periodInfo = new Paragraph(new Run($"مقارنة بين الفترتين"))
            {
                FontWeight = FontWeights.Bold,
                FontSize = 16,
                Margin = new Thickness(0, 0, 0, 10)
            };
            document.Blocks.Add(periodInfo);

            // إضافة مقارنة الإيرادات
            var revenueComparison = new Paragraph(new Run($"الإيرادات - الحالية: {report.CurrentRevenues:N2}, السابقة: {report.PreviousRevenues:N2}, التغيير: {report.RevenueChangePercentage:F2}%"));
            document.Blocks.Add(revenueComparison);

            // إضافة مقارنة صافي الدخل
            var netIncomeComparison = new Paragraph(new Run($"صافي الدخل - الحالي: {report.CurrentNetIncome:N2}, السابق: {report.PreviousNetIncome:N2}, التغيير: {report.NetIncomeChangePercentage:F2}%"));
            document.Blocks.Add(netIncomeComparison);
        }
    }

    // فئات البيانات للتقارير
    public class BalanceSheetReport
    {
        public DateTime AsOfDate { get; set; }
        public DateTime GeneratedAt { get; set; }
        public List<AccountBalance> Assets { get; set; } = new List<AccountBalance>();
        public List<AccountBalance> Liabilities { get; set; } = new List<AccountBalance>();
        public List<AccountBalance> Equity { get; set; } = new List<AccountBalance>();
        public decimal TotalAssets { get; set; }
        public decimal TotalLiabilities { get; set; }
        public decimal TotalEquity { get; set; }
    }

    public class IncomeStatementReport
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public DateTime GeneratedAt { get; set; }
        public List<AccountMovement> Revenues { get; set; } = new List<AccountMovement>();
        public List<AccountMovement> Expenses { get; set; } = new List<AccountMovement>();
        public decimal TotalRevenues { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetIncome { get; set; }
    }

    public class CashFlowReport
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public DateTime GeneratedAt { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal ClosingBalance { get; set; }
        public List<CashFlowItem> OperatingActivities { get; set; } = new List<CashFlowItem>();
        public List<CashFlowItem> InvestingActivities { get; set; } = new List<CashFlowItem>();
        public List<CashFlowItem> FinancingActivities { get; set; } = new List<CashFlowItem>();
        public decimal NetOperatingCashFlow { get; set; }
        public decimal NetInvestingCashFlow { get; set; }
        public decimal NetFinancingCashFlow { get; set; }
    }

    public class ComparativeReport
    {
        public DateTime CurrentPeriodStart { get; set; }
        public DateTime CurrentPeriodEnd { get; set; }
        public DateTime PreviousPeriodStart { get; set; }
        public DateTime PreviousPeriodEnd { get; set; }
        public DateTime GeneratedAt { get; set; }
        public decimal CurrentRevenues { get; set; }
        public decimal PreviousRevenues { get; set; }
        public decimal RevenueChange { get; set; }
        public decimal RevenueChangePercentage { get; set; }
        public decimal CurrentExpenses { get; set; }
        public decimal PreviousExpenses { get; set; }
        public decimal ExpenseChange { get; set; }
        public decimal ExpenseChangePercentage { get; set; }
        public decimal CurrentNetIncome { get; set; }
        public decimal PreviousNetIncome { get; set; }
        public decimal NetIncomeChange { get; set; }
        public decimal NetIncomeChangePercentage { get; set; }
    }

    public class AccountBalance
    {
        public int AccountId { get; set; }
        public string AccountCode { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public AccountType AccountType { get; set; }
        public decimal Balance { get; set; }
    }

    public class AccountMovement
    {
        public int AccountId { get; set; }
        public string AccountCode { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public AccountType AccountType { get; set; }
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
    }

    public class CashFlowItem
    {
        public DateTime Date { get; set; }
        public string Description { get; set; } = string.Empty;
        public decimal NetAmount { get; set; }
    }
}
