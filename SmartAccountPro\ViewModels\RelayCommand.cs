using System;
using System.Windows.Input;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// أمر التنفيذ
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action<object> _execute;
        private readonly Predicate<object> _canExecute;

        /// <summary>
        /// إنشاء أمر تنفيذ جديد
        /// </summary>
        /// <param name="execute">الإجراء المراد تنفيذه</param>
        public RelayCommand(Action<object> execute) : this(execute, null!)
        {
        }

        /// <summary>
        /// إنشاء أمر تنفيذ جديد
        /// </summary>
        /// <param name="execute">الإجراء المراد تنفيذه</param>
        /// <param name="canExecute">شرط إمكانية التنفيذ</param>
        public RelayCommand(Action<object> execute, Predicate<object> canExecute)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        /// <summary>
        /// حدث تغيير إمكانية التنفيذ
        /// </summary>
        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        /// <summary>
        /// التحقق من إمكانية التنفيذ
        /// </summary>
        public bool CanExecute(object? parameter)
        {
            return _canExecute == null || _canExecute(parameter ?? new object());
        }

        /// <summary>
        /// تنفيذ الأمر
        /// </summary>
        public void Execute(object? parameter)
        {
            _execute(parameter ?? new object());
        }
    }

    /// <summary>
    /// أمر التنفيذ مع معامل
    /// </summary>
    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T> _execute;
        private readonly Predicate<T> _canExecute;

        /// <summary>
        /// إنشاء أمر تنفيذ جديد
        /// </summary>
        /// <param name="execute">الإجراء المراد تنفيذه</param>
        public RelayCommand(Action<T> execute) : this(execute, null!)
        {
        }

        /// <summary>
        /// إنشاء أمر تنفيذ جديد
        /// </summary>
        /// <param name="execute">الإجراء المراد تنفيذه</param>
        /// <param name="canExecute">شرط إمكانية التنفيذ</param>
        public RelayCommand(Action<T> execute, Predicate<T> canExecute)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        /// <summary>
        /// حدث تغيير إمكانية التنفيذ
        /// </summary>
        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        /// <summary>
        /// التحقق من إمكانية التنفيذ
        /// </summary>
        public bool CanExecute(object? parameter)
        {
            if (parameter == null)
                return _canExecute == null;
            return _canExecute == null || _canExecute((T)parameter);
        }

        /// <summary>
        /// تنفيذ الأمر
        /// </summary>
        public void Execute(object? parameter)
        {
            if (parameter != null)
                _execute((T)parameter);
        }
    }
}
