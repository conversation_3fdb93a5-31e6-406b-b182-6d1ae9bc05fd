﻿<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartAccount Pro - تقرير اختبار النشر</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 3px solid #007acc; padding-bottom: 20px; margin-bottom: 30px; }
        .header h1 { color: #007acc; margin: 0; font-size: 2.5em; }
        .header p { color: #666; margin: 5px 0; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; font-size: 1.2em; }
        .summary-card .number { font-size: 2.5em; font-weight: bold; margin: 10px 0; }
        .environment { margin-bottom: 40px; border: 1px solid #ddd; border-radius: 10px; overflow: hidden; }
        .environment-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; }
        .environment-header h2 { margin: 0; color: #333; }
        .environment-content { padding: 20px; }
        .test-section { margin-bottom: 25px; }
        .test-section h3 { color: #007acc; border-bottom: 2px solid #007acc; padding-bottom: 5px; }
        .test-result { display: flex; justify-content: space-between; align-items: center; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-result.passed { background-color: #d4edda; border-left: 4px solid #28a745; }
        .test-result.failed { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .test-result.skipped { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .status-badge { padding: 4px 12px; border-radius: 20px; color: white; font-weight: bold; font-size: 0.8em; }
        .status-passed { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .status-skipped { background-color: #ffc107; }
        .details { margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; font-size: 0.9em; }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 12px; text-align: right; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .performance-metric { display: inline-block; margin: 5px 10px; padding: 5px 10px; background-color: #e9ecef; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 SmartAccount Pro</h1>
            <h2>تقرير اختبار النشر في البيئات النظيفة</h2>
            <p>معرف الجلسة: 25d6bea3</p>
            <p>تاريخ الاختبار: 2025-05-31 11:31:19</p>
        </div>        <div class="summary">
            <div class="summary-card">
                <h3>البيئات المختبرة</h3>
                <div class="number">3</div>
                <p>0 نجحت</p>
            </div>
            <div class="summary-card">
                <h3>إجمالي الاختبارات</h3>
                <div class="number">0</div>
                <p>0 نجحت، 0 فشلت</p>
            </div>
            <div class="summary-card">
                <h3>معدل النجاح</h3>
                <div class="number">NaN%</div>
                <p>معدل النجاح الإجمالي</p>
            </div>
        </div>        <div class="environment">
            <div class="environment-header">
                <h2>🖥️ Windows 10 Pro - <span class="status-badge status-failed">فشلت</span></h2>
                <p>المدة: 0 دقيقة | الاختبارات: 0/0 نجحت</p>
            </div>
            <div class="environment-content"></div></div>        <div class="environment">
            <div class="environment-header">
                <h2>🖥️ Windows 11 Home - <span class="status-badge status-failed">فشلت</span></h2>
                <p>المدة: 0 دقيقة | الاختبارات: 0/0 نجحت</p>
            </div>
            <div class="environment-content"></div></div>        <div class="environment">
            <div class="environment-header">
                <h2>🖥️ Windows Server 2019 - <span class="status-badge status-failed">فشلت</span></h2>
                <p>المدة: 0 دقيقة | الاختبارات: 0/0 نجحت</p>
            </div>
            <div class="environment-content"></div></div>        <div class="footer">
            <p>تم إنشاء هذا التقرير تلقائياً بواسطة إطار عمل اختبار النشر SmartAccount Pro</p>
            <p>© 2024 SmartAccount Pro Team. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html>
