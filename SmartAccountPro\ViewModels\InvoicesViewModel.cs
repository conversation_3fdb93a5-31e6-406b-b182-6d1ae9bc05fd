using Microsoft.Win32;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// نموذج عرض الفواتير
    /// </summary>
    public class InvoicesViewModel : ViewModelBase
    {
        private ObservableCollection<Invoice> _invoices;
        private Invoice _selectedInvoice;
        private bool _isInvoiceDialogOpen;
        private string _invoiceDialogTitle;
        private Invoice _currentInvoice;
        private ObservableCollection<Customer> _customers;
        private Customer _selectedCustomer;
        private ObservableCollection<Product> _products;
        private Product _selectedProduct;
        private decimal _quantity;
        private decimal _price;
        private decimal _discount;
        private decimal _tax;
        private string _searchText;
        private DateTime _startDate;
        private DateTime _endDate;
        private ObservableCollection<InvoiceStatus> _invoiceStatuses;
        private InvoiceStatus _selectedInvoiceStatus;

        /// <summary>
        /// قائمة الفواتير
        /// </summary>
        public ObservableCollection<Invoice> Invoices
        {
            get => _invoices;
            set => SetProperty(ref _invoices, value);
        }

        /// <summary>
        /// الفاتورة المحددة
        /// </summary>
        public Invoice SelectedInvoice
        {
            get => _selectedInvoice;
            set => SetProperty(ref _selectedInvoice, value);
        }

        /// <summary>
        /// هل نافذة الفاتورة مفتوحة
        /// </summary>
        public bool IsInvoiceDialogOpen
        {
            get => _isInvoiceDialogOpen;
            set => SetProperty(ref _isInvoiceDialogOpen, value);
        }

        /// <summary>
        /// عنوان نافذة الفاتورة
        /// </summary>
        public string InvoiceDialogTitle
        {
            get => _invoiceDialogTitle;
            set => SetProperty(ref _invoiceDialogTitle, value);
        }

        /// <summary>
        /// الفاتورة الحالية
        /// </summary>
        public Invoice CurrentInvoice
        {
            get => _currentInvoice;
            set => SetProperty(ref _currentInvoice, value);
        }

        /// <summary>
        /// قائمة العملاء
        /// </summary>
        public ObservableCollection<Customer> Customers
        {
            get => _customers;
            set => SetProperty(ref _customers, value);
        }

        /// <summary>
        /// العميل المحدد
        /// </summary>
        public Customer SelectedCustomer
        {
            get => _selectedCustomer;
            set => SetProperty(ref _selectedCustomer, value);
        }

        /// <summary>
        /// قائمة المنتجات
        /// </summary>
        public ObservableCollection<Product> Products
        {
            get => _products;
            set => SetProperty(ref _products, value);
        }

        /// <summary>
        /// المنتج المحدد
        /// </summary>
        public Product SelectedProduct
        {
            get => _selectedProduct;
            set => SetProperty(ref _selectedProduct, value);
        }

        /// <summary>
        /// الكمية
        /// </summary>
        public decimal Quantity
        {
            get => _quantity;
            set => SetProperty(ref _quantity, value);
        }

        /// <summary>
        /// السعر
        /// </summary>
        public decimal Price
        {
            get => _price;
            set => SetProperty(ref _price, value);
        }

        /// <summary>
        /// الخصم
        /// </summary>
        public decimal Discount
        {
            get => _discount;
            set => SetProperty(ref _discount, value);
        }

        /// <summary>
        /// الضريبة
        /// </summary>
        public decimal Tax
        {
            get => _tax;
            set => SetProperty(ref _tax, value);
        }

        /// <summary>
        /// نص البحث
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        /// <summary>
        /// تاريخ البداية
        /// </summary>
        public DateTime StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        /// <summary>
        /// تاريخ النهاية
        /// </summary>
        public DateTime EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        /// <summary>
        /// قائمة حالات الفواتير
        /// </summary>
        public ObservableCollection<InvoiceStatus> InvoiceStatuses
        {
            get => _invoiceStatuses;
            set => SetProperty(ref _invoiceStatuses, value);
        }

        /// <summary>
        /// حالة الفاتورة المحددة
        /// </summary>
        public InvoiceStatus SelectedInvoiceStatus
        {
            get => _selectedInvoiceStatus;
            set => SetProperty(ref _selectedInvoiceStatus, value);
        }

        /// <summary>
        /// أمر إضافة فاتورة جديدة
        /// </summary>
        public ICommand AddInvoiceCommand { get; }

        /// <summary>
        /// أمر تعديل الفاتورة
        /// </summary>
        public ICommand EditInvoiceCommand { get; }

        /// <summary>
        /// أمر حذف الفاتورة
        /// </summary>
        public ICommand DeleteInvoiceCommand { get; }

        /// <summary>
        /// أمر طباعة الفاتورة
        /// </summary>
        public ICommand PrintInvoiceCommand { get; }

        /// <summary>
        /// أمر تصدير الفاتورة إلى PDF
        /// </summary>
        public ICommand ExportToPdfCommand { get; }

        /// <summary>
        /// أمر البحث
        /// </summary>
        public ICommand SearchCommand { get; }

        /// <summary>
        /// أمر حفظ الفاتورة
        /// </summary>
        public ICommand SaveInvoiceCommand { get; }

        /// <summary>
        /// أمر إلغاء الفاتورة
        /// </summary>
        public ICommand CancelInvoiceCommand { get; }

        /// <summary>
        /// أمر إضافة عنصر
        /// </summary>
        public ICommand AddItemCommand { get; }

        /// <summary>
        /// أمر حذف عنصر
        /// </summary>
        public ICommand RemoveItemCommand { get; }

        public InvoicesViewModel()
        {
            // تهيئة الأوامر
            AddInvoiceCommand = new RelayCommand(AddInvoice);
            EditInvoiceCommand = new RelayCommand(EditInvoice, CanEditInvoice);
            DeleteInvoiceCommand = new RelayCommand(DeleteInvoice, CanDeleteInvoice);
            PrintInvoiceCommand = new RelayCommand(PrintInvoice, CanPrintInvoice);
            ExportToPdfCommand = new RelayCommand(ExportToPdf, CanExportToPdf);
            SearchCommand = new RelayCommand(Search);
            SaveInvoiceCommand = new RelayCommand(SaveInvoice);
            CancelInvoiceCommand = new RelayCommand(CancelInvoice);
            AddItemCommand = new RelayCommand(AddItem, CanAddItem);
            RemoveItemCommand = new RelayCommand(RemoveItem, CanRemoveItem);

            // تهيئة البيانات
            InitializeData();
        }

        /// <summary>
        /// تهيئة البيانات
        /// </summary>
        private void InitializeData()
        {
            // تهيئة التواريخ
            StartDate = new DateTime(DateTime.Now.Year, 1, 1);
            EndDate = DateTime.Now;

            // تهيئة حالات الفواتير
            InvoiceStatuses = new ObservableCollection<InvoiceStatus>
            {
                new InvoiceStatus { Id = 1, Name = "مسودة" },
                new InvoiceStatus { Id = 2, Name = "مدفوعة" },
                new InvoiceStatus { Id = 3, Name = "ملغاة" }
            };

            // تحميل البيانات
            LoadInvoices();
            LoadCustomers();
            LoadProducts();
        }

        /// <summary>
        /// تحميل الفواتير
        /// </summary>
        private void LoadInvoices()
        {
            // هنا يمكن تحميل الفواتير من قاعدة البيانات
            // في هذا المثال سنستخدم بيانات تجريبية
            Invoices = new ObservableCollection<Invoice>
            {
                new Invoice
                {
                    Id = 1,
                    InvoiceNumber = "INV-001",
                    Date = DateTime.Now.AddDays(-10),
                    CustomerId = 1,
                    CustomerName = "شركة الأمل",
                    Total = 1500,
                    Status = 2,
                    StatusName = "مدفوعة"
                },
                new Invoice
                {
                    Id = 2,
                    InvoiceNumber = "INV-002",
                    Date = DateTime.Now.AddDays(-5),
                    CustomerId = 2,
                    CustomerName = "مؤسسة النور",
                    Total = 2300,
                    Status = 1,
                    StatusName = "مسودة"
                }
            };
        }

        /// <summary>
        /// تحميل العملاء
        /// </summary>
        private void LoadCustomers()
        {
            // هنا يمكن تحميل العملاء من قاعدة البيانات
            // في هذا المثال سنستخدم بيانات تجريبية
            Customers = new ObservableCollection<Customer>
            {
                new Customer { Id = 1, Name = "شركة الأمل", Phone = "0123456789", Email = "<EMAIL>" },
                new Customer { Id = 2, Name = "مؤسسة النور", Phone = "0123456788", Email = "<EMAIL>" }
            };
        }

        /// <summary>
        /// تحميل المنتجات
        /// </summary>
        private void LoadProducts()
        {
            // هنا يمكن تحميل المنتجات من قاعدة البيانات
            // في هذا المثال سنستخدم بيانات تجريبية
            Products = new ObservableCollection<Product>
            {
                new Product { Id = 1, Name = "منتج 1", Price = 100 },
                new Product { Id = 2, Name = "منتج 2", Price = 200 },
                new Product { Id = 3, Name = "منتج 3", Price = 300 }
            };
        }

        /// <summary>
        /// إضافة فاتورة جديدة
        /// </summary>
        private void AddInvoice(object parameter)
        {
            InvoiceDialogTitle = "إضافة فاتورة جديدة";
            CurrentInvoice = new Invoice
            {
                Date = DateTime.Now,
                Status = 1,
                StatusName = "مسودة",
                InvoiceNumber = $"INV-{DateTime.Now:yyyyMMdd}-{Invoices.Count + 1:000}",
                Items = new ObservableCollection<InvoiceItem>()
            };
            IsInvoiceDialogOpen = true;
        }

        /// <summary>
        /// تعديل الفاتورة
        /// </summary>
        private void EditInvoice(object parameter)
        {
            if (SelectedInvoice != null)
            {
                InvoiceDialogTitle = "تعديل الفاتورة";
                CurrentInvoice = new Invoice
                {
                    Id = SelectedInvoice.Id,
                    InvoiceNumber = SelectedInvoice.InvoiceNumber,
                    Date = SelectedInvoice.Date,
                    CustomerId = SelectedInvoice.CustomerId,
                    CustomerName = SelectedInvoice.CustomerName,
                    Total = SelectedInvoice.Total,
                    Status = SelectedInvoice.Status,
                    StatusName = SelectedInvoice.StatusName,
                    Items = new ObservableCollection<InvoiceItem>()
                };
                IsInvoiceDialogOpen = true;
            }
        }

        /// <summary>
        /// هل يمكن تعديل الفاتورة
        /// </summary>
        private bool CanEditInvoice(object parameter)
        {
            return SelectedInvoice != null;
        }

        /// <summary>
        /// حذف الفاتورة
        /// </summary>
        private void DeleteInvoice(object parameter)
        {
            if (SelectedInvoice != null)
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف الفاتورة؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    Invoices.Remove(SelectedInvoice);
                }
            }
        }

        /// <summary>
        /// هل يمكن حذف الفاتورة
        /// </summary>
        private bool CanDeleteInvoice(object parameter)
        {
            return SelectedInvoice != null;
        }

        /// <summary>
        /// طباعة الفاتورة
        /// </summary>
        private void PrintInvoice(object parameter)
        {
            if (SelectedInvoice != null)
            {
                // هنا يمكن إضافة كود لطباعة الفاتورة
                MessageBox.Show("جاري طباعة الفاتورة...", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// هل يمكن طباعة الفاتورة
        /// </summary>
        private bool CanPrintInvoice(object parameter)
        {
            return SelectedInvoice != null;
        }

        /// <summary>
        /// تصدير الفاتورة إلى PDF
        /// </summary>
        private void ExportToPdf(object parameter)
        {
            if (SelectedInvoice != null)
            {
                try
                {
                    var fileName = $"فاتورة_{SelectedInvoice.InvoiceNumber}_{DateTime.Now:yyyy-MM-dd}.pdf";
                    var filePath = ReportExporter.ShowSaveFileDialog(fileName, "PDF Files (*.pdf)|*.pdf|All Files (*.*)|*.*");

                    if (!string.IsNullOrEmpty(filePath))
                    {
                        // الحصول على عنصر الفاتورة
                        var invoiceElement = Application.Current.MainWindow.FindName("MainContent") as ContentControl;
                        if (invoiceElement != null && invoiceElement.Content is FrameworkElement element)
                        {
                            if (ReportExporter.ExportToPdf(element, filePath, $"فاتورة رقم {SelectedInvoice.InvoiceNumber}"))
                            {
                                MessageBox.Show("تم تصدير الفاتورة بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    ExceptionHandler.HandleException(ex, "InvoicesViewModel.ExportToPdf");
                }
            }
        }

        /// <summary>
        /// هل يمكن تصدير الفاتورة إلى PDF
        /// </summary>
        private bool CanExportToPdf(object parameter)
        {
            return SelectedInvoice != null;
        }

        /// <summary>
        /// البحث
        /// </summary>
        private void Search(object parameter)
        {
            // هنا يمكن إضافة كود للبحث في الفواتير
            MessageBox.Show("جاري البحث...", "بحث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// حفظ الفاتورة
        /// </summary>
        private void SaveInvoice(object parameter)
        {
            // هنا يمكن إضافة كود لحفظ الفاتورة
            IsInvoiceDialogOpen = false;
        }

        /// <summary>
        /// إلغاء الفاتورة
        /// </summary>
        private void CancelInvoice(object parameter)
        {
            IsInvoiceDialogOpen = false;
        }

        /// <summary>
        /// إضافة عنصر
        /// </summary>
        private void AddItem(object parameter)
        {
            // هنا يمكن إضافة كود لإضافة عنصر للفاتورة
        }

        /// <summary>
        /// هل يمكن إضافة عنصر
        /// </summary>
        private bool CanAddItem(object parameter)
        {
            return SelectedProduct != null && Quantity > 0;
        }

        /// <summary>
        /// حذف عنصر
        /// </summary>
        private void RemoveItem(object parameter)
        {
            // هنا يمكن إضافة كود لحذف عنصر من الفاتورة
        }

        /// <summary>
        /// هل يمكن حذف عنصر
        /// </summary>
        private bool CanRemoveItem(object parameter)
        {
            return parameter != null;
        }
    }

    /// <summary>
    /// نموذج الفاتورة
    /// </summary>
    public class Invoice
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime Date { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public decimal Total { get; set; }
        public int Status { get; set; }
        public string StatusName { get; set; }
        public ObservableCollection<InvoiceItem> Items { get; set; }
    }

    /// <summary>
    /// نموذج عنصر الفاتورة
    /// </summary>
    public class InvoiceItem
    {
        public int Id { get; set; }
        public int InvoiceId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal Quantity { get; set; }
        public decimal Price { get; set; }
        public decimal Discount { get; set; }
        public decimal Tax { get; set; }
        public decimal Total { get; set; }
    }

    /// <summary>
    /// نموذج العميل
    /// </summary>
    public class Customer
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
    }

    /// <summary>
    /// نموذج المنتج
    /// </summary>
    public class Product
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public decimal Price { get; set; }
    }

    /// <summary>
    /// نموذج حالة الفاتورة
    /// </summary>
    public class InvoiceStatus
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
}
