using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SmartAccountPro.Data.Services
{
    /// <summary>
    /// خدمة إدارة الإشعارات
    /// </summary>
    public class NotificationService
    {
        private readonly AppDbContext _dbContext;

        public NotificationService(AppDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// إنشاء إشعار جديد
        /// </summary>
        public async Task<Notification> CreateNotificationAsync(Notification notification)
        {
            notification.CreatedAt = DateTime.Now;
            _dbContext.Notifications.Add(notification);
            await _dbContext.SaveChangesAsync();
            return notification;
        }

        /// <summary>
        /// إنشاء إشعار جديد بشكل مبسط
        /// </summary>
        public async Task<Notification> CreateNotificationAsync(
            string title,
            string content,
            NotificationType type,
            int userId,
            int? entityId = null,
            string entityType = null,
            string link = null,
            bool isImportant = false)
        {
            var notification = new Notification
            {
                Title = title,
                Content = content,
                Type = type,
                UserId = userId,
                EntityId = entityId,
                EntityType = entityType,
                Link = link,
                IsImportant = isImportant,
                CreatedAt = DateTime.Now
            };

            return await CreateNotificationAsync(notification);
        }

        /// <summary>
        /// الحصول على جميع الإشعارات لمستخدم معين
        /// </summary>
        public async Task<List<Notification>> GetNotificationsForUserAsync(int userId, bool includeRead = false)
        {
            var query = _dbContext.Notifications.Where(n => n.UserId == userId);

            if (!includeRead)
            {
                query = query.Where(n => !n.ReadAt.HasValue);
            }

            return await query.OrderByDescending(n => n.CreatedAt).ToListAsync();
        }

        /// <summary>
        /// تحديد إشعار كمقروء
        /// </summary>
        public async Task<bool> MarkAsReadAsync(int notificationId)
        {
            var notification = await _dbContext.Notifications.FindAsync(notificationId);
            if (notification == null)
            {
                return false;
            }

            notification.ReadAt = DateTime.Now;
            await _dbContext.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// تحديد جميع إشعارات المستخدم كمقروءة
        /// </summary>
        public async Task<int> MarkAllAsReadAsync(int userId)
        {
            var unreadNotifications = await _dbContext.Notifications
                .Where(n => n.UserId == userId && !n.ReadAt.HasValue)
                .ToListAsync();

            if (!unreadNotifications.Any())
            {
                return 0;
            }

            foreach (var notification in unreadNotifications)
            {
                notification.ReadAt = DateTime.Now;
            }

            await _dbContext.SaveChangesAsync();
            return unreadNotifications.Count;
        }

        /// <summary>
        /// حذف إشعار
        /// </summary>
        public async Task<bool> DeleteNotificationAsync(int notificationId)
        {
            var notification = await _dbContext.Notifications.FindAsync(notificationId);
            if (notification == null)
            {
                return false;
            }

            _dbContext.Notifications.Remove(notification);
            await _dbContext.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف جميع الإشعارات المقروءة للمستخدم
        /// </summary>
        public async Task<int> DeleteReadNotificationsAsync(int userId)
        {
            var readNotifications = await _dbContext.Notifications
                .Where(n => n.UserId == userId && n.ReadAt.HasValue)
                .ToListAsync();

            if (!readNotifications.Any())
            {
                return 0;
            }

            _dbContext.Notifications.RemoveRange(readNotifications);
            await _dbContext.SaveChangesAsync();
            return readNotifications.Count;
        }

        /// <summary>
        /// إنشاء إشعار تذكير بفاتورة مستحقة
        /// </summary>
        public async Task<Notification> CreateInvoiceDueReminderAsync(int userId, int invoiceId, string invoiceNumber, DateTime dueDate, decimal amount)
        {
            return await CreateNotificationAsync(
                "فاتورة مستحقة الدفع",
                $"الفاتورة رقم {invoiceNumber} مستحقة الدفع بتاريخ {dueDate:yyyy/MM/dd} بمبلغ {amount:N2}",
                NotificationType.Reminder,
                userId,
                invoiceId,
                "Invoice",
                $"/invoices/details/{invoiceId}",
                true);
        }

        /// <summary>
        /// إنشاء إشعار بانخفاض الرصيد
        /// </summary>
        public async Task<Notification> CreateLowBalanceAlertAsync(int userId, int accountId, string accountName, decimal balance, decimal threshold)
        {
            return await CreateNotificationAsync(
                "تنبيه انخفاض الرصيد",
                $"رصيد الحساب {accountName} انخفض إلى {balance:N2} وهو أقل من الحد الأدنى {threshold:N2}",
                NotificationType.Warning,
                userId,
                accountId,
                "Account",
                $"/accounts/details/{accountId}",
                true);
        }

        /// <summary>
        /// إنشاء إشعار بوجود قيود غير مرحلة
        /// </summary>
        public async Task<Notification> CreateUnpostedEntriesAlertAsync(int userId, int count)
        {
            return await CreateNotificationAsync(
                "قيود غير مرحلة",
                $"يوجد {count} قيود محاسبية غير مرحلة",
                NotificationType.Info,
                userId,
                null,
                "JournalEntries",
                "/journal-entries/unposted",
                false);
        }
    }
}
