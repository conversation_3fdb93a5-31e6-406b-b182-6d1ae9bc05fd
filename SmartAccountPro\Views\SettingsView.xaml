<UserControl x:Class="SmartAccountPro.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SmartAccountPro.Views"
             xmlns:vm="clr-namespace:SmartAccountPro.ViewModels"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <UserControl.DataContext>
        <vm:SettingsViewModel />
    </UserControl.DataContext>

    <Grid Background="{DynamicResource BackgroundColor}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <Border Grid.Row="0"
                Background="{DynamicResource PrimaryColor}"
                CornerRadius="10"
                Padding="20,15"
                Margin="20,20,20,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="2" BlurRadius="10" Opacity="0.2" Color="#000000"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="&#xE713;"
                           FontFamily="Segoe MDL2 Assets"
                           FontSize="24"
                           Foreground="White"
                           VerticalAlignment="Center"
                           Margin="0,0,10,0"/>
                <TextBlock Text="الإعدادات"
                           FontSize="24"
                           FontWeight="Bold"
                           Foreground="White">
                    <TextBlock.Effect>
                        <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.3" Color="#000000"/>
                    </TextBlock.Effect>
                </TextBlock>
            </StackPanel>
        </Border>

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                <!-- المظهر -->
                <Border Background="White"
                        BorderBrush="#E0E0E0"
                        BorderThickness="1"
                        CornerRadius="10"
                        Margin="0,0,0,20"
                        Padding="20">
                    <Border.Effect>
                        <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="&#xE790;"
                                       FontFamily="Segoe MDL2 Assets"
                                       Foreground="{DynamicResource PrimaryColor}"
                                       FontSize="18"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,0"/>
                            <TextBlock Text="المظهر"
                                       FontSize="18"
                                       FontWeight="Bold"
                                       Foreground="#333333"/>
                        </StackPanel>

                        <!-- وضع الليل -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="وضع الليل"
                                           FontWeight="SemiBold"
                                           Foreground="{DynamicResource TextColor}"/>
                                <TextBlock Text="تفعيل وضع الليل للتطبيق"
                                           FontSize="12"
                                           Foreground="{DynamicResource SecondaryTextColor}"
                                           Margin="0,5,0,0"/>
                            </StackPanel>

                            <ToggleButton Grid.Column="1"
                                          IsChecked="{Binding IsDarkMode}"
                                          Command="{Binding ToggleThemeCommand}"
                                          Width="50"
                                          Height="24"
                                          VerticalAlignment="Center">
                                <ToggleButton.Template>
                                    <ControlTemplate TargetType="ToggleButton">
                                        <Border x:Name="Border"
                                                Background="#EEEEEE"
                                                CornerRadius="12"
                                                BorderThickness="1"
                                                BorderBrush="#CCCCCC">
                                            <Grid>
                                                <Ellipse x:Name="Indicator"
                                                         Width="18"
                                                         Height="18"
                                                         HorizontalAlignment="Left"
                                                         Margin="3,0,0,0"
                                                         Fill="White"
                                                         Stroke="#CCCCCC"
                                                         StrokeThickness="1"/>
                                            </Grid>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsChecked" Value="True">
                                                <Setter TargetName="Border" Property="Background" Value="#2196F3"/>
                                                <Setter TargetName="Border" Property="BorderBrush" Value="#1976D2"/>
                                                <Setter TargetName="Indicator" Property="Margin" Value="29,0,0,0"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </ToggleButton.Template>
                            </ToggleButton>
                        </Grid>

                        <!-- حجم الخط -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="حجم الخط"
                                           FontWeight="SemiBold"
                                           Foreground="{DynamicResource TextColor}"/>
                                <TextBlock Text="تغيير حجم الخط في التطبيق"
                                           FontSize="12"
                                           Foreground="{DynamicResource SecondaryTextColor}"
                                           Margin="0,5,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1"
                                        Orientation="Horizontal"
                                        VerticalAlignment="Center">
                                <Button Content="أ-"
                                        Command="{Binding DecreaseFontSizeCommand}"
                                        Style="{StaticResource ModernButton}"
                                        Width="40"
                                        Height="30"
                                        Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding FontSize}"
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource TextColor}"
                                           Width="30"
                                           TextAlignment="Center"/>
                                <Button Content="أ+"
                                        Command="{Binding IncreaseFontSizeCommand}"
                                        Style="{StaticResource ModernButton}"
                                        Width="40"
                                        Height="30"
                                        Margin="5,0,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- النسخ الاحتياطي -->
                <Border Background="White"
                        BorderBrush="#E0E0E0"
                        BorderThickness="1"
                        CornerRadius="10"
                        Margin="0,0,0,20"
                        Padding="20">
                    <Border.Effect>
                        <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="&#xE74E;"
                                       FontFamily="Segoe MDL2 Assets"
                                       Foreground="{DynamicResource PrimaryColor}"
                                       FontSize="18"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,0"/>
                            <TextBlock Text="النسخ الاحتياطي"
                                       FontSize="18"
                                       FontWeight="Bold"
                                       Foreground="#333333"/>
                        </StackPanel>

                        <!-- النسخ الاحتياطي التلقائي -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="النسخ الاحتياطي التلقائي"
                                           FontWeight="SemiBold"
                                           Foreground="{DynamicResource TextColor}"/>
                                <TextBlock Text="إنشاء نسخة احتياطية تلقائياً بشكل دوري"
                                           FontSize="12"
                                           Foreground="{DynamicResource SecondaryTextColor}"
                                           Margin="0,5,0,0"/>
                            </StackPanel>

                            <ToggleButton Grid.Column="1"
                                          IsChecked="{Binding AutoBackup}"
                                          Width="50"
                                          Height="24"
                                          VerticalAlignment="Center">
                                <ToggleButton.Template>
                                    <ControlTemplate TargetType="ToggleButton">
                                        <Border x:Name="Border"
                                                Background="#EEEEEE"
                                                CornerRadius="12"
                                                BorderThickness="1"
                                                BorderBrush="#CCCCCC">
                                            <Grid>
                                                <Ellipse x:Name="Indicator"
                                                         Width="18"
                                                         Height="18"
                                                         HorizontalAlignment="Left"
                                                         Margin="3,0,0,0"
                                                         Fill="White"
                                                         Stroke="#CCCCCC"
                                                         StrokeThickness="1"/>
                                            </Grid>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsChecked" Value="True">
                                                <Setter TargetName="Border" Property="Background" Value="#2196F3"/>
                                                <Setter TargetName="Border" Property="BorderBrush" Value="#1976D2"/>
                                                <Setter TargetName="Indicator" Property="Margin" Value="29,0,0,0"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </ToggleButton.Template>
                            </ToggleButton>
                        </Grid>

                        <!-- فترة النسخ الاحتياطي -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="فترة النسخ الاحتياطي (بالأيام)"
                                           FontWeight="SemiBold"
                                           Foreground="{DynamicResource TextColor}"/>
                                <TextBlock Text="عدد الأيام بين كل نسخة احتياطية تلقائية"
                                           FontSize="12"
                                           Foreground="{DynamicResource SecondaryTextColor}"
                                           Margin="0,5,0,0"/>
                            </StackPanel>

                            <ComboBox Grid.Column="1"
                                      SelectedValue="{Binding BackupInterval}"
                                      Width="100"
                                      VerticalAlignment="Center">
                                <ComboBoxItem Content="1" />
                                <ComboBoxItem Content="3" />
                                <ComboBoxItem Content="7" />
                                <ComboBoxItem Content="14" />
                                <ComboBoxItem Content="30" />
                            </ComboBox>
                        </Grid>

                        <!-- مسار النسخ الاحتياطي -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="مسار النسخ الاحتياطي"
                                           FontWeight="SemiBold"
                                           Foreground="{DynamicResource TextColor}"/>
                                <TextBlock Text="المجلد الذي سيتم حفظ النسخ الاحتياطية فيه"
                                           FontSize="12"
                                           Foreground="{DynamicResource SecondaryTextColor}"
                                           Margin="0,5,0,0"/>
                            </StackPanel>

                            <Button Grid.Column="1"
                                    Command="{Binding SelectBackupPathCommand}"
                                    Style="{StaticResource ModernButton}"
                                    VerticalAlignment="Center"
                                    Padding="10,6">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="&#xE8B7;"
                                               FontFamily="Segoe MDL2 Assets"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                                    <TextBlock Text="تحديد المسار"
                                               VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>

                        <TextBlock Text="{Binding BackupPath}"
                                   Foreground="{DynamicResource SecondaryTextColor}"
                                   TextWrapping="Wrap"
                                   Margin="0,0,0,10"/>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,0">
                            <Button Command="{Binding RestoreBackupCommand}"
                                    Style="{StaticResource ModernButton}"
                                    Margin="0,0,10,0"
                                    Padding="10,6">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="&#xE777;"
                                               FontFamily="Segoe MDL2 Assets"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                                    <TextBlock Text="استعادة نسخة احتياطية"
                                               VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Command="{Binding CreateBackupNowCommand}"
                                    Style="{StaticResource GreenButton}"
                                    Padding="10,6">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="&#xE74E;"
                                               FontFamily="Segoe MDL2 Assets"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                                    <TextBlock Text="إنشاء نسخة احتياطية الآن"
                                               VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- حفظ الإعدادات -->
                <Button Command="{Binding SaveSettingsCommand}"
                        Style="{StaticResource GreenButton}"
                        HorizontalAlignment="Center"
                        Padding="20,12"
                        Margin="0,20,0,0">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE74E;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ الإعدادات"
                                   VerticalAlignment="Center"
                                   FontWeight="SemiBold"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
