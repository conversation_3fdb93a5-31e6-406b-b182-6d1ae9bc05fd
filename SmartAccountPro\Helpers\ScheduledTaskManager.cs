using SmartAccountPro.Data;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// مدير المهام المجدولة
    /// </summary>
    public class ScheduledTaskManager
    {
        private static ScheduledTaskManager? _instance;
        private readonly List<ScheduledTask> _tasks;
        private readonly System.Timers.Timer _timer;
        private readonly object _lockObject = new object();
        private bool _isRunning = false;

        /// <summary>
        /// الحصول على النسخة الوحيدة من مدير المهام المجدولة
        /// </summary>
        public static ScheduledTaskManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ScheduledTaskManager();
                }
                return _instance;
            }
        }

        /// <summary>
        /// إنشاء نسخة جديدة من مدير المهام المجدولة
        /// </summary>
        private ScheduledTaskManager()
        {
            try
            {
                _tasks = new List<ScheduledTask>();
                
                // إنشاء مؤقت للتحقق من المهام كل دقيقة
                _timer = new System.Timers.Timer(60000); // 60 ثانية
                _timer.Elapsed += OnTimerElapsed;
                _timer.AutoReset = true;

                // تحميل المهام الافتراضية
                LoadDefaultTasks();

                Debug.WriteLine("[SCHEDULER] تم تهيئة مدير المهام المجدولة.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ScheduledTaskManager.Constructor");
                _tasks = new List<ScheduledTask>();
                _timer = new System.Timers.Timer(60000);
            }
        }

        /// <summary>
        /// بدء تشغيل مدير المهام
        /// </summary>
        public void Start()
        {
            try
            {
                PerformanceHelper.MeasureExecutionTime("ScheduledTaskManager.Start", () =>
                {
                    lock (_lockObject)
                    {
                        if (!_isRunning)
                        {
                            _timer.Start();
                            _isRunning = true;
                            Debug.WriteLine("[SCHEDULER] تم بدء تشغيل مدير المهام المجدولة.");
                        }
                    }
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ScheduledTaskManager.Start");
            }
        }

        /// <summary>
        /// إيقاف تشغيل مدير المهام
        /// </summary>
        public void Stop()
        {
            try
            {
                PerformanceHelper.MeasureExecutionTime("ScheduledTaskManager.Stop", () =>
                {
                    lock (_lockObject)
                    {
                        if (_isRunning)
                        {
                            _timer.Stop();
                            _isRunning = false;
                            Debug.WriteLine("[SCHEDULER] تم إيقاف تشغيل مدير المهام المجدولة.");
                        }
                    }
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ScheduledTaskManager.Stop");
            }
        }

        /// <summary>
        /// إضافة مهمة مجدولة
        /// </summary>
        /// <param name="task">المهمة</param>
        public void AddTask(ScheduledTask task)
        {
            try
            {
                PerformanceHelper.MeasureExecutionTime("ScheduledTaskManager.AddTask", () =>
                {
                    lock (_lockObject)
                    {
                        _tasks.Add(task);
                        Debug.WriteLine($"[SCHEDULER] تم إضافة مهمة مجدولة: {task.Name}");
                    }
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ScheduledTaskManager.AddTask");
            }
        }

        /// <summary>
        /// إزالة مهمة مجدولة
        /// </summary>
        /// <param name="taskName">اسم المهمة</param>
        public void RemoveTask(string taskName)
        {
            try
            {
                PerformanceHelper.MeasureExecutionTime("ScheduledTaskManager.RemoveTask", () =>
                {
                    lock (_lockObject)
                    {
                        var task = _tasks.FirstOrDefault(t => t.Name == taskName);
                        if (task != null)
                        {
                            _tasks.Remove(task);
                            Debug.WriteLine($"[SCHEDULER] تم إزالة مهمة مجدولة: {taskName}");
                        }
                    }
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ScheduledTaskManager.RemoveTask");
            }
        }

        /// <summary>
        /// الحصول على قائمة المهام
        /// </summary>
        /// <returns>قائمة المهام</returns>
        public List<ScheduledTask> GetTasks()
        {
            try
            {
                return PerformanceHelper.MeasureExecutionTime("ScheduledTaskManager.GetTasks", () =>
                {
                    lock (_lockObject)
                    {
                        return new List<ScheduledTask>(_tasks);
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ScheduledTaskManager.GetTasks");
                return new List<ScheduledTask>();
            }
        }

        /// <summary>
        /// تنفيذ مهمة فوري
        /// </summary>
        /// <param name="taskName">اسم المهمة</param>
        /// <returns>نجاح التنفيذ</returns>
        public async Task<bool> ExecuteTaskNowAsync(string taskName)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("ScheduledTaskManager.ExecuteTaskNow", async () =>
                {
                    ScheduledTask? task;
                    lock (_lockObject)
                    {
                        task = _tasks.FirstOrDefault(t => t.Name == taskName);
                    }

                    if (task != null && task.IsEnabled)
                    {
                        return await ExecuteTaskAsync(task);
                    }

                    return false;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ScheduledTaskManager.ExecuteTaskNow");
                return false;
            }
        }

        /// <summary>
        /// معالج حدث المؤقت
        /// </summary>
        private async void OnTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            try
            {
                await PerformanceHelper.MeasureExecutionTimeAsync("ScheduledTaskManager.OnTimerElapsed", async () =>
                {
                    List<ScheduledTask> tasksToExecute;
                    lock (_lockObject)
                    {
                        tasksToExecute = _tasks.Where(t => t.IsEnabled && ShouldExecuteTask(t)).ToList();
                    }

                    foreach (var task in tasksToExecute)
                    {
                        try
                        {
                            await ExecuteTaskAsync(task);
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"[SCHEDULER] فشل تنفيذ المهمة {task.Name}: {ex.Message}");
                        }
                    }

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ScheduledTaskManager.OnTimerElapsed");
            }
        }

        /// <summary>
        /// التحقق من ضرورة تنفيذ المهمة
        /// </summary>
        /// <param name="task">المهمة</param>
        /// <returns>هل يجب تنفيذ المهمة</returns>
        private bool ShouldExecuteTask(ScheduledTask task)
        {
            try
            {
                var now = DateTime.Now;

                // التحقق من الوقت المحدد
                if (task.ScheduleType == ScheduleType.Daily)
                {
                    return now.Hour == task.ScheduleTime.Hour && 
                           now.Minute == task.ScheduleTime.Minute &&
                           (task.LastExecuted == null || task.LastExecuted.Value.Date < now.Date);
                }
                else if (task.ScheduleType == ScheduleType.Weekly)
                {
                    return now.DayOfWeek == task.DayOfWeek &&
                           now.Hour == task.ScheduleTime.Hour && 
                           now.Minute == task.ScheduleTime.Minute &&
                           (task.LastExecuted == null || (now - task.LastExecuted.Value).TotalDays >= 7);
                }
                else if (task.ScheduleType == ScheduleType.Monthly)
                {
                    return now.Day == task.DayOfMonth &&
                           now.Hour == task.ScheduleTime.Hour && 
                           now.Minute == task.ScheduleTime.Minute &&
                           (task.LastExecuted == null || task.LastExecuted.Value.Month != now.Month);
                }
                else if (task.ScheduleType == ScheduleType.Interval)
                {
                    return task.LastExecuted == null || 
                           (now - task.LastExecuted.Value).TotalMinutes >= task.IntervalMinutes;
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SCHEDULER] خطأ في التحقق من ضرورة تنفيذ المهمة {task.Name}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنفيذ المهمة
        /// </summary>
        /// <param name="task">المهمة</param>
        /// <returns>نجاح التنفيذ</returns>
        private async Task<bool> ExecuteTaskAsync(ScheduledTask task)
        {
            try
            {
                Debug.WriteLine($"[SCHEDULER] بدء تنفيذ المهمة: {task.Name}");

                task.LastExecuted = DateTime.Now;
                task.ExecutionCount++;

                bool result = false;

                switch (task.TaskType)
                {
                    case TaskType.Backup:
                        result = await ExecuteBackupTaskAsync(task);
                        break;
                    case TaskType.Report:
                        result = await ExecuteReportTaskAsync(task);
                        break;
                    case TaskType.Cleanup:
                        result = await ExecuteCleanupTaskAsync(task);
                        break;
                    case TaskType.Notification:
                        result = await ExecuteNotificationTaskAsync(task);
                        break;
                    case TaskType.Custom:
                        result = await ExecuteCustomTaskAsync(task);
                        break;
                }

                if (result)
                {
                    task.LastSuccessfulExecution = DateTime.Now;
                    Debug.WriteLine($"[SCHEDULER] تم تنفيذ المهمة بنجاح: {task.Name}");
                }
                else
                {
                    task.FailureCount++;
                    Debug.WriteLine($"[SCHEDULER] فشل تنفيذ المهمة: {task.Name}");
                }

                return result;
            }
            catch (Exception ex)
            {
                task.FailureCount++;
                Debug.WriteLine($"[SCHEDULER] خطأ في تنفيذ المهمة {task.Name}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنفيذ مهمة النسخ الاحتياطي
        /// </summary>
        private async Task<bool> ExecuteBackupTaskAsync(ScheduledTask task)
        {
            try
            {
                using (var context = new AppDbContext())
                {
                    var backupManager = new BackupManager(context);
                    var result = await backupManager.CreateBackupAsync(true, true, true);
                    return result.Success;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SCHEDULER] فشل تنفيذ مهمة النسخ الاحتياطي: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنفيذ مهمة التقرير
        /// </summary>
        private async Task<bool> ExecuteReportTaskAsync(ScheduledTask task)
        {
            try
            {
                using (var context = new AppDbContext())
                {
                    var reportGenerator = new AdvancedReportGenerator(context);
                    
                    // إنشاء تقرير حسب نوع المهمة
                    if (task.Parameters.ContainsKey("ReportType"))
                    {
                        string reportType = task.Parameters["ReportType"];
                        
                        if (reportType == "IncomeStatement")
                        {
                            var report = await reportGenerator.GenerateIncomeStatementAsync(
                                DateTime.Now.AddMonths(-1), DateTime.Now);
                            
                            // إرسال التقرير بالبريد الإلكتروني
                            if (task.Parameters.ContainsKey("Recipients"))
                            {
                                var recipients = task.Parameters["Recipients"].Split(',').ToList();
                                await NotificationService.Instance.SendPeriodicReportAsync(
                                    reportType, recipients, report);
                            }
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SCHEDULER] فشل تنفيذ مهمة التقرير: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنفيذ مهمة التنظيف
        /// </summary>
        private async Task<bool> ExecuteCleanupTaskAsync(ScheduledTask task)
        {
            try
            {
                // تنظيف الملفات المؤقتة
                string tempPath = Path.Combine(App.AppDataPath, "Temp");
                int deletedFiles = FileSystemHelper.CleanupTempFiles(tempPath, 7);

                // تنظيف المرفقات غير المستخدمة
                using (var context = new AppDbContext())
                {
                    var attachmentManager = new AttachmentManager(context);
                    int deletedAttachments = await attachmentManager.CleanupUnusedAttachmentsAsync();
                }

                Debug.WriteLine($"[SCHEDULER] تم تنظيف {deletedFiles} ملف مؤقت");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SCHEDULER] فشل تنفيذ مهمة التنظيف: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنفيذ مهمة الإشعار
        /// </summary>
        private async Task<bool> ExecuteNotificationTaskAsync(ScheduledTask task)
        {
            try
            {
                if (task.Parameters.ContainsKey("Message") && task.Parameters.ContainsKey("Recipients"))
                {
                    string message = task.Parameters["Message"];
                    var recipients = task.Parameters["Recipients"].Split(',').ToList();

                    foreach (var recipient in recipients)
                    {
                        await NotificationService.Instance.SendEmailAsync(
                            recipient.Trim(), "إشعار مجدول", message);
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SCHEDULER] فشل تنفيذ مهمة الإشعار: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنفيذ مهمة مخصصة
        /// </summary>
        private async Task<bool> ExecuteCustomTaskAsync(ScheduledTask task)
        {
            try
            {
                // تنفيذ مهمة مخصصة حسب المعاملات
                if (task.CustomAction != null)
                {
                    await task.CustomAction();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SCHEDULER] فشل تنفيذ المهمة المخصصة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحميل المهام الافتراضية
        /// </summary>
        private void LoadDefaultTasks()
        {
            try
            {
                // مهمة النسخ الاحتياطي اليومي
                var dailyBackupTask = new ScheduledTask
                {
                    Name = "DailyBackup",
                    Description = "نسخة احتياطية يومية",
                    TaskType = TaskType.Backup,
                    ScheduleType = ScheduleType.Daily,
                    ScheduleTime = new TimeSpan(2, 0, 0), // 2:00 صباحاً
                    IsEnabled = true
                };
                _tasks.Add(dailyBackupTask);

                // مهمة تنظيف أسبوعية
                var weeklyCleanupTask = new ScheduledTask
                {
                    Name = "WeeklyCleanup",
                    Description = "تنظيف أسبوعي للملفات المؤقتة",
                    TaskType = TaskType.Cleanup,
                    ScheduleType = ScheduleType.Weekly,
                    DayOfWeek = DayOfWeek.Sunday,
                    ScheduleTime = new TimeSpan(3, 0, 0), // 3:00 صباحاً يوم الأحد
                    IsEnabled = true
                };
                _tasks.Add(weeklyCleanupTask);

                Debug.WriteLine("[SCHEDULER] تم تحميل المهام الافتراضية.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SCHEDULER] فشل تحميل المهام الافتراضية: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// المهمة المجدولة
    /// </summary>
    public class ScheduledTask
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public TaskType TaskType { get; set; }
        public ScheduleType ScheduleType { get; set; }
        public TimeSpan ScheduleTime { get; set; }
        public DayOfWeek DayOfWeek { get; set; }
        public int DayOfMonth { get; set; } = 1;
        public int IntervalMinutes { get; set; } = 60;
        public bool IsEnabled { get; set; } = true;
        public DateTime? LastExecuted { get; set; }
        public DateTime? LastSuccessfulExecution { get; set; }
        public int ExecutionCount { get; set; }
        public int FailureCount { get; set; }
        public Dictionary<string, string> Parameters { get; set; } = new Dictionary<string, string>();
        public Func<Task>? CustomAction { get; set; }
    }

    /// <summary>
    /// نوع المهمة
    /// </summary>
    public enum TaskType
    {
        Backup,
        Report,
        Cleanup,
        Notification,
        Custom
    }

    /// <summary>
    /// نوع الجدولة
    /// </summary>
    public enum ScheduleType
    {
        Daily,
        Weekly,
        Monthly,
        Interval
    }
}
