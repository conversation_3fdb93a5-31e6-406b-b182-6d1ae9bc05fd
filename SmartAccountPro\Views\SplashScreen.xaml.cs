using System;
using System.Windows;
using System.Windows.Threading;

namespace SmartAccountPro.Views
{
    /// <summary>
    /// شاشة البداية للتطبيق
    /// </summary>
    public partial class SplashScreen : Window
    {
        private DispatcherTimer _timer;

        public SplashScreen()
        {
            InitializeComponent();
            
            // إنشاء مؤقت لإغلاق شاشة البداية بعد 3 ثواني
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(3)
            };
            _timer.Tick += Timer_Tick;
            _timer.Start();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            _timer.Stop();
            
            // فتح نافذة تسجيل الدخول
            var loginWindow = new LoginWindow();
            loginWindow.Show();
            
            // إغلاق شاشة البداية
            this.Close();
        }
    }
}
