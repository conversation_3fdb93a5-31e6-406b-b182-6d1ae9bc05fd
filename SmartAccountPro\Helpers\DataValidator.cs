using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للتحقق من صحة البيانات
    /// </summary>
    public static class DataValidator
    {
        /// <summary>
        /// التحقق من صحة رقم الحساب
        /// </summary>
        /// <param name="accountNumber">رقم الحساب</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateAccountNumber(string accountNumber)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(accountNumber))
                {
                    return new ValidationResult(false, "رقم الحساب مطلوب.");
                }

                // التحقق من الطول
                if (accountNumber.Length < 5 || accountNumber.Length > 20)
                {
                    return new ValidationResult(false, "يجب أن يكون طول رقم الحساب بين 5 و 20 حرفاً.");
                }

                // التحقق من الأحرف المسموح بها
                if (!Regex.IsMatch(accountNumber, @"^[a-zA-Z0-9-]+$"))
                {
                    return new ValidationResult(false, "يجب أن يحتوي رقم الحساب على أحرف إنجليزية وأرقام ورمز - فقط.");
                }

                return new ValidationResult(true, "رقم الحساب صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataValidator.ValidateAccountNumber");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من رقم الحساب.");
            }
        }

        /// <summary>
        /// التحقق من صحة رقم الفاتورة
        /// </summary>
        /// <param name="invoiceNumber">رقم الفاتورة</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateInvoiceNumber(string invoiceNumber)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(invoiceNumber))
                {
                    return new ValidationResult(false, "رقم الفاتورة مطلوب.");
                }

                // التحقق من الطول
                if (invoiceNumber.Length < 3 || invoiceNumber.Length > 20)
                {
                    return new ValidationResult(false, "يجب أن يكون طول رقم الفاتورة بين 3 و 20 حرفاً.");
                }

                // التحقق من الأحرف المسموح بها
                if (!Regex.IsMatch(invoiceNumber, @"^[a-zA-Z0-9-/]+$"))
                {
                    return new ValidationResult(false, "يجب أن يحتوي رقم الفاتورة على أحرف إنجليزية وأرقام ورموز - / فقط.");
                }

                return new ValidationResult(true, "رقم الفاتورة صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataValidator.ValidateInvoiceNumber");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من رقم الفاتورة.");
            }
        }

        /// <summary>
        /// التحقق من صحة رقم القيد
        /// </summary>
        /// <param name="entryNumber">رقم القيد</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateEntryNumber(string entryNumber)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(entryNumber))
                {
                    return new ValidationResult(false, "رقم القيد مطلوب.");
                }

                // التحقق من الطول
                if (entryNumber.Length < 3 || entryNumber.Length > 20)
                {
                    return new ValidationResult(false, "يجب أن يكون طول رقم القيد بين 3 و 20 حرفاً.");
                }

                // التحقق من الأحرف المسموح بها
                if (!Regex.IsMatch(entryNumber, @"^[a-zA-Z0-9-/]+$"))
                {
                    return new ValidationResult(false, "يجب أن يحتوي رقم القيد على أحرف إنجليزية وأرقام ورموز - / فقط.");
                }

                return new ValidationResult(true, "رقم القيد صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataValidator.ValidateEntryNumber");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من رقم القيد.");
            }
        }

        /// <summary>
        /// التحقق من صحة رقم العميل
        /// </summary>
        /// <param name="customerNumber">رقم العميل</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateCustomerNumber(string customerNumber)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(customerNumber))
                {
                    return new ValidationResult(false, "رقم العميل مطلوب.");
                }

                // التحقق من الطول
                if (customerNumber.Length < 3 || customerNumber.Length > 20)
                {
                    return new ValidationResult(false, "يجب أن يكون طول رقم العميل بين 3 و 20 حرفاً.");
                }

                // التحقق من الأحرف المسموح بها
                if (!Regex.IsMatch(customerNumber, @"^[a-zA-Z0-9-]+$"))
                {
                    return new ValidationResult(false, "يجب أن يحتوي رقم العميل على أحرف إنجليزية وأرقام ورمز - فقط.");
                }

                return new ValidationResult(true, "رقم العميل صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataValidator.ValidateCustomerNumber");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من رقم العميل.");
            }
        }

        /// <summary>
        /// التحقق من صحة رقم المنتج
        /// </summary>
        /// <param name="productNumber">رقم المنتج</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateProductNumber(string productNumber)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(productNumber))
                {
                    return new ValidationResult(false, "رقم المنتج مطلوب.");
                }

                // التحقق من الطول
                if (productNumber.Length < 3 || productNumber.Length > 20)
                {
                    return new ValidationResult(false, "يجب أن يكون طول رقم المنتج بين 3 و 20 حرفاً.");
                }

                // التحقق من الأحرف المسموح بها
                if (!Regex.IsMatch(productNumber, @"^[a-zA-Z0-9-]+$"))
                {
                    return new ValidationResult(false, "يجب أن يحتوي رقم المنتج على أحرف إنجليزية وأرقام ورمز - فقط.");
                }

                return new ValidationResult(true, "رقم المنتج صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataValidator.ValidateProductNumber");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من رقم المنتج.");
            }
        }

        /// <summary>
        /// التحقق من صحة الرقم الضريبي
        /// </summary>
        /// <param name="taxNumber">الرقم الضريبي</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateTaxNumber(string taxNumber)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(taxNumber))
                {
                    return new ValidationResult(true, "الرقم الضريبي اختياري.");
                }

                // التحقق من الطول
                if (taxNumber.Length < 5 || taxNumber.Length > 20)
                {
                    return new ValidationResult(false, "يجب أن يكون طول الرقم الضريبي بين 5 و 20 حرفاً.");
                }

                // التحقق من الأحرف المسموح بها
                if (!Regex.IsMatch(taxNumber, @"^[a-zA-Z0-9-]+$"))
                {
                    return new ValidationResult(false, "يجب أن يحتوي الرقم الضريبي على أحرف إنجليزية وأرقام ورمز - فقط.");
                }

                return new ValidationResult(true, "الرقم الضريبي صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataValidator.ValidateTaxNumber");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من الرقم الضريبي.");
            }
        }

        /// <summary>
        /// التحقق من صحة رقم الهوية
        /// </summary>
        /// <param name="identityNumber">رقم الهوية</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateIdentityNumber(string identityNumber)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(identityNumber))
                {
                    return new ValidationResult(true, "رقم الهوية اختياري.");
                }

                // التحقق من الطول
                if (identityNumber.Length < 5 || identityNumber.Length > 20)
                {
                    return new ValidationResult(false, "يجب أن يكون طول رقم الهوية بين 5 و 20 حرفاً.");
                }

                // التحقق من الأحرف المسموح بها
                if (!Regex.IsMatch(identityNumber, @"^[a-zA-Z0-9-]+$"))
                {
                    return new ValidationResult(false, "يجب أن يحتوي رقم الهوية على أحرف إنجليزية وأرقام ورمز - فقط.");
                }

                return new ValidationResult(true, "رقم الهوية صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataValidator.ValidateIdentityNumber");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من رقم الهوية.");
            }
        }

        /// <summary>
        /// التحقق من صحة رقم الحساب البنكي
        /// </summary>
        /// <param name="bankAccountNumber">رقم الحساب البنكي</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateBankAccountNumber(string bankAccountNumber)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(bankAccountNumber))
                {
                    return new ValidationResult(true, "رقم الحساب البنكي اختياري.");
                }

                // التحقق من الطول
                if (bankAccountNumber.Length < 5 || bankAccountNumber.Length > 30)
                {
                    return new ValidationResult(false, "يجب أن يكون طول رقم الحساب البنكي بين 5 و 30 حرفاً.");
                }

                // التحقق من الأحرف المسموح بها
                if (!Regex.IsMatch(bankAccountNumber, @"^[a-zA-Z0-9-]+$"))
                {
                    return new ValidationResult(false, "يجب أن يحتوي رقم الحساب البنكي على أحرف إنجليزية وأرقام ورمز - فقط.");
                }

                return new ValidationResult(true, "رقم الحساب البنكي صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataValidator.ValidateBankAccountNumber");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من رقم الحساب البنكي.");
            }
        }
    }
}
