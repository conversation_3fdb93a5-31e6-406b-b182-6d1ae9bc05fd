using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// يمثل حساب في شجرة الحسابات المحاسبية
    /// </summary>
    public class Account
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(20)]
        public string Code { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [MaxLength(255)]
        public string Description { get; set; }

        /// <summary>
        /// نوع الحساب: أصول، خصوم، إيرادات، مصروفات، حقوق ملكية
        /// </summary>
        public AccountType Type { get; set; }

        /// <summary>
        /// الحساب الأب (إذا كان حساب فرعي)
        /// </summary>
        public int? ParentAccountId { get; set; }
        public virtual Account ParentAccount { get; set; }

        /// <summary>
        /// الحسابات الفرعية
        /// </summary>
        public virtual ICollection<Account> ChildAccounts { get; set; }

        /// <summary>
        /// الرصيد الحالي للحساب
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// هل الحساب نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ إنشاء الحساب
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث للحساب
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// المستخدم الذي أنشأ الحساب
        /// </summary>
        public int? CreatedBy { get; set; }

        public Account()
        {
            ChildAccounts = new HashSet<Account>();
        }
    }

    /// <summary>
    /// أنواع الحسابات المحاسبية
    /// </summary>
    public enum AccountType
    {
        /// <summary>
        /// أصول
        /// </summary>
        Asset = 1,
        
        /// <summary>
        /// خصوم
        /// </summary>
        Liability = 2,
        
        /// <summary>
        /// إيرادات
        /// </summary>
        Revenue = 3,
        
        /// <summary>
        /// مصروفات
        /// </summary>
        Expense = 4,
        
        /// <summary>
        /// حقوق ملكية
        /// </summary>
        Equity = 5
    }
}
