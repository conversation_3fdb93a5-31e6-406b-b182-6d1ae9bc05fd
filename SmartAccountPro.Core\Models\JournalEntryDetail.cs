using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// تفاصيل القيد المحاسبي
    /// </summary>
    public class JournalEntryDetail
    {
        /// <summary>
        /// معرف التفصيل
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف القيد المحاسبي
        /// </summary>
        [Required]
        public int JournalEntryId { get; set; }

        /// <summary>
        /// القيد المحاسبي
        /// </summary>
        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry? JournalEntry { get; set; }

        /// <summary>
        /// معرف الحساب
        /// </summary>
        [Required]
        public int AccountId { get; set; }

        /// <summary>
        /// الحساب
        /// </summary>
        [ForeignKey("AccountId")]
        public virtual Account? Account { get; set; }

        /// <summary>
        /// وصف التفصيل
        /// </summary>
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// المبلغ المدين
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitAmount { get; set; }

        /// <summary>
        /// المبلغ الدائن
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditAmount { get; set; }

        /// <summary>
        /// ترتيب التفصيل في القيد
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
}
