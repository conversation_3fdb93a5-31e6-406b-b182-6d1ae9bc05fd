using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة سجل التدقيق
    /// </summary>
    public static class AuditTrailManager
    {
        private static readonly string _auditTrailPath;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// إنشاء مدير سجل التدقيق
        /// </summary>
        static AuditTrailManager()
        {
            try
            {
                // تحديد مسار سجل التدقيق
                _auditTrailPath = Path.Combine(App.AppDataPath, "AuditTrail");

                // إنشاء مجلد سجل التدقيق إذا لم يكن موجوداً
                if (!Directory.Exists(_auditTrailPath))
                {
                    Directory.CreateDirectory(_auditTrailPath);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AuditTrailManager.Constructor");
            }
        }

        /// <summary>
        /// تسجيل حدث في سجل التدقيق
        /// </summary>
        /// <param name="action">الإجراء</param>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <param name="details">التفاصيل</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نجاح العملية</returns>
        public static bool LogAction(string action, string entityType, int entityId, string details, int userId)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("AuditTrailManager.LogAction", () =>
                {
                    // إنشاء سجل التدقيق
                    var auditTrail = new AuditTrail
                    {
                        Action = action,
                        EntityType = entityType,
                        EntityId = entityId,
                        Details = details,
                        UserId = userId,
                        Timestamp = DateTime.Now,
                        IpAddress = GetIpAddress(),
                        UserAgent = GetUserAgent()
                    };

                    // حفظ سجل التدقيق
                    return SaveAuditTrail(auditTrail);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AuditTrailManager.LogAction");
                return false;
            }
        }

        /// <summary>
        /// تسجيل حدث تسجيل الدخول في سجل التدقيق
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="success">نجاح العملية</param>
        /// <returns>نجاح العملية</returns>
        public static bool LogLogin(int userId, string username, bool success)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("AuditTrailManager.LogLogin", () =>
                {
                    // إنشاء سجل التدقيق
                    var auditTrail = new AuditTrail
                    {
                        Action = success ? "تسجيل الدخول" : "فشل تسجيل الدخول",
                        EntityType = "User",
                        EntityId = userId,
                        Details = $"اسم المستخدم: {username}",
                        UserId = userId,
                        Timestamp = DateTime.Now,
                        IpAddress = GetIpAddress(),
                        UserAgent = GetUserAgent()
                    };

                    // حفظ سجل التدقيق
                    return SaveAuditTrail(auditTrail);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AuditTrailManager.LogLogin");
                return false;
            }
        }

        /// <summary>
        /// تسجيل حدث تسجيل الخروج في سجل التدقيق
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>نجاح العملية</returns>
        public static bool LogLogout(int userId, string username)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("AuditTrailManager.LogLogout", () =>
                {
                    // إنشاء سجل التدقيق
                    var auditTrail = new AuditTrail
                    {
                        Action = "تسجيل الخروج",
                        EntityType = "User",
                        EntityId = userId,
                        Details = $"اسم المستخدم: {username}",
                        UserId = userId,
                        Timestamp = DateTime.Now,
                        IpAddress = GetIpAddress(),
                        UserAgent = GetUserAgent()
                    };

                    // حفظ سجل التدقيق
                    return SaveAuditTrail(auditTrail);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AuditTrailManager.LogLogout");
                return false;
            }
        }

        /// <summary>
        /// تسجيل حدث إنشاء كيان في سجل التدقيق
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <param name="details">التفاصيل</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نجاح العملية</returns>
        public static bool LogCreate(string entityType, int entityId, string details, int userId)
        {
            return LogAction("إنشاء", entityType, entityId, details, userId);
        }

        /// <summary>
        /// تسجيل حدث تحديث كيان في سجل التدقيق
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <param name="details">التفاصيل</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نجاح العملية</returns>
        public static bool LogUpdate(string entityType, int entityId, string details, int userId)
        {
            return LogAction("تحديث", entityType, entityId, details, userId);
        }

        /// <summary>
        /// تسجيل حدث حذف كيان في سجل التدقيق
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <param name="details">التفاصيل</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نجاح العملية</returns>
        public static bool LogDelete(string entityType, int entityId, string details, int userId)
        {
            return LogAction("حذف", entityType, entityId, details, userId);
        }

        /// <summary>
        /// تسجيل حدث عرض كيان في سجل التدقيق
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <param name="details">التفاصيل</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نجاح العملية</returns>
        public static bool LogView(string entityType, int entityId, string details, int userId)
        {
            return LogAction("عرض", entityType, entityId, details, userId);
        }

        /// <summary>
        /// تسجيل حدث طباعة كيان في سجل التدقيق
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <param name="details">التفاصيل</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نجاح العملية</returns>
        public static bool LogPrint(string entityType, int entityId, string details, int userId)
        {
            return LogAction("طباعة", entityType, entityId, details, userId);
        }

        /// <summary>
        /// تسجيل حدث تصدير كيان في سجل التدقيق
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <param name="details">التفاصيل</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نجاح العملية</returns>
        public static bool LogExport(string entityType, int entityId, string details, int userId)
        {
            return LogAction("تصدير", entityType, entityId, details, userId);
        }

        /// <summary>
        /// تسجيل حدث استيراد كيان في سجل التدقيق
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <param name="details">التفاصيل</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نجاح العملية</returns>
        public static bool LogImport(string entityType, int entityId, string details, int userId)
        {
            return LogAction("استيراد", entityType, entityId, details, userId);
        }

        /// <summary>
        /// الحصول على سجل التدقيق
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="action">الإجراء</param>
        /// <returns>سجل التدقيق</returns>
        public static List<AuditTrail> GetAuditTrail(DateTime? startDate = null, DateTime? endDate = null, int? userId = null, string? entityType = null, string? action = null)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("AuditTrailManager.GetAuditTrail", () =>
                {
                    // تحديد تاريخ البداية والنهاية
                    startDate ??= DateTime.Now.AddDays(-30);
                    endDate ??= DateTime.Now;

                    // الحصول على ملفات سجل التدقيق
                    var auditTrailFiles = GetAuditTrailFiles(startDate.Value, endDate.Value);

                    // قراءة سجلات التدقيق
                    var auditTrails = new List<AuditTrail>();
                    foreach (var file in auditTrailFiles)
                    {
                        try
                        {
                            // قراءة ملف سجل التدقيق
                            var fileContent = File.ReadAllText(file);
                            var fileAuditTrails = JsonConvert.DeserializeObject<List<AuditTrail>>(fileContent);

                            if (fileAuditTrails != null)
                            {
                                auditTrails.AddRange(fileAuditTrails);
                            }
                        }
                        catch (Exception ex)
                        {
                            ExceptionHandler.HandleException(ex, $"AuditTrailManager.GetAuditTrail.ReadFile: {file}");
                        }
                    }

                    // تصفية سجلات التدقيق
                    var filteredAuditTrails = auditTrails.Where(a =>
                        a.Timestamp >= startDate &&
                        a.Timestamp <= endDate &&
                        (userId == null || a.UserId == userId) &&
                        (string.IsNullOrEmpty(entityType) || a.EntityType == entityType) &&
                        (string.IsNullOrEmpty(action) || a.Action == action)
                    ).OrderByDescending(a => a.Timestamp).ToList();

                    return filteredAuditTrails;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AuditTrailManager.GetAuditTrail");
                return new List<AuditTrail>();
            }
        }

        /// <summary>
        /// حفظ سجل التدقيق
        /// </summary>
        /// <param name="auditTrail">سجل التدقيق</param>
        /// <returns>نجاح العملية</returns>
        private static bool SaveAuditTrail(AuditTrail auditTrail)
        {
            try
            {
                // تحديد مسار ملف سجل التدقيق
                var fileName = $"AuditTrail_{auditTrail.Timestamp:yyyy-MM-dd}.json";
                var filePath = Path.Combine(_auditTrailPath, fileName);

                // قراءة سجلات التدقيق الحالية
                List<AuditTrail> auditTrails = new List<AuditTrail>();
                if (File.Exists(filePath))
                {
                    try
                    {
                        var fileContent = File.ReadAllText(filePath);
                        auditTrails = JsonConvert.DeserializeObject<List<AuditTrail>>(fileContent) ?? new List<AuditTrail>();
                    }
                    catch (Exception ex)
                    {
                        ExceptionHandler.HandleException(ex, $"AuditTrailManager.SaveAuditTrail.ReadFile: {filePath}");
                    }
                }

                // إضافة سجل التدقيق الجديد
                auditTrails.Add(auditTrail);

                // حفظ سجلات التدقيق
                lock (_lockObject)
                {
                    var json = JsonConvert.SerializeObject(auditTrails, Formatting.Indented);
                    File.WriteAllText(filePath, json);
                }

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AuditTrailManager.SaveAuditTrail");
                return false;
            }
        }

        /// <summary>
        /// الحصول على ملفات سجل التدقيق
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>ملفات سجل التدقيق</returns>
        private static List<string> GetAuditTrailFiles(DateTime startDate, DateTime endDate)
        {
            try
            {
                // الحصول على جميع ملفات سجل التدقيق
                var allFiles = Directory.GetFiles(_auditTrailPath, "AuditTrail_*.json");

                // تصفية الملفات حسب التاريخ
                var filteredFiles = new List<string>();
                foreach (var file in allFiles)
                {
                    try
                    {
                        // استخراج تاريخ الملف
                        var fileName = Path.GetFileNameWithoutExtension(file);
                        var dateString = fileName.Replace("AuditTrail_", "");
                        if (DateTime.TryParse(dateString, out var fileDate))
                        {
                            // التحقق من أن تاريخ الملف ضمن النطاق المطلوب
                            if (fileDate.Date >= startDate.Date && fileDate.Date <= endDate.Date)
                            {
                                filteredFiles.Add(file);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        ExceptionHandler.HandleException(ex, $"AuditTrailManager.GetAuditTrailFiles.ParseDate: {file}");
                    }
                }

                return filteredFiles;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AuditTrailManager.GetAuditTrailFiles");
                return new List<string>();
            }
        }

        /// <summary>
        /// الحصول على عنوان IP
        /// </summary>
        /// <returns>عنوان IP</returns>
        private static string GetIpAddress()
        {
            try
            {
                // محاكاة للحصول على عنوان IP
                return "127.0.0.1";
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AuditTrailManager.GetIpAddress");
                return "unknown";
            }
        }

        /// <summary>
        /// الحصول على وكيل المستخدم
        /// </summary>
        /// <returns>وكيل المستخدم</returns>
        private static string GetUserAgent()
        {
            try
            {
                // محاكاة للحصول على وكيل المستخدم
                return "SmartAccountPro Desktop Application";
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AuditTrailManager.GetUserAgent");
                return "unknown";
            }
        }
    }

    /// <summary>
    /// سجل التدقيق
    /// </summary>
    public class AuditTrail
    {
        /// <summary>
        /// الإجراء
        /// </summary>
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// نوع الكيان
        /// </summary>
        public string EntityType { get; set; } = string.Empty;

        /// <summary>
        /// معرف الكيان
        /// </summary>
        public int EntityId { get; set; }

        /// <summary>
        /// التفاصيل
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// الطابع الزمني
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// عنوان IP
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// وكيل المستخدم
        /// </summary>
        public string UserAgent { get; set; } = string.Empty;
    }
}
