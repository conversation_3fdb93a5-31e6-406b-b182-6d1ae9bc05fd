using SmartAccountPro.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SmartAccountPro.Data.Repositories
{
    /// <summary>
    /// واجهة مستودع الحسابات
    /// </summary>
    public interface IAccountRepository : IRepository<Account>
    {
        /// <summary>
        /// الحصول على حساب بواسطة الكود
        /// </summary>
        Task<Account> GetByCodeAsync(string code);

        /// <summary>
        /// الحصول على الحسابات الرئيسية
        /// </summary>
        Task<IEnumerable<Account>> GetRootAccountsAsync();

        /// <summary>
        /// الحصول على الحسابات الفرعية لحساب معين
        /// </summary>
        Task<IEnumerable<Account>> GetChildAccountsAsync(int parentAccountId);

        /// <summary>
        /// الحصول على شجرة الحسابات
        /// </summary>
        Task<IEnumerable<Account>> GetAccountTreeAsync();

        /// <summary>
        /// الحصول على ميزان المراجعة
        /// </summary>
        Task<IEnumerable<Account>> GetTrialBalanceAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// تحديث رصيد الحساب
        /// </summary>
        Task<bool> UpdateBalanceAsync(int accountId, decimal amount);
    }
}
