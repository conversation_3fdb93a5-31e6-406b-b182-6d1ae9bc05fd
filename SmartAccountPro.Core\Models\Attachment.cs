using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// نموذج المرفق
    /// </summary>
    public class Attachment
    {
        /// <summary>
        /// معرف المرفق
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// نوع الكيان المرتبط بالمرفق
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string EntityType { get; set; } = string.Empty;

        /// <summary>
        /// معرف الكيان المرتبط بالمرفق
        /// </summary>
        [Required]
        public int EntityId { get; set; }

        /// <summary>
        /// اسم الملف الأصلي
        /// </summary>
        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// حجم الملف بالبايت
        /// </summary>
        [Required]
        public long FileSize { get; set; }

        /// <summary>
        /// نوع الملف
        /// </summary>
        [Required]
        [MaxLength(10)]
        public string FileType { get; set; } = string.Empty;

        /// <summary>
        /// مسار الملف على القرص
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// تجزئة MD5 للملف
        /// </summary>
        [MaxLength(32)]
        public string FileHash { get; set; } = string.Empty;

        /// <summary>
        /// وصف المرفق
        /// </summary>
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الرفع
        /// </summary>
        [Required]
        public DateTime UploadedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي رفع الملف
        /// </summary>
        [Required]
        public int UploadedBy { get; set; }

        /// <summary>
        /// المستخدم الذي رفع الملف
        /// </summary>
        [ForeignKey("UploadedBy")]
        public virtual User? User { get; set; }
    }
}
