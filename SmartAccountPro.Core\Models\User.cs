using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// يمثل مستخدم في النظام
    /// </summary>
    public class User
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string Username { get; set; }

        [Required]
        [MaxLength(100)]
        public string FullName { get; set; }

        [Required]
        [MaxLength(100)]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        [MaxLength(100)]
        public string PasswordHash { get; set; }

        [MaxLength(100)]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// هل المستخدم نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// آخر تسجيل دخول
        /// </summary>
        public DateTime? LastLogin { get; set; }

        /// <summary>
        /// تاريخ إنشاء الحساب
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث للحساب
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// أدوار المستخدم
        /// </summary>
        public virtual ICollection<UserRole> UserRoles { get; set; }

        public User()
        {
            UserRoles = new HashSet<UserRole>();
        }
    }

    /// <summary>
    /// يمثل دور في النظام
    /// </summary>
    public class Role
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string Name { get; set; }

        [MaxLength(255)]
        public string Description { get; set; }

        /// <summary>
        /// المستخدمين في هذا الدور
        /// </summary>
        public virtual ICollection<UserRole> UserRoles { get; set; }

        /// <summary>
        /// صلاحيات هذا الدور
        /// </summary>
        public virtual ICollection<RolePermission> RolePermissions { get; set; }

        public Role()
        {
            UserRoles = new HashSet<UserRole>();
            RolePermissions = new HashSet<RolePermission>();
        }
    }

    /// <summary>
    /// علاقة بين المستخدم والدور
    /// </summary>
    public class UserRole
    {
        public int UserId { get; set; }
        public virtual User User { get; set; }

        public int RoleId { get; set; }
        public virtual Role Role { get; set; }
    }

    /// <summary>
    /// يمثل صلاحية في النظام
    /// </summary>
    public class Permission
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string Name { get; set; }

        [MaxLength(255)]
        public string Description { get; set; }

        /// <summary>
        /// الأدوار التي تملك هذه الصلاحية
        /// </summary>
        public virtual ICollection<RolePermission> RolePermissions { get; set; }

        public Permission()
        {
            RolePermissions = new HashSet<RolePermission>();
        }
    }

    /// <summary>
    /// علاقة بين الدور والصلاحية
    /// </summary>
    public class RolePermission
    {
        public int RoleId { get; set; }
        public virtual Role Role { get; set; }

        public int PermissionId { get; set; }
        public virtual Permission Permission { get; set; }
    }
}
