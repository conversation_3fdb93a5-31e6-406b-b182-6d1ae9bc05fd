<Window x:Class="SmartAccountPro.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SmartAccountPro.Views"
        xmlns:vm="clr-namespace:SmartAccountPro.ViewModels"
        mc:Ignorable="d"
        Title="تسجيل الدخول - SmartAccount Pro"
        Height="500"
        Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="Transparent"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.DataContext>
        <vm:LoginViewModel />
    </Window.DataContext>

    <Border CornerRadius="20" BorderThickness="1" BorderBrush="#2196F3">
        <Border.Effect>
            <DropShadowEffect ShadowDepth="3" BlurRadius="15" Opacity="0.3" Color="#000000"/>
        </Border.Effect>
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#FFFFFF" Offset="0"/>
                <GradientStop Color="#F5F5F5" Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="350"/>
            </Grid.ColumnDefinitions>

            <!-- الجانب الأيسر - صورة وشعار -->
            <Border Grid.Column="0" CornerRadius="20,0,0,20">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#1A237E" Offset="0"/>
                        <GradientStop Color="#0D47A1" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>

                <Grid>
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <Image Source="/Resources/Images/logo.png" Width="150" Height="150" Margin="0,0,0,20">
                            <Image.Effect>
                                <DropShadowEffect ShadowDepth="2" BlurRadius="10" Opacity="0.5" Color="#000000"/>
                            </Image.Effect>
                        </Image>
                        <TextBlock Text="SmartAccount Pro"
                                   FontSize="32"
                                   FontWeight="Bold"
                                   Foreground="White"
                                   HorizontalAlignment="Center">
                            <TextBlock.Effect>
                                <DropShadowEffect ShadowDepth="2" BlurRadius="5" Opacity="0.5" Color="#000000"/>
                            </TextBlock.Effect>
                        </TextBlock>
                        <TextBlock Text="نظام المحاسبة الذكي"
                                   FontSize="20"
                                   Foreground="#BBDEFB"
                                   HorizontalAlignment="Center"
                                   Margin="0,10,0,0">
                            <TextBlock.Effect>
                                <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.5" Color="#000000"/>
                            </TextBlock.Effect>
                        </TextBlock>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
            <Grid Grid.Column="1">
                <Button x:Name="CloseButton"
                        Content="✕"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Margin="0,10,10,0"
                        Width="30"
                        Height="30"
                        Background="Transparent"
                        Foreground="#333333"
                        BorderThickness="0"
                        FontSize="16"
                        Click="CloseButton_Click"/>

                <StackPanel VerticalAlignment="Center" Margin="30,0">
                    <TextBlock Text="تسجيل الدخول"
                               FontSize="24"
                               FontWeight="Bold"
                               Foreground="#333333"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,30"/>

                    <TextBlock Text="اسم المستخدم"
                               FontSize="14"
                               Foreground="#555555"
                               Margin="0,0,0,5"/>
                    <Border CornerRadius="5" BorderThickness="1" BorderBrush="#DDDDDD">
                        <TextBox Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                                 Height="40"
                                 Padding="10,0"
                                 VerticalContentAlignment="Center"
                                 BorderThickness="0"
                                 Background="Transparent"/>
                    </Border>

                    <TextBlock Text="كلمة المرور"
                               FontSize="14"
                               Foreground="#555555"
                               Margin="0,15,0,5"/>
                    <Border CornerRadius="5" BorderThickness="1" BorderBrush="#DDDDDD">
                        <PasswordBox x:Name="PasswordBox"
                                     Height="40"
                                     Padding="10,0"
                                     VerticalContentAlignment="Center"
                                     BorderThickness="0"
                                     Background="Transparent"
                                     PasswordChanged="PasswordBox_PasswordChanged"/>
                    </Border>

                    <TextBlock Text="{Binding ErrorMessage}"
                               Foreground="#F44336"
                               TextWrapping="Wrap"
                               Margin="0,15,0,15"
                               Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

                    <Button Content="تسجيل الدخول"
                            Command="{Binding LoginCommand}"
                            Height="45"
                            Background="#2196F3"
                            Foreground="White"
                            BorderThickness="0"
                            FontSize="16"
                            FontWeight="SemiBold">
                        <Button.Effect>
                            <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.3" Color="#000000"/>
                        </Button.Effect>
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="5"/>
                            </Style>
                        </Button.Resources>
                    </Button>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window>
