using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للأمان
    /// </summary>
    public static class SecurityHelper
    {
        // مفتاح التشفير الافتراضي (يجب تغييره في بيئة الإنتاج)
        private static readonly byte[] DefaultKey = Encoding.UTF8.GetBytes("SmartAccountProSecurityKey123456");
        private static readonly byte[] DefaultIV = Encoding.UTF8.GetBytes("SmartAccountProIV");

        /// <summary>
        /// تشفير نص
        /// </summary>
        /// <param name="plainText">النص المراد تشفيره</param>
        /// <returns>النص المشفر</returns>
        public static string Encrypt(string plainText)
        {
            try
            {
                if (string.IsNullOrEmpty(plainText))
                    return string.Empty;

                // إنشاء مشفر AES
                using (Aes aes = Aes.Create())
                {
                    aes.Key = DefaultKey;
                    aes.IV = DefaultIV;

                    // إنشاء محول التشفير
                    ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                    // إنشاء ذاكرة مؤقتة للتشفير
                    using (MemoryStream ms = new MemoryStream())
                    {
                        using (CryptoStream cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                        {
                            using (StreamWriter sw = new StreamWriter(cs))
                            {
                                sw.Write(plainText);
                            }
                            return Convert.ToBase64String(ms.ToArray());
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityHelper.Encrypt");
                return string.Empty;
            }
        }

        /// <summary>
        /// فك تشفير نص
        /// </summary>
        /// <param name="cipherText">النص المشفر</param>
        /// <returns>النص الأصلي</returns>
        public static string Decrypt(string cipherText)
        {
            try
            {
                if (string.IsNullOrEmpty(cipherText))
                    return string.Empty;

                // تحويل النص المشفر إلى مصفوفة بايت
                byte[] buffer = Convert.FromBase64String(cipherText);

                // إنشاء مشفر AES
                using (Aes aes = Aes.Create())
                {
                    aes.Key = DefaultKey;
                    aes.IV = DefaultIV;

                    // إنشاء محول فك التشفير
                    ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                    // إنشاء ذاكرة مؤقتة لفك التشفير
                    using (MemoryStream ms = new MemoryStream(buffer))
                    {
                        using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                        {
                            using (StreamReader sr = new StreamReader(cs))
                            {
                                return sr.ReadToEnd();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityHelper.Decrypt");
                return string.Empty;
            }
        }

        /// <summary>
        /// حساب هاش لكلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>هاش كلمة المرور</returns>
        public static string HashPassword(string password)
        {
            try
            {
                if (string.IsNullOrEmpty(password))
                    return string.Empty;

                // إنشاء ملح عشوائي
                byte[] salt = new byte[16];
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(salt);
                }

                // حساب هاش كلمة المرور باستخدام PBKDF2
                var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000);
                byte[] hash = pbkdf2.GetBytes(20);

                // دمج الملح والهاش
                byte[] hashBytes = new byte[36];
                Array.Copy(salt, 0, hashBytes, 0, 16);
                Array.Copy(hash, 0, hashBytes, 16, 20);

                // تحويل الهاش إلى نص
                return Convert.ToBase64String(hashBytes);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityHelper.HashPassword");
                return string.Empty;
            }
        }

        /// <summary>
        /// التحقق من صحة كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="hashedPassword">هاش كلمة المرور</param>
        /// <returns>صحة كلمة المرور</returns>
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            try
            {
                if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hashedPassword))
                    return false;

                // تحويل الهاش إلى مصفوفة بايت
                byte[] hashBytes = Convert.FromBase64String(hashedPassword);

                // استخراج الملح
                byte[] salt = new byte[16];
                Array.Copy(hashBytes, 0, salt, 0, 16);

                // حساب هاش كلمة المرور باستخدام PBKDF2
                var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000);
                byte[] hash = pbkdf2.GetBytes(20);

                // مقارنة الهاش
                for (int i = 0; i < 20; i++)
                {
                    if (hashBytes[i + 16] != hash[i])
                        return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityHelper.VerifyPassword");
                return false;
            }
        }

        /// <summary>
        /// إنشاء رمز تحقق
        /// </summary>
        /// <param name="length">طول الرمز</param>
        /// <returns>رمز التحقق</returns>
        public static string GenerateVerificationCode(int length = 6)
        {
            try
            {
                // إنشاء رمز عشوائي
                var random = new Random();
                var code = new StringBuilder();
                for (int i = 0; i < length; i++)
                {
                    code.Append(random.Next(0, 10));
                }
                return code.ToString();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityHelper.GenerateVerificationCode");
                return string.Empty;
            }
        }

        /// <summary>
        /// إنشاء رمز تفعيل
        /// </summary>
        /// <returns>رمز التفعيل</returns>
        public static string GenerateActivationCode()
        {
            try
            {
                // إنشاء رمز تفعيل عشوائي
                var guid = Guid.NewGuid();
                return guid.ToString("N").Substring(0, 16).ToUpper();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityHelper.GenerateActivationCode");
                return string.Empty;
            }
        }

        /// <summary>
        /// إنشاء رمز استرداد
        /// </summary>
        /// <returns>رمز الاسترداد</returns>
        public static string GenerateRecoveryCode()
        {
            try
            {
                // إنشاء رمز استرداد عشوائي
                var guid = Guid.NewGuid();
                return guid.ToString("N").Substring(0, 8).ToUpper();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityHelper.GenerateRecoveryCode");
                return string.Empty;
            }
        }

        /// <summary>
        /// التحقق من صحة رمز التحقق
        /// </summary>
        /// <param name="code">الرمز المدخل</param>
        /// <param name="expectedCode">الرمز المتوقع</param>
        /// <returns>صحة الرمز</returns>
        public static bool VerifyCode(string code, string expectedCode)
        {
            try
            {
                if (string.IsNullOrEmpty(code) || string.IsNullOrEmpty(expectedCode))
                    return false;

                return code.Equals(expectedCode, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityHelper.VerifyCode");
                return false;
            }
        }
    }
}
