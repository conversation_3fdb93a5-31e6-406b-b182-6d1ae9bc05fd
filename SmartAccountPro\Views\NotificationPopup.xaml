<Window x:Class="SmartAccountPro.Views.NotificationPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SmartAccountPro.Views"
        mc:Ignorable="d"
        Title="إشعار جديد" 
        Height="120" 
        Width="350"
        WindowStyle="None" 
        ResizeMode="NoResize" 
        ShowInTaskbar="False" 
        Topmost="True"
        AllowsTransparency="True" 
        Background="Transparent"
        WindowStartupLocation="Manual"
        FlowDirection="RightToLeft">
    
    <Window.Resources>
        <Storyboard x:Key="FadeInStoryboard">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.5"/>
        </Storyboard>
        <Storyboard x:Key="FadeOutStoryboard">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="1" To="0" Duration="0:0:0.5"/>
        </Storyboard>
    </Window.Resources>
    
    <Border Background="{DynamicResource CardBackgroundColor}" 
            BorderBrush="{DynamicResource BorderColor}" 
            BorderThickness="1" 
            CornerRadius="5" 
            Effect="{DynamicResource ShadowEffect}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- أيقونة نوع الإشعار -->
            <Border Grid.Column="0" 
                    Width="40" 
                    Height="40" 
                    CornerRadius="20" 
                    Background="{Binding TypeColor}" 
                    Margin="15,0,10,0">
                <TextBlock Text="{Binding TypeIcon}" 
                           FontFamily="Segoe MDL2 Assets" 
                           FontSize="18" 
                           Foreground="White" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center"/>
            </Border>
            
            <!-- محتوى الإشعار -->
            <StackPanel Grid.Column="1" 
                        Margin="0,15">
                <TextBlock Text="{Binding Title}" 
                           FontWeight="Bold" 
                           FontSize="14" 
                           Foreground="{DynamicResource TextColor}"/>
                <TextBlock Text="{Binding Content}" 
                           TextWrapping="Wrap" 
                           Margin="0,5,0,0" 
                           Foreground="{DynamicResource TextColor}"/>
            </StackPanel>
            
            <!-- زر الإغلاق -->
            <Button Grid.Column="2" 
                    Content="&#xE711;" 
                    FontFamily="Segoe MDL2 Assets" 
                    FontSize="10" 
                    Width="20" 
                    Height="20" 
                    Margin="0,10,10,0" 
                    VerticalAlignment="Top" 
                    HorizontalAlignment="Right" 
                    Background="Transparent" 
                    BorderThickness="0" 
                    Foreground="{DynamicResource SecondaryTextColor}" 
                    Click="CloseButton_Click"/>
        </Grid>
    </Border>
</Window>
