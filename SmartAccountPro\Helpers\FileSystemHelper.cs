using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للتعامل مع نظام الملفات
    /// </summary>
    public static class FileSystemHelper
    {
        /// <summary>
        /// إنشاء مجلد
        /// </summary>
        /// <param name="path">مسار المجلد</param>
        /// <returns>نجاح العملية</returns>
        public static bool CreateDirectory(string path)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.CreateDirectory", () =>
                {
                    // التحقق من وجود المجلد
                    if (Directory.Exists(path))
                    {
                        return true;
                    }

                    // إنشاء المجلد
                    Directory.CreateDirectory(path);

                    Debug.WriteLine($"[FILESYSTEM] تم إنشاء المجلد: {path}");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.CreateDirectory");
                return false;
            }
        }

        /// <summary>
        /// حذف مجلد
        /// </summary>
        /// <param name="path">مسار المجلد</param>
        /// <param name="recursive">حذف المحتويات</param>
        /// <returns>نجاح العملية</returns>
        public static bool DeleteDirectory(string path, bool recursive = true)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.DeleteDirectory", () =>
                {
                    // التحقق من وجود المجلد
                    if (!Directory.Exists(path))
                    {
                        return true;
                    }

                    // حذف المجلد
                    Directory.Delete(path, recursive);

                    Debug.WriteLine($"[FILESYSTEM] تم حذف المجلد: {path}");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.DeleteDirectory");
                return false;
            }
        }

        /// <summary>
        /// نسخ مجلد
        /// </summary>
        /// <param name="sourcePath">مسار المصدر</param>
        /// <param name="destinationPath">مسار الوجهة</param>
        /// <param name="overwrite">استبدال الملفات الموجودة</param>
        /// <returns>نجاح العملية</returns>
        public static bool CopyDirectory(string sourcePath, string destinationPath, bool overwrite = true)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.CopyDirectory", () =>
                {
                    // التحقق من وجود المجلد المصدر
                    if (!Directory.Exists(sourcePath))
                    {
                        throw new DirectoryNotFoundException($"المجلد المصدر غير موجود: {sourcePath}");
                    }

                    // إنشاء المجلد الوجهة إذا لم يكن موجوداً
                    if (!Directory.Exists(destinationPath))
                    {
                        Directory.CreateDirectory(destinationPath);
                    }

                    // نسخ الملفات
                    foreach (string file in Directory.GetFiles(sourcePath))
                    {
                        string fileName = Path.GetFileName(file);
                        string destFile = Path.Combine(destinationPath, fileName);
                        File.Copy(file, destFile, overwrite);
                    }

                    // نسخ المجلدات الفرعية
                    foreach (string subDir in Directory.GetDirectories(sourcePath))
                    {
                        string dirName = Path.GetFileName(subDir);
                        string destDir = Path.Combine(destinationPath, dirName);
                        CopyDirectory(subDir, destDir, overwrite);
                    }

                    Debug.WriteLine($"[FILESYSTEM] تم نسخ المجلد من {sourcePath} إلى {destinationPath}");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.CopyDirectory");
                return false;
            }
        }

        /// <summary>
        /// نقل مجلد
        /// </summary>
        /// <param name="sourcePath">مسار المصدر</param>
        /// <param name="destinationPath">مسار الوجهة</param>
        /// <returns>نجاح العملية</returns>
        public static bool MoveDirectory(string sourcePath, string destinationPath)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.MoveDirectory", () =>
                {
                    // التحقق من وجود المجلد المصدر
                    if (!Directory.Exists(sourcePath))
                    {
                        throw new DirectoryNotFoundException($"المجلد المصدر غير موجود: {sourcePath}");
                    }

                    // التحقق من عدم وجود المجلد الوجهة
                    if (Directory.Exists(destinationPath))
                    {
                        Directory.Delete(destinationPath, true);
                    }

                    // نقل المجلد
                    Directory.Move(sourcePath, destinationPath);

                    Debug.WriteLine($"[FILESYSTEM] تم نقل المجلد من {sourcePath} إلى {destinationPath}");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.MoveDirectory");
                return false;
            }
        }

        /// <summary>
        /// حذف ملف
        /// </summary>
        /// <param name="path">مسار الملف</param>
        /// <returns>نجاح العملية</returns>
        public static bool DeleteFile(string path)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.DeleteFile", () =>
                {
                    // التحقق من وجود الملف
                    if (!File.Exists(path))
                    {
                        return true;
                    }

                    // حذف الملف
                    File.Delete(path);

                    Debug.WriteLine($"[FILESYSTEM] تم حذف الملف: {path}");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.DeleteFile");
                return false;
            }
        }

        /// <summary>
        /// نسخ ملف
        /// </summary>
        /// <param name="sourcePath">مسار المصدر</param>
        /// <param name="destinationPath">مسار الوجهة</param>
        /// <param name="overwrite">استبدال الملف الموجود</param>
        /// <returns>نجاح العملية</returns>
        public static bool CopyFile(string sourcePath, string destinationPath, bool overwrite = true)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.CopyFile", () =>
                {
                    // التحقق من وجود الملف المصدر
                    if (!File.Exists(sourcePath))
                    {
                        throw new FileNotFoundException($"الملف المصدر غير موجود: {sourcePath}");
                    }

                    // إنشاء مجلد الوجهة إذا لم يكن موجوداً
                    string? destinationDir = Path.GetDirectoryName(destinationPath);
                    if (!string.IsNullOrEmpty(destinationDir) && !Directory.Exists(destinationDir))
                    {
                        Directory.CreateDirectory(destinationDir);
                    }

                    // نسخ الملف
                    File.Copy(sourcePath, destinationPath, overwrite);

                    Debug.WriteLine($"[FILESYSTEM] تم نسخ الملف من {sourcePath} إلى {destinationPath}");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.CopyFile");
                return false;
            }
        }

        /// <summary>
        /// نقل ملف
        /// </summary>
        /// <param name="sourcePath">مسار المصدر</param>
        /// <param name="destinationPath">مسار الوجهة</param>
        /// <param name="overwrite">استبدال الملف الموجود</param>
        /// <returns>نجاح العملية</returns>
        public static bool MoveFile(string sourcePath, string destinationPath, bool overwrite = true)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.MoveFile", () =>
                {
                    // التحقق من وجود الملف المصدر
                    if (!File.Exists(sourcePath))
                    {
                        throw new FileNotFoundException($"الملف المصدر غير موجود: {sourcePath}");
                    }

                    // إنشاء مجلد الوجهة إذا لم يكن موجوداً
                    string? destinationDir = Path.GetDirectoryName(destinationPath);
                    if (!string.IsNullOrEmpty(destinationDir) && !Directory.Exists(destinationDir))
                    {
                        Directory.CreateDirectory(destinationDir);
                    }

                    // حذف الملف الوجهة إذا كان موجوداً
                    if (File.Exists(destinationPath))
                    {
                        if (overwrite)
                        {
                            File.Delete(destinationPath);
                        }
                        else
                        {
                            throw new IOException($"الملف الوجهة موجود بالفعل: {destinationPath}");
                        }
                    }

                    // نقل الملف
                    File.Move(sourcePath, destinationPath);

                    Debug.WriteLine($"[FILESYSTEM] تم نقل الملف من {sourcePath} إلى {destinationPath}");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.MoveFile");
                return false;
            }
        }

        /// <summary>
        /// قراءة ملف نصي
        /// </summary>
        /// <param name="path">مسار الملف</param>
        /// <param name="encoding">ترميز النص</param>
        /// <returns>محتوى الملف</returns>
        public static string ReadTextFile(string path, Encoding? encoding = null)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.ReadTextFile", () =>
                {
                    // التحقق من وجود الملف
                    if (!File.Exists(path))
                    {
                        throw new FileNotFoundException($"الملف غير موجود: {path}");
                    }

                    // تحديد ترميز النص
                    encoding ??= Encoding.UTF8;

                    // قراءة الملف
                    return File.ReadAllText(path, encoding);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.ReadTextFile");
                throw;
            }
        }

        /// <summary>
        /// كتابة ملف نصي
        /// </summary>
        /// <param name="path">مسار الملف</param>
        /// <param name="content">محتوى الملف</param>
        /// <param name="encoding">ترميز النص</param>
        /// <returns>نجاح العملية</returns>
        public static bool WriteTextFile(string path, string content, Encoding? encoding = null)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.WriteTextFile", () =>
                {
                    // إنشاء مجلد الملف إذا لم يكن موجوداً
                    string? directory = Path.GetDirectoryName(path);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // تحديد ترميز النص
                    encoding ??= Encoding.UTF8;

                    // كتابة الملف
                    File.WriteAllText(path, content, encoding);

                    Debug.WriteLine($"[FILESYSTEM] تم كتابة الملف: {path}");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.WriteTextFile");
                return false;
            }
        }

        /// <summary>
        /// حساب تجزئة MD5 لملف
        /// </summary>
        /// <param name="path">مسار الملف</param>
        /// <returns>تجزئة MD5</returns>
        public static string CalculateFileMD5(string path)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.CalculateFileMD5", () =>
                {
                    // التحقق من وجود الملف
                    if (!File.Exists(path))
                    {
                        throw new FileNotFoundException($"الملف غير موجود: {path}");
                    }

                    // حساب تجزئة MD5
                    using (var md5 = MD5.Create())
                    using (var stream = File.OpenRead(path))
                    {
                        byte[] hash = md5.ComputeHash(stream);
                        return BitConverter.ToString(hash).Replace("-", "").ToLower();
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.CalculateFileMD5");
                throw;
            }
        }

        /// <summary>
        /// الحصول على حجم مجلد
        /// </summary>
        /// <param name="path">مسار المجلد</param>
        /// <returns>حجم المجلد بالبايت</returns>
        public static long GetDirectorySize(string path)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.GetDirectorySize", () =>
                {
                    // التحقق من وجود المجلد
                    if (!Directory.Exists(path))
                    {
                        throw new DirectoryNotFoundException($"المجلد غير موجود: {path}");
                    }

                    // حساب حجم المجلد
                    long size = 0;

                    // حساب حجم الملفات
                    foreach (string file in Directory.GetFiles(path, "*", SearchOption.AllDirectories))
                    {
                        var fileInfo = new FileInfo(file);
                        size += fileInfo.Length;
                    }

                    return size;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.GetDirectorySize");
                return -1;
            }
        }

        /// <summary>
        /// تنسيق حجم الملف
        /// </summary>
        /// <param name="size">الحجم بالبايت</param>
        /// <returns>الحجم المنسق</returns>
        public static string FormatFileSize(long size)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.FormatFileSize", () =>
                {
                    // تحديد وحدات القياس
                    string[] sizes = { "B", "KB", "MB", "GB", "TB" };
                    double len = size;
                    int order = 0;

                    // تحويل الحجم إلى الوحدة المناسبة
                    while (len >= 1024 && order < sizes.Length - 1)
                    {
                        order++;
                        len = len / 1024;
                    }

                    // تنسيق الحجم
                    return $"{len:0.##} {sizes[order]}";
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.FormatFileSize");
                return $"{size} B";
            }
        }

        /// <summary>
        /// البحث عن ملفات
        /// </summary>
        /// <param name="path">مسار البحث</param>
        /// <param name="searchPattern">نمط البحث</param>
        /// <param name="recursive">بحث متكرر</param>
        /// <returns>قائمة الملفات</returns>
        public static List<string> FindFiles(string path, string searchPattern = "*", bool recursive = true)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.FindFiles", () =>
                {
                    // التحقق من وجود المجلد
                    if (!Directory.Exists(path))
                    {
                        throw new DirectoryNotFoundException($"المجلد غير موجود: {path}");
                    }

                    // تحديد خيارات البحث
                    var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;

                    // البحث عن الملفات
                    return Directory.GetFiles(path, searchPattern, searchOption).ToList();
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.FindFiles");
                return new List<string>();
            }
        }

        /// <summary>
        /// تنظيف المجلدات المؤقتة
        /// </summary>
        /// <param name="path">مسار المجلد</param>
        /// <param name="olderThanDays">أقدم من (بالأيام)</param>
        /// <returns>عدد الملفات المحذوفة</returns>
        public static int CleanupTempFiles(string path, int olderThanDays = 7)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("FileSystemHelper.CleanupTempFiles", () =>
                {
                    // التحقق من وجود المجلد
                    if (!Directory.Exists(path))
                    {
                        return 0;
                    }

                    // تحديد تاريخ الحذف
                    var cutoffDate = DateTime.Now.AddDays(-olderThanDays);
                    int deletedCount = 0;

                    // حذف الملفات القديمة
                    foreach (string file in Directory.GetFiles(path, "*", SearchOption.AllDirectories))
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.LastWriteTime < cutoffDate)
                        {
                            try
                            {
                                File.Delete(file);
                                deletedCount++;
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"[FILESYSTEM] فشل حذف الملف {file}: {ex.Message}");
                            }
                        }
                    }

                    // حذف المجلدات الفارغة
                    foreach (string dir in Directory.GetDirectories(path, "*", SearchOption.AllDirectories))
                    {
                        try
                        {
                            if (!Directory.EnumerateFileSystemEntries(dir).Any())
                            {
                                Directory.Delete(dir);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"[FILESYSTEM] فشل حذف المجلد {dir}: {ex.Message}");
                        }
                    }

                    Debug.WriteLine($"[FILESYSTEM] تم حذف {deletedCount} ملف من المجلد {path}");
                    return deletedCount;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FileSystemHelper.CleanupTempFiles");
                return -1;
            }
        }
    }
}
