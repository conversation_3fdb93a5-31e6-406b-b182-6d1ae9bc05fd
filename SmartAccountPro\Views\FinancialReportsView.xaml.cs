using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using SmartAccountPro.Helpers;
using SmartAccountPro.ViewModels;

namespace SmartAccountPro.Views
{
    /// <summary>
    /// Interaction logic for FinancialReportsView.xaml
    /// </summary>
    public partial class FinancialReportsView : UserControl
    {
        private FinancialReportsViewModel _viewModel;

        public FinancialReportsView()
        {
            // قياس وقت تهيئة الواجهة
            PerformanceHelper.MeasureExecutionTime("FinancialReportsView.Initialize", () =>
            {
                InitializeComponent();

                // تهيئة نموذج العرض
                _viewModel = new FinancialReportsViewModel();
                DataContext = _viewModel;
            });
        }
    }
}
