<UserControl x:Class="SmartAccountPro.Views.AccountsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SmartAccountPro.Views"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             FlowDirection="RightToLeft">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- مؤشر التحميل -->
        <Border Grid.Row="0" Grid.RowSpan="2"
                Background="#********"
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                Panel.ZIndex="1000">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <Border Background="White"
                        CornerRadius="10"
                        Padding="20">
                    <StackPanel>
                        <TextBlock Text="&#xE895;"
                                   FontFamily="Segoe MDL2 Assets"
                                   FontSize="32"
                                   HorizontalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryColor}">
                            <TextBlock.RenderTransform>
                                <RotateTransform x:Name="SpinnerRotation" Angle="0" CenterX="16" CenterY="16"/>
                            </TextBlock.RenderTransform>
                            <TextBlock.Triggers>
                                <EventTrigger RoutedEvent="Loaded">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="SpinnerRotation"
                                                             Storyboard.TargetProperty="Angle"
                                                             From="0" To="360"
                                                             Duration="0:0:1"
                                                             RepeatBehavior="Forever"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </TextBlock.Triggers>
                        </TextBlock>
                        <TextBlock Text="جاري التحميل..."
                                   Margin="0,10,0,0"
                                   HorizontalAlignment="Center"
                                   FontSize="14"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>

        <!-- شريط الأدوات -->
        <Border Grid.Row="0"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="10"
                Padding="15"
                Margin="0,0,0,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal">
                <Button x:Name="AddAccountButton"
                        Style="{StaticResource ModernButton}"
                        Margin="0,0,10,0"
                        Padding="12,8"
                        Command="{Binding AddAccountCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE710;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="إضافة حساب"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button x:Name="EditAccountButton"
                        Style="{StaticResource GreenButton}"
                        Margin="0,0,10,0"
                        Padding="12,8"
                        Command="{Binding EditAccountCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE70F;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="تعديل حساب"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button x:Name="DeleteAccountButton"
                        Style="{StaticResource RedButton}"
                        Margin="0,0,10,0"
                        Padding="12,8"
                        Command="{Binding DeleteAccountCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE74D;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="حذف حساب"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button x:Name="RefreshButton"
                        Style="{StaticResource OrangeButton}"
                        Margin="0,0,10,0"
                        Padding="12,8"
                        Command="{Binding RefreshCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE72C;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="تحديث"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button x:Name="PrintButton"
                        Style="{StaticResource PurpleButton}"
                        Padding="12,8"
                        Command="{Binding PrintCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE749;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="طباعة"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>

        <!-- شجرة الحسابات -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="2*"/>
            </Grid.ColumnDefinitions>

            <!-- شجرة الحسابات -->
            <Border Grid.Column="0"
                    Background="White"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1"
                    CornerRadius="10"
                    Padding="15">
                <Border.Effect>
                    <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="&#xE825;"
                                   FontFamily="Segoe MDL2 Assets"
                                   Foreground="{DynamicResource PrimaryColor}"
                                   FontSize="20"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="شجرة الحسابات"
                                   FontWeight="Bold"
                                   FontSize="16"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <TreeView Grid.Row="1" x:Name="AccountsTreeView" BorderThickness="0"
                              ItemsSource="{Binding Accounts}"
                              SelectedItemChanged="AccountsTreeView_SelectedItemChanged">
                        <TreeView.ItemTemplate>
                            <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding Code}" Margin="0,0,5,0" FontWeight="SemiBold"/>
                                    <TextBlock Text="{Binding Name}"/>
                                </StackPanel>
                            </HierarchicalDataTemplate>
                        </TreeView.ItemTemplate>
                        <!-- هنا سيتم إضافة بيانات شجرة الحسابات بشكل ديناميكي -->
                        <TreeView.ItemsPanel>
                            <ItemsPanelTemplate>
                                <StackPanel/>
                            </ItemsPanelTemplate>
                        </TreeView.ItemsPanel>
                    </TreeView>
                </Grid>
            </Border>

            <!-- فاصل -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Center" VerticalAlignment="Stretch"/>

            <!-- تفاصيل الحساب -->
            <Border Grid.Column="2"
                    Background="White"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1"
                    CornerRadius="10"
                    Padding="15">
                <Border.Effect>
                    <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="&#xE7BE;"
                                   FontFamily="Segoe MDL2 Assets"
                                   Foreground="{DynamicResource PrimaryColor}"
                                   FontSize="20"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="تفاصيل الحساب"
                                   FontWeight="Bold"
                                   FontSize="16"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- نموذج تفاصيل الحساب -->
                    <Border Grid.Row="1"
                            Background="#F8F9FA"
                            CornerRadius="8"
                            Padding="15"
                            Margin="0,0,0,15">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="كود الحساب:" Margin="0,8,10,8"/>
                            <Border Grid.Row="0" Grid.Column="1"
                                    BorderBrush="#DDDDDD"
                                    BorderThickness="1"
                                    CornerRadius="4"
                                    Margin="0,5,10,5">
                                <TextBox BorderThickness="0"
                                         Background="Transparent"
                                         Padding="8,5"
                                         IsReadOnly="True"/>
                            </Border>

                            <TextBlock Grid.Row="0" Grid.Column="2" Text="اسم الحساب:" Margin="0,8,10,8"/>
                            <Border Grid.Row="0" Grid.Column="3"
                                    BorderBrush="#DDDDDD"
                                    BorderThickness="1"
                                    CornerRadius="4"
                                    Margin="0,5,0,5">
                                <TextBox BorderThickness="0"
                                         Background="Transparent"
                                         Padding="8,5"
                                         IsReadOnly="True"/>
                            </Border>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="نوع الحساب:" Margin="0,8,10,8"/>
                            <Border Grid.Row="1" Grid.Column="1"
                                    BorderBrush="#DDDDDD"
                                    BorderThickness="1"
                                    CornerRadius="4"
                                    Margin="0,5,10,5">
                                <ComboBox BorderThickness="0"
                                          Background="Transparent"
                                          Padding="5"
                                          IsEnabled="False">
                                    <ComboBoxItem Content="أصول"/>
                                    <ComboBoxItem Content="خصوم"/>
                                    <ComboBoxItem Content="حقوق ملكية"/>
                                    <ComboBoxItem Content="إيرادات"/>
                                    <ComboBoxItem Content="مصروفات"/>
                                </ComboBox>
                            </Border>

                            <TextBlock Grid.Row="1" Grid.Column="2" Text="الحساب الأب:" Margin="0,8,10,8"/>
                            <Border Grid.Row="1" Grid.Column="3"
                                    BorderBrush="#DDDDDD"
                                    BorderThickness="1"
                                    CornerRadius="4"
                                    Margin="0,5,0,5">
                                <TextBox BorderThickness="0"
                                         Background="Transparent"
                                         Padding="8,5"
                                         IsReadOnly="True"/>
                            </Border>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="الرصيد:" Margin="0,8,10,8"/>
                            <Border Grid.Row="2" Grid.Column="1"
                                    BorderBrush="#DDDDDD"
                                    BorderThickness="1"
                                    CornerRadius="4"
                                    Margin="0,5,10,5">
                                <TextBox BorderThickness="0"
                                         Background="Transparent"
                                         Padding="8,5"
                                         IsReadOnly="True"/>
                            </Border>

                            <TextBlock Grid.Row="2" Grid.Column="2" Text="الحالة:" Margin="0,8,10,8"/>
                            <CheckBox Grid.Row="2" Grid.Column="3"
                                      Content="نشط"
                                      Margin="0,8,0,8"
                                      IsEnabled="False"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="الوصف:" Margin="0,8,10,8"/>
                            <Border Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="3"
                                    BorderBrush="#DDDDDD"
                                    BorderThickness="1"
                                    CornerRadius="4"
                                    Margin="0,5,0,5">
                                <TextBox BorderThickness="0"
                                         Background="Transparent"
                                         Padding="8,5"
                                         TextWrapping="Wrap"
                                         AcceptsReturn="True"
                                         Height="60"
                                         IsReadOnly="True"/>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- حركة الحساب -->
                    <Grid Grid.Row="2">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBlock Text="&#xE81C;"
                                       FontFamily="Segoe MDL2 Assets"
                                       Foreground="{DynamicResource PrimaryColor}"
                                       FontSize="18"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,0"/>
                            <TextBlock Text="حركة الحساب"
                                       FontWeight="Bold"
                                       FontSize="14"
                                       VerticalAlignment="Center"/>
                        </StackPanel>

                        <DataGrid Grid.Row="1"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  BorderThickness="1"
                                  BorderBrush="#E0E0E0"
                                  Background="White"
                                  RowBackground="White"
                                  AlternatingRowBackground="#F8F9FA"
                                  GridLinesVisibility="Horizontal"
                                  HorizontalGridLinesBrush="#E0E0E0"
                                  ItemsSource="{Binding AccountLedger}"
                                  SelectionMode="Single"
                                  SelectionUnit="FullRow"
                                  CanUserSortColumns="True"
                                  CanUserResizeColumns="True"
                                  CanUserReorderColumns="True"
                                  HeadersVisibility="Column"
                                  RowHeaderWidth="0">
                            <DataGrid.Resources>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#F1F3F4"/>
                                    <Setter Property="Padding" Value="10,8"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                </Style>
                            </DataGrid.Resources>
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="التاريخ" Width="100" Binding="{Binding Date, StringFormat=\{0:yyyy/MM/dd\}}">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="رقم القيد" Width="80" Binding="{Binding EntryNumber}">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="البيان" Width="*" Binding="{Binding Description}">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="TextWrapping" Value="Wrap"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="مدين" Width="100" Binding="{Binding Debit, StringFormat=\{0:N2\}}">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Left"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Foreground" Value="#2E7D32"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="دائن" Width="100" Binding="{Binding Credit, StringFormat=\{0:N2\}}">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Left"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Foreground" Value="#C62828"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="الرصيد" Width="100" Binding="{Binding Balance, StringFormat=\{0:N2\}}">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Left"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
