using Microsoft.Win32;
using SmartAccountPro.Helpers;
using System;
using System.IO;
using System.Windows;
using System.Windows.Input;
using System.Windows.Forms;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// نموذج عرض الإعدادات
    /// </summary>
    public class SettingsViewModel : ViewModelBase
    {
        private bool _isDarkMode;
        private double _fontSize;
        private bool _autoBackup;
        private int _backupInterval;
        private string _backupPath;

        /// <summary>
        /// وضع الليل
        /// </summary>
        public bool IsDarkMode
        {
            get => _isDarkMode;
            set => SetProperty(ref _isDarkMode, value);
        }

        /// <summary>
        /// حجم الخط
        /// </summary>
        public double FontSize
        {
            get => _fontSize;
            set => SetProperty(ref _fontSize, value);
        }

        /// <summary>
        /// النسخ الاحتياطي التلقائي
        /// </summary>
        public bool AutoBackup
        {
            get => _autoBackup;
            set => SetProperty(ref _autoBackup, value);
        }

        /// <summary>
        /// فترة النسخ الاحتياطي
        /// </summary>
        public int BackupInterval
        {
            get => _backupInterval;
            set => SetProperty(ref _backupInterval, value);
        }

        /// <summary>
        /// مسار النسخ الاحتياطي
        /// </summary>
        public string BackupPath
        {
            get => _backupPath;
            set => SetProperty(ref _backupPath, value);
        }

        /// <summary>
        /// أمر تبديل السمة
        /// </summary>
        public ICommand ToggleThemeCommand { get; }

        /// <summary>
        /// أمر زيادة حجم الخط
        /// </summary>
        public ICommand IncreaseFontSizeCommand { get; }

        /// <summary>
        /// أمر تقليل حجم الخط
        /// </summary>
        public ICommand DecreaseFontSizeCommand { get; }

        /// <summary>
        /// أمر تحديد مسار النسخ الاحتياطي
        /// </summary>
        public ICommand SelectBackupPathCommand { get; }

        /// <summary>
        /// أمر إنشاء نسخة احتياطية الآن
        /// </summary>
        public ICommand CreateBackupNowCommand { get; }

        /// <summary>
        /// أمر استعادة نسخة احتياطية
        /// </summary>
        public ICommand RestoreBackupCommand { get; }

        /// <summary>
        /// أمر حفظ الإعدادات
        /// </summary>
        public ICommand SaveSettingsCommand { get; }

        public SettingsViewModel()
        {
            // تحميل الإعدادات
            LoadSettings();

            // تهيئة الأوامر
            ToggleThemeCommand = new RelayCommand(ToggleTheme);
            IncreaseFontSizeCommand = new RelayCommand(IncreaseFontSize);
            DecreaseFontSizeCommand = new RelayCommand(DecreaseFontSize);
            SelectBackupPathCommand = new RelayCommand(SelectBackupPath);
            CreateBackupNowCommand = new RelayCommand(CreateBackupNow);
            RestoreBackupCommand = new RelayCommand(RestoreBackup);
            SaveSettingsCommand = new RelayCommand(SaveSettings);
        }

        /// <summary>
        /// تحميل الإعدادات
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                // تحميل إعدادات السمة
                IsDarkMode = Properties.Settings.Default.Theme == "Dark";

                // تحميل إعدادات حجم الخط
                FontSize = Properties.Settings.Default.FontSize;

                // تحميل إعدادات النسخ الاحتياطي
                AutoBackup = Properties.Settings.Default.AutoBackup;
                BackupInterval = Properties.Settings.Default.BackupInterval;
                BackupPath = Properties.Settings.Default.BackupPath;

                // إذا كان مسار النسخ الاحتياطي فارغاً، استخدم المسار الافتراضي
                if (string.IsNullOrEmpty(BackupPath))
                {
                    BackupPath = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                        "SmartAccountPro",
                        "Backups");
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SettingsViewModel.LoadSettings");
            }
        }

        /// <summary>
        /// تبديل السمة
        /// </summary>
        private void ToggleTheme(object parameter)
        {
            try
            {
                IsDarkMode = !IsDarkMode;
                ThemeManager.ChangeTheme(IsDarkMode ? ThemeManager.ThemeType.Dark : ThemeManager.ThemeType.Light);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SettingsViewModel.ToggleTheme");
            }
        }

        /// <summary>
        /// زيادة حجم الخط
        /// </summary>
        private void IncreaseFontSize(object parameter)
        {
            try
            {
                if (FontSize < 20)
                {
                    FontSize += 1;
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SettingsViewModel.IncreaseFontSize");
            }
        }

        /// <summary>
        /// تقليل حجم الخط
        /// </summary>
        private void DecreaseFontSize(object parameter)
        {
            try
            {
                if (FontSize > 8)
                {
                    FontSize -= 1;
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SettingsViewModel.DecreaseFontSize");
            }
        }

        /// <summary>
        /// تحديد مسار النسخ الاحتياطي
        /// </summary>
        private void SelectBackupPath(object parameter)
        {
            try
            {
                using (var dialog = new FolderBrowserDialog
                {
                    Description = "اختر مجلد النسخ الاحتياطي",
                    SelectedPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
                })
                {
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        BackupPath = dialog.SelectedPath;
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SettingsViewModel.SelectBackupPath");
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية الآن
        /// </summary>
        private void CreateBackupNow(object parameter)
        {
            try
            {
                // التأكد من وجود مسار النسخ الاحتياطي
                if (string.IsNullOrEmpty(BackupPath))
                {
                    System.Windows.MessageBox.Show("الرجاء تحديد مسار النسخ الاحتياطي أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // التأكد من وجود المجلد
                if (!Directory.Exists(BackupPath))
                {
                    Directory.CreateDirectory(BackupPath);
                }

                // إنشاء اسم ملف النسخة الاحتياطية
                string backupFileName = $"SmartAccountPro_Backup_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.bak";
                string backupFilePath = Path.Combine(BackupPath, backupFileName);

                // هنا يمكن إضافة كود لإنشاء النسخة الاحتياطية
                // مثال بسيط للتوضيح فقط
                File.Copy(App.DatabasePath, backupFilePath, true);

                // تحديث تاريخ آخر نسخة احتياطية
                Properties.Settings.Default.LastBackupDate = DateTime.Now;
                Properties.Settings.Default.Save();

                System.Windows.MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SettingsViewModel.CreateBackupNow");
            }
        }

        /// <summary>
        /// استعادة نسخة احتياطية
        /// </summary>
        private void RestoreBackup(object parameter)
        {
            try
            {
                // التأكد من وجود مسار النسخ الاحتياطي
                if (string.IsNullOrEmpty(BackupPath) || !Directory.Exists(BackupPath))
                {
                    System.Windows.MessageBox.Show("الرجاء تحديد مسار النسخ الاحتياطي الصحيح أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // فتح مربع حوار اختيار ملف
                var dialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختر ملف النسخة الاحتياطية",
                    Filter = "ملفات النسخ الاحتياطي (*.bak)|*.bak",
                    InitialDirectory = BackupPath
                };

                if (dialog.ShowDialog() == true)
                {
                    // التأكد من أن المستخدم يريد استعادة النسخة الاحتياطية
                    var result = System.Windows.MessageBox.Show(
                        "هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال قاعدة البيانات الحالية.",
                        "تأكيد",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // إغلاق الاتصال بقاعدة البيانات
                        App.DbContext.Dispose();

                        // استعادة النسخة الاحتياطية
                        File.Copy(dialog.FileName, App.DatabasePath, true);

                        // إعادة تهيئة قاعدة البيانات
                        // إعادة تشغيل التطبيق سيقوم بإعادة تهيئة قاعدة البيانات تلقائياً

                        System.Windows.MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تشغيل التطبيق.", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);

                        // إعادة تشغيل التطبيق
                        System.Windows.Application.Current.Shutdown();
                        System.Diagnostics.Process.Start(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SettingsViewModel.RestoreBackup");
            }
        }

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private void SaveSettings(object parameter)
        {
            try
            {
                // حفظ إعدادات السمة
                Properties.Settings.Default.Theme = IsDarkMode ? "Dark" : "Light";

                // حفظ إعدادات حجم الخط
                Properties.Settings.Default.FontSize = FontSize;

                // حفظ إعدادات النسخ الاحتياطي
                Properties.Settings.Default.AutoBackup = AutoBackup;
                Properties.Settings.Default.BackupInterval = BackupInterval;
                Properties.Settings.Default.BackupPath = BackupPath;

                // حفظ الإعدادات
                Properties.Settings.Default.Save();

                System.Windows.MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SettingsViewModel.SaveSettings");
            }
        }
    }
}
