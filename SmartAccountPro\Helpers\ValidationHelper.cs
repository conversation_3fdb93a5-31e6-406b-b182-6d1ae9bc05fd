using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للتحقق من صحة البيانات
    /// </summary>
    public static class ValidationHelper
    {
        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>صحيح إذا كان البريد الإلكتروني صحيحاً</returns>
        public static bool IsValidEmail(string? email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                // تعبير منتظم للتحقق من صحة البريد الإلكتروني
                var regex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                return regex.IsMatch(email);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة رقم الهاتف
        /// </summary>
        /// <param name="phoneNumber">رقم الهاتف</param>
        /// <returns>صحيح إذا كان رقم الهاتف صحيحاً</returns>
        public static bool IsValidPhoneNumber(string? phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            try
            {
                // تعبير منتظم للتحقق من صحة رقم الهاتف
                var regex = new Regex(@"^[0-9\+\-\(\)\s]{8,15}$");
                return regex.IsMatch(phoneNumber);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة المبلغ المالي
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>صحيح إذا كان المبلغ صحيحاً</returns>
        public static bool IsValidAmount(string? amount)
        {
            if (string.IsNullOrWhiteSpace(amount))
                return false;

            try
            {
                // محاولة تحويل النص إلى قيمة عشرية
                if (decimal.TryParse(amount, NumberStyles.Currency, CultureInfo.CurrentCulture, out decimal result))
                {
                    // التحقق من أن المبلغ ليس سالبًا
                    return result >= 0;
                }
                return false;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ValidationHelper.IsValidAmount");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة التاريخ
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <returns>صحيح إذا كان التاريخ صحيحاً</returns>
        public static bool IsValidDate(string? date)
        {
            if (string.IsNullOrWhiteSpace(date))
                return false;

            try
            {
                // محاولة تحويل النص إلى تاريخ
                return DateTime.TryParse(date, out _);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة الرقم
        /// </summary>
        /// <param name="number">الرقم</param>
        /// <returns>صحيح إذا كان الرقم صحيحاً</returns>
        public static bool IsValidNumber(string? number)
        {
            if (string.IsNullOrWhiteSpace(number))
                return false;

            try
            {
                // محاولة تحويل النص إلى رقم
                return int.TryParse(number, out _);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة النص
        /// </summary>
        /// <param name="text">النص</param>
        /// <param name="minLength">الحد الأدنى للطول</param>
        /// <param name="maxLength">الحد الأقصى للطول</param>
        /// <returns>صحيح إذا كان النص صحيحاً</returns>
        public static bool IsValidText(string? text, int minLength = 1, int maxLength = int.MaxValue)
        {
            if (string.IsNullOrWhiteSpace(text))
                return false;

            try
            {
                // التحقق من طول النص
                return text.Length >= minLength && text.Length <= maxLength;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="minLength">الحد الأدنى للطول</param>
        /// <returns>صحيح إذا كانت كلمة المرور صحيحة</returns>
        public static bool IsValidPassword(string? password, int minLength = 8)
        {
            if (string.IsNullOrWhiteSpace(password))
                return false;

            try
            {
                // التحقق من طول كلمة المرور
                if (password.Length < minLength)
                    return false;

                // التحقق من وجود حرف كبير
                if (!password.Any(char.IsUpper))
                    return false;

                // التحقق من وجود حرف صغير
                if (!password.Any(char.IsLower))
                    return false;

                // التحقق من وجود رقم
                if (!password.Any(char.IsDigit))
                    return false;

                // التحقق من وجود رمز خاص
                if (!password.Any(c => !char.IsLetterOrDigit(c)))
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ValidationHelper.IsValidPassword");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="minLength">الحد الأدنى للطول</param>
        /// <returns>صحيح إذا كان اسم المستخدم صحيحاً</returns>
        public static bool IsValidUsername(string? username, int minLength = 3)
        {
            if (string.IsNullOrWhiteSpace(username))
                return false;

            try
            {
                // تعبير منتظم للتحقق من صحة اسم المستخدم
                var regex = new Regex(@"^[a-zA-Z0-9_]+$");
                return username.Length >= minLength && regex.IsMatch(username);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة الرمز
        /// </summary>
        /// <param name="code">الرمز</param>
        /// <returns>صحيح إذا كان الرمز صحيحاً</returns>
        public static bool IsValidCode(string? code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return false;

            try
            {
                // تعبير منتظم للتحقق من صحة الرمز
                var regex = new Regex(@"^[a-zA-Z0-9\-\.]+$");
                return regex.IsMatch(code);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ValidationHelper.IsValidCode");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة الرقم الضريبي
        /// </summary>
        /// <param name="taxNumber">الرقم الضريبي</param>
        /// <returns>صحيح إذا كان الرقم الضريبي صحيحاً</returns>
        public static bool IsValidTaxNumber(string? taxNumber)
        {
            if (string.IsNullOrWhiteSpace(taxNumber))
                return false;

            try
            {
                // تعبير منتظم للتحقق من صحة الرقم الضريبي
                var regex = new Regex(@"^[0-9]{9,15}$");
                return regex.IsMatch(taxNumber);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ValidationHelper.IsValidTaxNumber");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة العنوان
        /// </summary>
        /// <param name="address">العنوان</param>
        /// <returns>صحيح إذا كان العنوان صحيحاً</returns>
        public static bool IsValidAddress(string? address)
        {
            if (string.IsNullOrWhiteSpace(address))
                return false;

            try
            {
                // التحقق من طول العنوان
                return address.Length >= 5 && address.Length <= 200;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ValidationHelper.IsValidAddress");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة الرمز البريدي
        /// </summary>
        /// <param name="zipCode">الرمز البريدي</param>
        /// <returns>صحيح إذا كان الرمز البريدي صحيحاً</returns>
        public static bool IsValidZipCode(string? zipCode)
        {
            if (string.IsNullOrWhiteSpace(zipCode))
                return false;

            try
            {
                // تعبير منتظم للتحقق من صحة الرمز البريدي
                var regex = new Regex(@"^[0-9]{5,10}$");
                return regex.IsMatch(zipCode);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ValidationHelper.IsValidZipCode");
                return false;
            }
        }
    }
}
