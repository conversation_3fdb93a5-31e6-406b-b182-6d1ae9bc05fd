{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro\\SmartAccountPro.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro\\SmartAccountPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro\\SmartAccountPro.csproj", "projectName": "SmartAccountPro", "projectPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro\\SmartAccountPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"EPPlus": {"target": "Package", "version": "[8.0.5, )"}, "LiveChartsCore.SkiaSharpView.WPF": {"target": "Package", "version": "[2.0.0-rc5.4, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[6.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}