using SmartAccountPro.ViewModels;
using System;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Windows.Data;

namespace SmartAccountPro.Converters
{
    /// <summary>
    /// محول لحساب إجمالي الفواتير
    /// </summary>
    public class InvoicesTotalConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ObservableCollection<Invoice> invoices)
            {
                decimal total = invoices.Sum(i => i.Total);
                return total.ToString("N2");
            }
            
            return "0.00";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
