using System;
using System.Windows;
using System.Windows.Controls;
using SmartAccountPro.ViewModels;

namespace SmartAccountPro.Views
{
    /// <summary>
    /// نافذة تسجيل الدخول
    /// </summary>
    public partial class LoginWindow : Window
    {
        private LoginViewModel _viewModel;

        public LoginWindow()
        {
            InitializeComponent();
            _viewModel = (LoginViewModel)DataContext;
            _viewModel.LoginSuccessful += ViewModel_LoginSuccessful;
        }

        private void ViewModel_LoginSuccessful(object sender, EventArgs e)
        {
            // فتح النافذة الرئيسية
            var mainWindow = new MainWindow();
            mainWindow.Show();
            
            // إغلاق نافذة تسجيل الدخول
            this.Close();
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (_viewModel != null)
            {
                _viewModel.Password = PasswordBox.Password;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }
    }
}
