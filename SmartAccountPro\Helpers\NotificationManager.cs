using SmartAccountPro.Core.Models;
using SmartAccountPro.Core.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// مدير الإشعارات في التطبيق
    /// </summary>
    public class NotificationManager
    {
        private static NotificationManager? _instance;
        private readonly NotificationService _notificationService;
        private readonly ObservableCollection<Notification> _notifications;
        private int _currentUserId;
        private bool _isInitialized;

        /// <summary>
        /// الحصول على نسخة من مدير الإشعارات
        /// </summary>
        public static NotificationManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new NotificationManager();
                }
                return _instance;
            }
        }

        /// <summary>
        /// حالة تهيئة مدير الإشعارات
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// قائمة الإشعارات
        /// </summary>
        public ObservableCollection<Notification> Notifications => _notifications;

        /// <summary>
        /// عدد الإشعارات غير المقروءة
        /// </summary>
        public int UnreadCount => _notifications.Count(n => !n.IsRead);

        /// <summary>
        /// حدث تغيير الإشعارات
        /// </summary>
        public event EventHandler? NotificationsChanged;

        private NotificationManager()
        {
            _notifications = new ObservableCollection<Notification>();
            _notificationService = new NotificationService();
        }

        /// <summary>
        /// تهيئة مدير الإشعارات
        /// </summary>
        public async Task InitializeAsync(int userId)
        {
            try
            {
                _currentUserId = userId;
                await RefreshNotificationsAsync();
                _isInitialized = true;

                // قياس أداء تهيئة مدير الإشعارات
                PerformanceHelper.MeasureExecutionTime("NotificationManager.Initialize", () =>
                {
                    Debug.WriteLine($"تم تهيئة مدير الإشعارات للمستخدم {userId}");
                });
            }
            catch (Exception ex)
            {
                _isInitialized = false;
                ExceptionHandler.HandleException(ex, "NotificationManager.Initialize");
            }
        }

        /// <summary>
        /// تحديث قائمة الإشعارات
        /// </summary>
        public async Task RefreshNotificationsAsync()
        {
            try
            {
                if (_currentUserId <= 0)
                {
                    return;
                }

                var notifications = await _notificationService.GetUserNotificationsAsync(_currentUserId, true);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    _notifications.Clear();
                    foreach (var notification in notifications)
                    {
                        _notifications.Add(notification);
                    }

                    NotificationsChanged?.Invoke(this, EventArgs.Empty);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationManager.RefreshNotifications");
            }
        }

        /// <summary>
        /// إضافة إشعار جديد
        /// </summary>
        public async Task AddNotificationAsync(Notification notification)
        {
            try
            {
                var savedNotification = await _notificationService.AddNotificationAsync(notification);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    _notifications.Insert(0, savedNotification);
                    NotificationsChanged?.Invoke(this, EventArgs.Empty);
                });

                ShowNotificationPopup(notification);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationManager.AddNotification");
            }
        }

        /// <summary>
        /// إضافة إشعار جديد بشكل مبسط (غير متزامن)
        /// </summary>
        public async Task AddNotificationAsync(
            string title,
            string content,
            NotificationType type,
            int? entityId = null,
            string? entityType = null,
            string? link = null,
            bool isImportant = false)
        {
            try
            {
                if (_currentUserId <= 0)
                {
                    return;
                }

                var notification = await _notificationService.CreateNotificationAsync(
                    title,
                    content,
                    type,
                    _currentUserId,
                    entityId,
                    entityType,
                    link,
                    isImportant);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    _notifications.Insert(0, notification);
                    NotificationsChanged?.Invoke(this, EventArgs.Empty);
                });

                ShowNotificationPopup(notification);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationManager.AddNotification");
            }
        }

        /// <summary>
        /// تحديد إشعار كمقروء
        /// </summary>
        public async Task MarkAsReadAsync(int notificationId)
        {
            try
            {
                var success = await _notificationService.MarkAsReadAsync(notificationId, true);
                if (success)
                {
                    var notification = _notifications.FirstOrDefault(n => n.Id == notificationId);
                    if (notification != null)
                    {
                        notification.ReadDate = DateTime.Now;
                        NotificationsChanged?.Invoke(this, EventArgs.Empty);
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationManager.MarkAsRead");
            }
        }

        /// <summary>
        /// تحديد جميع الإشعارات كمقروءة
        /// </summary>
        public async Task MarkAllAsReadAsync()
        {
            try
            {
                if (_currentUserId <= 0)
                {
                    return;
                }

                var count = await _notificationService.MarkAllAsReadAsync(_currentUserId, true);
                if (count > 0)
                {
                    foreach (var notification in _notifications.Where(n => !n.IsRead))
                    {
                        notification.ReadDate = DateTime.Now;
                    }

                    NotificationsChanged?.Invoke(this, EventArgs.Empty);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationManager.MarkAllAsRead");
            }
        }

        /// <summary>
        /// حذف إشعار
        /// </summary>
        public async Task DeleteNotificationAsync(int notificationId)
        {
            try
            {
                var success = await _notificationService.DeleteNotificationAsync(notificationId, true);
                if (success)
                {
                    var notification = _notifications.FirstOrDefault(n => n.Id == notificationId);
                    if (notification != null)
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            _notifications.Remove(notification);
                            NotificationsChanged?.Invoke(this, EventArgs.Empty);
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationManager.DeleteNotification");
            }
        }

        /// <summary>
        /// حذف جميع الإشعارات المقروءة
        /// </summary>
        public async Task DeleteReadNotificationsAsync()
        {
            try
            {
                if (_currentUserId <= 0)
                {
                    return;
                }

                var count = await _notificationService.DeleteReadNotificationsAsync(_currentUserId);
                if (count > 0)
                {
                    var readNotifications = _notifications.Where(n => n.IsRead).ToList();

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        foreach (var notification in readNotifications)
                        {
                            _notifications.Remove(notification);
                        }

                        NotificationsChanged?.Invoke(this, EventArgs.Empty);
                    });
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationManager.DeleteReadNotifications");
            }
        }

        /// <summary>
        /// إضافة إشعار جديد بشكل مبسط (متزامن)
        /// </summary>
        public void AddNotification(
            string title,
            string content,
            NotificationType type,
            int? entityId = null,
            string? entityType = null,
            string? link = null,
            bool isImportant = false)
        {
            // استدعاء الطريقة غير المتزامنة بدون انتظار
            _ = AddNotificationAsync(title, content, type, entityId, entityType, link, isImportant);
        }

        /// <summary>
        /// عرض نافذة منبثقة للإشعار
        /// </summary>
        private void ShowNotificationPopup(Notification notification)
        {
            try
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var popup = new Views.NotificationPopup(notification);
                    popup.Show();
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationManager.ShowNotificationPopup");
            }
        }
    }
}
