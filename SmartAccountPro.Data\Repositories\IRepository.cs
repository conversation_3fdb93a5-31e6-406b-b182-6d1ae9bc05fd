using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace SmartAccountPro.Data.Repositories
{
    /// <summary>
    /// واجهة المستودع العامة
    /// </summary>
    /// <typeparam name="T">نوع الكيان</typeparam>
    public interface IRepository<T> where T : class
    {
        /// <summary>
        /// الحصول على كيان بواسطة المعرف
        /// </summary>
        Task<T> GetByIdAsync(int id);

        /// <summary>
        /// الحصول على جميع الكيانات
        /// </summary>
        Task<IEnumerable<T>> GetAllAsync();

        /// <summary>
        /// البحث عن كيانات بواسطة شرط
        /// </summary>
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// إضافة كيان جديد
        /// </summary>
        Task<T> AddAsync(T entity);

        /// <summary>
        /// إضافة مجموعة من الكيانات
        /// </summary>
        Task AddRangeAsync(IEnumerable<T> entities);

        /// <summary>
        /// تحديث كيان
        /// </summary>
        Task<bool> UpdateAsync(T entity);

        /// <summary>
        /// حذف كيان
        /// </summary>
        Task<bool> RemoveAsync(T entity);

        /// <summary>
        /// حذف كيان بواسطة المعرف
        /// </summary>
        Task<bool> RemoveByIdAsync(int id);

        /// <summary>
        /// حذف مجموعة من الكيانات
        /// </summary>
        Task RemoveRangeAsync(IEnumerable<T> entities);

        /// <summary>
        /// التحقق من وجود كيان بواسطة شرط
        /// </summary>
        Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// الحصول على عدد الكيانات
        /// </summary>
        Task<int> CountAsync();

        /// <summary>
        /// الحصول على عدد الكيانات بواسطة شرط
        /// </summary>
        Task<int> CountAsync(Expression<Func<T, bool>> predicate);
    }
}
