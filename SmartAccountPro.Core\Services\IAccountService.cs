using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Core.Services
{
    /// <summary>
    /// واجهة خدمة الحسابات
    /// </summary>
    public interface IAccountService
    {
        /// <summary>
        /// الحصول على جميع الحسابات
        /// </summary>
        Task<IEnumerable<Account>> GetAllAccountsAsync();

        /// <summary>
        /// الحصول على الحسابات الرئيسية
        /// </summary>
        Task<IEnumerable<Account>> GetRootAccountsAsync();

        /// <summary>
        /// الحصول على الحسابات الفرعية لحساب معين
        /// </summary>
        Task<IEnumerable<Account>> GetChildAccountsAsync(int parentAccountId);

        /// <summary>
        /// الحصول على حساب بواسطة المعرف
        /// </summary>
        Task<Account> GetAccountByIdAsync(int id);

        /// <summary>
        /// الحصول على حساب بواسطة الكود
        /// </summary>
        Task<Account> GetAccountByCodeAsync(string code);

        /// <summary>
        /// إنشاء حساب جديد
        /// </summary>
        Task<Account> CreateAccountAsync(Account account);

        /// <summary>
        /// تحديث حساب
        /// </summary>
        Task<bool> UpdateAccountAsync(Account account);

        /// <summary>
        /// حذف حساب
        /// </summary>
        Task<bool> DeleteAccountAsync(int id);

        /// <summary>
        /// الحصول على شجرة الحسابات
        /// </summary>
        Task<IEnumerable<Account>> GetAccountTreeAsync();

        /// <summary>
        /// الحصول على ميزان المراجعة
        /// </summary>
        Task<IEnumerable<Account>> GetTrialBalanceAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// تحديث رصيد الحساب
        /// </summary>
        Task<bool> UpdateAccountBalanceAsync(int accountId, decimal amount);
    }
}
