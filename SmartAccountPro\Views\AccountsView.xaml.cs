using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using SmartAccountPro.ViewModels;

namespace SmartAccountPro.Views
{
    /// <summary>
    /// شاشة إدارة الحسابات
    /// </summary>
    public partial class AccountsView : UserControl
    {
        private AccountsViewModel _viewModel;

        public AccountsView()
        {
            InitializeComponent();

            // تهيئة نموذج العرض
            _viewModel = new AccountsViewModel();
            DataContext = _viewModel;

            // قياس وقت تهيئة الواجهة
            PerformanceHelper.MeasureExecutionTime("AccountsView.Initialize", () =>
            {
                SetupEventHandlers();
            });
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            AddAccountButton.Click += AddAccountButton_Click;
            EditAccountButton.Click += EditAccountButton_Click;
            DeleteAccountButton.Click += DeleteAccountButton_Click;
            RefreshButton.Click += RefreshButton_Click;
            PrintButton.Click += PrintButton_Click;
            AccountsTreeView.SelectedItemChanged += AccountsTreeView_SelectedItemChanged;
        }

        /// <summary>
        /// معالج حدث النقر على زر إضافة حساب
        /// </summary>
        private void AddAccountButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة حساب جديد", "إضافة حساب", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// معالج حدث النقر على زر تعديل حساب
        /// </summary>
        private void EditAccountButton_Click(object sender, RoutedEventArgs e)
        {
            if (AccountsTreeView.SelectedItem == null)
            {
                MessageBox.Show("الرجاء اختيار حساب لتعديله", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBox.Show("سيتم فتح نافذة تعديل الحساب المحدد", "تعديل حساب", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// معالج حدث النقر على زر حذف حساب
        /// </summary>
        private void DeleteAccountButton_Click(object sender, RoutedEventArgs e)
        {
            if (AccountsTreeView.SelectedItem == null)
            {
                MessageBox.Show("الرجاء اختيار حساب لحذفه", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBoxResult result = MessageBox.Show(
                "هل أنت متأكد من حذف الحساب المحدد؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("تم حذف الحساب بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// معالج حدث النقر على زر تحديث
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تحديث بيانات الحسابات", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// معالج حدث النقر على زر طباعة
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم طباعة شجرة الحسابات", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// معالج حدث تغيير العنصر المحدد في شجرة الحسابات
        /// </summary>
        private void AccountsTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            try
            {
                if (AccountsTreeView.SelectedItem != null)
                {
                    Account selectedAccount = AccountsTreeView.SelectedItem as Account;
                    if (selectedAccount != null)
                    {
                        // تعيين الحساب المحدد في نموذج العرض
                        _viewModel.SelectedAccount = selectedAccount;

                        // قياس وقت تحميل بيانات الحساب
                        PerformanceHelper.MeasureExecutionTime("AccountsView.LoadAccountDetails", () =>
                        {
                            Debug.WriteLine($"تم اختيار الحساب: {selectedAccount.Name} (الكود: {selectedAccount.Code})");
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AccountsView.AccountsTreeView_SelectedItemChanged");
            }
        }
    }
}
