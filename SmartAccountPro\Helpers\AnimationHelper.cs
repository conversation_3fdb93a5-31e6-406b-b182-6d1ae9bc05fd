using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للرسوم المتحركة
    /// </summary>
    public static class AnimationHelper
    {
        /// <summary>
        /// تأثير ظهور تدريجي
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="duration">المدة</param>
        /// <param name="delay">التأخير</param>
        /// <returns>مهمة</returns>
        public static async Task FadeInAsync(UIElement element, TimeSpan duration, TimeSpan? delay = null)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // تعيين الشفافية الأولية
                element.Opacity = 0;
                element.Visibility = Visibility.Visible;

                // إنشاء الرسم المتحرك
                var animation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = duration,
                    BeginTime = delay
                };

                // تعيين دالة الإكمال
                var tcs = new TaskCompletionSource<bool>();
                animation.Completed += (s, e) => tcs.SetResult(true);

                // بدء الرسم المتحرك
                element.BeginAnimation(UIElement.OpacityProperty, animation);

                // انتظار اكتمال الرسم المتحرك
                await tcs.Task;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnimationHelper.FadeIn");
            }
        }

        /// <summary>
        /// تأثير اختفاء تدريجي
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="duration">المدة</param>
        /// <param name="delay">التأخير</param>
        /// <returns>مهمة</returns>
        public static async Task FadeOutAsync(UIElement element, TimeSpan duration, TimeSpan? delay = null)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء الرسم المتحرك
                var animation = new DoubleAnimation
                {
                    From = element.Opacity,
                    To = 0,
                    Duration = duration,
                    BeginTime = delay
                };

                // تعيين دالة الإكمال
                var tcs = new TaskCompletionSource<bool>();
                animation.Completed += (s, e) =>
                {
                    element.Visibility = Visibility.Collapsed;
                    tcs.SetResult(true);
                };

                // بدء الرسم المتحرك
                element.BeginAnimation(UIElement.OpacityProperty, animation);

                // انتظار اكتمال الرسم المتحرك
                await tcs.Task;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnimationHelper.FadeOut");
            }
        }

        /// <summary>
        /// تأثير انزلاق
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="from">من</param>
        /// <param name="to">إلى</param>
        /// <param name="duration">المدة</param>
        /// <param name="delay">التأخير</param>
        /// <returns>مهمة</returns>
        public static async Task SlideAsync(FrameworkElement element, Point from, Point to, TimeSpan duration, TimeSpan? delay = null)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء تحويل الإزاحة
                var transform = new TranslateTransform(from.X, from.Y);
                element.RenderTransform = transform;

                // إنشاء الرسم المتحرك للمحور X
                var animationX = new DoubleAnimation
                {
                    From = from.X,
                    To = to.X,
                    Duration = duration,
                    BeginTime = delay
                };

                // إنشاء الرسم المتحرك للمحور Y
                var animationY = new DoubleAnimation
                {
                    From = from.Y,
                    To = to.Y,
                    Duration = duration,
                    BeginTime = delay
                };

                // تعيين دالة الإكمال
                var tcs = new TaskCompletionSource<bool>();
                animationY.Completed += (s, e) => tcs.SetResult(true);

                // بدء الرسم المتحرك
                transform.BeginAnimation(TranslateTransform.XProperty, animationX);
                transform.BeginAnimation(TranslateTransform.YProperty, animationY);

                // انتظار اكتمال الرسم المتحرك
                await tcs.Task;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnimationHelper.Slide");
            }
        }

        /// <summary>
        /// تأثير تكبير/تصغير
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="from">من</param>
        /// <param name="to">إلى</param>
        /// <param name="duration">المدة</param>
        /// <param name="delay">التأخير</param>
        /// <returns>مهمة</returns>
        public static async Task ScaleAsync(FrameworkElement element, double from, double to, TimeSpan duration, TimeSpan? delay = null)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء تحويل المقياس
                var transform = new ScaleTransform(from, from);
                element.RenderTransform = transform;

                // إنشاء الرسم المتحرك للمحور X
                var animationX = new DoubleAnimation
                {
                    From = from,
                    To = to,
                    Duration = duration,
                    BeginTime = delay
                };

                // إنشاء الرسم المتحرك للمحور Y
                var animationY = new DoubleAnimation
                {
                    From = from,
                    To = to,
                    Duration = duration,
                    BeginTime = delay
                };

                // تعيين دالة الإكمال
                var tcs = new TaskCompletionSource<bool>();
                animationY.Completed += (s, e) => tcs.SetResult(true);

                // بدء الرسم المتحرك
                transform.BeginAnimation(ScaleTransform.ScaleXProperty, animationX);
                transform.BeginAnimation(ScaleTransform.ScaleYProperty, animationY);

                // انتظار اكتمال الرسم المتحرك
                await tcs.Task;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnimationHelper.Scale");
            }
        }

        /// <summary>
        /// تأثير دوران
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="from">من</param>
        /// <param name="to">إلى</param>
        /// <param name="duration">المدة</param>
        /// <param name="delay">التأخير</param>
        /// <returns>مهمة</returns>
        public static async Task RotateAsync(FrameworkElement element, double from, double to, TimeSpan duration, TimeSpan? delay = null)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء تحويل الدوران
                var transform = new RotateTransform(from);
                element.RenderTransform = transform;

                // إنشاء الرسم المتحرك
                var animation = new DoubleAnimation
                {
                    From = from,
                    To = to,
                    Duration = duration,
                    BeginTime = delay
                };

                // تعيين دالة الإكمال
                var tcs = new TaskCompletionSource<bool>();
                animation.Completed += (s, e) => tcs.SetResult(true);

                // بدء الرسم المتحرك
                transform.BeginAnimation(RotateTransform.AngleProperty, animation);

                // انتظار اكتمال الرسم المتحرك
                await tcs.Task;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnimationHelper.Rotate");
            }
        }

        /// <summary>
        /// تأثير تغيير اللون
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="property">الخاصية</param>
        /// <param name="from">من</param>
        /// <param name="to">إلى</param>
        /// <param name="duration">المدة</param>
        /// <param name="delay">التأخير</param>
        /// <returns>مهمة</returns>
        public static async Task ColorChangeAsync(DependencyObject element, DependencyProperty property, Color from, Color to, TimeSpan duration, TimeSpan? delay = null)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء الرسم المتحرك
                var animation = new ColorAnimation
                {
                    From = from,
                    To = to,
                    Duration = duration,
                    BeginTime = delay
                };

                // تعيين دالة الإكمال
                var tcs = new TaskCompletionSource<bool>();
                animation.Completed += (s, e) => tcs.SetResult(true);

                // بدء الرسم المتحرك
                element.BeginAnimation(property, animation);

                // انتظار اكتمال الرسم المتحرك
                await tcs.Task;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnimationHelper.ColorChange");
            }
        }

        /// <summary>
        /// تأثير نبض
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="duration">المدة</param>
        /// <param name="scale">المقياس</param>
        /// <param name="repeat">التكرار</param>
        /// <returns>مهمة</returns>
        public static async Task PulseAsync(FrameworkElement element, TimeSpan duration, double scale = 1.2, int repeat = 1)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء تحويل المقياس
                var transform = new ScaleTransform(1, 1);
                element.RenderTransform = transform;

                // إنشاء الرسم المتحرك للمحور X
                var animationX = new DoubleAnimation
                {
                    From = 1,
                    To = scale,
                    Duration = TimeSpan.FromMilliseconds(duration.TotalMilliseconds / 2),
                    AutoReverse = true,
                    RepeatBehavior = new RepeatBehavior(repeat)
                };

                // إنشاء الرسم المتحرك للمحور Y
                var animationY = new DoubleAnimation
                {
                    From = 1,
                    To = scale,
                    Duration = TimeSpan.FromMilliseconds(duration.TotalMilliseconds / 2),
                    AutoReverse = true,
                    RepeatBehavior = new RepeatBehavior(repeat)
                };

                // تعيين دالة الإكمال
                var tcs = new TaskCompletionSource<bool>();
                animationY.Completed += (s, e) => tcs.SetResult(true);

                // بدء الرسم المتحرك
                transform.BeginAnimation(ScaleTransform.ScaleXProperty, animationX);
                transform.BeginAnimation(ScaleTransform.ScaleYProperty, animationY);

                // انتظار اكتمال الرسم المتحرك
                await tcs.Task;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnimationHelper.Pulse");
            }
        }

        /// <summary>
        /// تأثير اهتزاز
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="duration">المدة</param>
        /// <param name="strength">القوة</param>
        /// <param name="repeat">التكرار</param>
        /// <returns>مهمة</returns>
        public static async Task ShakeAsync(FrameworkElement element, TimeSpan duration, double strength = 10, int repeat = 5)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء تحويل الإزاحة
                var transform = new TranslateTransform(0, 0);
                element.RenderTransform = transform;

                // إنشاء الرسم المتحرك
                var animation = new DoubleAnimation
                {
                    From = -strength,
                    To = strength,
                    Duration = TimeSpan.FromMilliseconds(duration.TotalMilliseconds / repeat),
                    AutoReverse = true,
                    RepeatBehavior = new RepeatBehavior(repeat)
                };

                // تعيين دالة الإكمال
                var tcs = new TaskCompletionSource<bool>();
                animation.Completed += (s, e) =>
                {
                    transform.BeginAnimation(TranslateTransform.XProperty, new DoubleAnimation(0, TimeSpan.FromMilliseconds(100)));
                    tcs.SetResult(true);
                };

                // بدء الرسم المتحرك
                transform.BeginAnimation(TranslateTransform.XProperty, animation);

                // انتظار اكتمال الرسم المتحرك
                await tcs.Task;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnimationHelper.Shake");
            }
        }
    }
}
