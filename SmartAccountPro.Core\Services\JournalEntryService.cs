using SmartAccountPro.Core.Models;
using SmartAccountPro.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SmartAccountPro.Core.Services
{
    /// <summary>
    /// تنفيذ خدمة القيود المحاسبية
    /// </summary>
    public class JournalEntryService : IJournalEntryService
    {
        private readonly IJournalEntryRepository _journalEntryRepository;

        public JournalEntryService(IJournalEntryRepository journalEntryRepository)
        {
            _journalEntryRepository = journalEntryRepository;
        }

        /// <summary>
        /// الحصول على جميع القيود المحاسبية
        /// </summary>
        public async Task<IEnumerable<JournalEntry>> GetAllJournalEntriesAsync()
        {
            return await _journalEntryRepository.GetAllAsync();
        }

        /// <summary>
        /// الحصول على القيود المحاسبية في فترة معينة
        /// </summary>
        public async Task<IEnumerable<JournalEntry>> GetJournalEntriesByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _journalEntryRepository.GetByDateRangeAsync(fromDate, toDate);
        }

        /// <summary>
        /// الحصول على قيد محاسبي بواسطة المعرف
        /// </summary>
        public async Task<JournalEntry> GetJournalEntryByIdAsync(int id)
        {
            return await _journalEntryRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// الحصول على قيد محاسبي بواسطة الرقم
        /// </summary>
        public async Task<JournalEntry> GetJournalEntryByNumberAsync(string entryNumber)
        {
            return await _journalEntryRepository.GetByNumberAsync(entryNumber);
        }

        /// <summary>
        /// إنشاء قيد محاسبي جديد
        /// </summary>
        public async Task<JournalEntry> CreateJournalEntryAsync(JournalEntry journalEntry)
        {
            // التحقق من توازن القيد
            if (!IsJournalEntryBalanced(journalEntry))
                throw new Exception("القيد غير متوازن، يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن");

            // إنشاء رقم قيد جديد إذا لم يكن موجوداً
            if (string.IsNullOrEmpty(journalEntry.EntryNumber))
                journalEntry.EntryNumber = await _journalEntryRepository.GenerateEntryNumberAsync();

            // حساب إجمالي المدين والدائن
            journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
            journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);

            // تعيين تاريخ الإنشاء
            journalEntry.CreatedAt = DateTime.Now;

            return await _journalEntryRepository.AddAsync(journalEntry);
        }

        /// <summary>
        /// تحديث قيد محاسبي
        /// </summary>
        public async Task<bool> UpdateJournalEntryAsync(JournalEntry journalEntry)
        {
            // التحقق من عدم ترحيل القيد
            var existingEntry = await _journalEntryRepository.GetByIdAsync(journalEntry.Id);
            if (existingEntry.IsPosted)
                throw new Exception("لا يمكن تعديل قيد مرحل");

            // التحقق من توازن القيد
            if (!IsJournalEntryBalanced(journalEntry))
                throw new Exception("القيد غير متوازن، يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن");

            // حساب إجمالي المدين والدائن
            journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
            journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);

            // تحديث تاريخ التعديل
            journalEntry.UpdatedAt = DateTime.Now;

            return await _journalEntryRepository.UpdateAsync(journalEntry);
        }

        /// <summary>
        /// حذف قيد محاسبي
        /// </summary>
        public async Task<bool> DeleteJournalEntryAsync(int id)
        {
            // التحقق من عدم ترحيل القيد
            var journalEntry = await _journalEntryRepository.GetByIdAsync(id);
            if (journalEntry.IsPosted)
                throw new Exception("لا يمكن حذف قيد مرحل");

            return await _journalEntryRepository.RemoveByIdAsync(id);
        }

        /// <summary>
        /// ترحيل قيد محاسبي
        /// </summary>
        public async Task<bool> PostJournalEntryAsync(int id, int userId)
        {
            return await _journalEntryRepository.PostJournalEntryAsync(id, userId);
        }

        /// <summary>
        /// إلغاء ترحيل قيد محاسبي
        /// </summary>
        public async Task<bool> UnpostJournalEntryAsync(int id)
        {
            return await _journalEntryRepository.UnpostJournalEntryAsync(id);
        }

        /// <summary>
        /// التحقق من توازن القيد المحاسبي
        /// </summary>
        public bool IsJournalEntryBalanced(JournalEntry journalEntry)
        {
            decimal totalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
            decimal totalCredit = journalEntry.Items.Sum(i => i.CreditAmount);
            return Math.Abs(totalDebit - totalCredit) < 0.01m; // للتعامل مع أخطاء التقريب
        }

        /// <summary>
        /// الحصول على دفتر اليومية
        /// </summary>
        public async Task<IEnumerable<JournalEntry>> GetJournalAsync(DateTime fromDate, DateTime toDate)
        {
            return await _journalEntryRepository.GetJournalAsync(fromDate, toDate);
        }

        /// <summary>
        /// الحصول على دفتر الأستاذ لحساب معين
        /// </summary>
        public async Task<IEnumerable<JournalEntryItem>> GetLedgerAsync(int accountId, DateTime fromDate, DateTime toDate)
        {
            return await _journalEntryRepository.GetLedgerAsync(accountId, fromDate, toDate);
        }
    }
}
