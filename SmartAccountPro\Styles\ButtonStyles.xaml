<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- أنماط الأزرار -->
    <Style x:Key="ModernButton" TargetType="Button">
        <Setter Property="Background" Value="#2196F3"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Padding" Value="15,10"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="5" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            BorderBrush="{TemplateBinding BorderBrush}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#1976D2"/>
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="1.03" ScaleY="1.03"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#0D47A1"/>
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="#BDBDBD"/>
                            <Setter Property="Foreground" Value="#757575"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- زر أخضر -->
    <Style x:Key="GreenButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
        <Setter Property="Background" Value="#4CAF50"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#388E3C"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#1B5E20"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- زر أحمر -->
    <Style x:Key="RedButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
        <Setter Property="Background" Value="#F44336"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#D32F2F"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#B71C1C"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- زر برتقالي -->
    <Style x:Key="OrangeButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
        <Setter Property="Background" Value="#FF9800"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#F57C00"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#E65100"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- زر بنفسجي -->
    <Style x:Key="PurpleButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
        <Setter Property="Background" Value="#9C27B0"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#7B1FA2"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#4A148C"/>
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>
