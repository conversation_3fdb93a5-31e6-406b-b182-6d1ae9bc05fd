using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لتحسين استخدام الذاكرة
    /// </summary>
    public static class MemoryOptimizer
    {
        // استدعاء دوال نظام التشغيل
        [DllImport("kernel32.dll")]
        private static extern bool SetProcessWorkingSetSize(IntPtr proc, int min, int max);

        [DllImport("psapi.dll")]
        private static extern bool GetProcessMemoryInfo(IntPtr hProcess, out PROCESS_MEMORY_COUNTERS counters, uint size);

        [StructLayout(LayoutKind.Sequential)]
        private struct PROCESS_MEMORY_COUNTERS
        {
            public uint cb;
            public uint PageFaultCount;
            public UIntPtr PeakWorkingSetSize;
            public UIntPtr WorkingSetSize;
            public UIntPtr QuotaPeakPagedPoolUsage;
            public UIntPtr QuotaPagedPoolUsage;
            public UIntPtr QuotaPeakNonPagedPoolUsage;
            public UIntPtr QuotaNonPagedPoolUsage;
            public UIntPtr PagefileUsage;
            public UIntPtr PeakPagefileUsage;
        }

        /// <summary>
        /// تحسين استخدام الذاكرة
        /// </summary>
        /// <returns>نجاح العملية</returns>
        public static bool OptimizeMemory()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("MemoryOptimizer.OptimizeMemory", () =>
                {
                    // الحصول على معلومات الذاكرة قبل التحسين
                    var memoryBefore = GetMemoryUsage();

                    // تنفيذ جمع القمامة
                    GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
                    GC.Collect(GC.MaxGeneration, GCCollectionMode.Forced, true, true);
                    GC.WaitForPendingFinalizers();
                    GC.Collect(GC.MaxGeneration, GCCollectionMode.Forced, true, true);

                    // تحرير الذاكرة الزائدة
                    SetProcessWorkingSetSize(Process.GetCurrentProcess().Handle, -1, -1);

                    // الحصول على معلومات الذاكرة بعد التحسين
                    var memoryAfter = GetMemoryUsage();

                    // حساب نسبة التحسين
                    double optimizationPercentage = 0;
                    if (memoryBefore > 0)
                    {
                        optimizationPercentage = ((memoryBefore - memoryAfter) / (double)memoryBefore) * 100;
                    }

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم تحسين استخدام الذاكرة",
                        $"تم تحسين استخدام الذاكرة بنسبة {optimizationPercentage:F2}%.",
                        NotificationType.Info);

                    Debug.WriteLine($"[MEMORY] تم تحسين استخدام الذاكرة من {FormatMemorySize(memoryBefore)} إلى {FormatMemorySize(memoryAfter)} (تحسين بنسبة {optimizationPercentage:F2}%)");

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MemoryOptimizer.OptimizeMemory");
                return false;
            }
        }

        /// <summary>
        /// الحصول على استخدام الذاكرة الحالي
        /// </summary>
        /// <returns>حجم الذاكرة المستخدمة بالبايت</returns>
        public static long GetMemoryUsage()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                PROCESS_MEMORY_COUNTERS counters;
                
                if (GetProcessMemoryInfo(process.Handle, out counters, (uint)Marshal.SizeOf(typeof(PROCESS_MEMORY_COUNTERS))))
                {
                    return (long)counters.WorkingSetSize;
                }
                
                return process.WorkingSet64;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MemoryOptimizer.GetMemoryUsage");
                return 0;
            }
        }

        /// <summary>
        /// تنسيق حجم الذاكرة
        /// </summary>
        /// <param name="bytes">حجم الذاكرة بالبايت</param>
        /// <returns>النص المنسق</returns>
        public static string FormatMemorySize(long bytes)
        {
            try
            {
                string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت" };
                double len = bytes;
                int order = 0;
                
                while (len >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    len = len / 1024;
                }
                
                return $"{len:0.##} {sizes[order]}";
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MemoryOptimizer.FormatMemorySize");
                return $"{bytes} بايت";
            }
        }

        /// <summary>
        /// مراقبة استخدام الذاكرة
        /// </summary>
        /// <param name="threshold">الحد الأقصى للذاكرة بالميجابايت</param>
        /// <returns>نجاح العملية</returns>
        public static bool MonitorMemoryUsage(int threshold = 500)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("MemoryOptimizer.MonitorMemoryUsage", () =>
                {
                    // الحصول على استخدام الذاكرة الحالي
                    var memoryUsage = GetMemoryUsage();
                    var memoryUsageMB = memoryUsage / (1024 * 1024);

                    // التحقق من تجاوز الحد الأقصى
                    if (memoryUsageMB > threshold)
                    {
                        // تحسين استخدام الذاكرة
                        OptimizeMemory();

                        // إضافة إشعار
                        NotificationManager.Instance.AddNotification(
                            "تنبيه استخدام الذاكرة",
                            $"تم تجاوز الحد الأقصى لاستخدام الذاكرة ({threshold} ميجابايت).",
                            NotificationType.Warning);

                        Debug.WriteLine($"[MEMORY] تنبيه: تم تجاوز الحد الأقصى لاستخدام الذاكرة ({threshold} ميجابايت).");
                    }

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MemoryOptimizer.MonitorMemoryUsage");
                return false;
            }
        }

        /// <summary>
        /// الحصول على تقرير استخدام الذاكرة
        /// </summary>
        /// <returns>تقرير استخدام الذاكرة</returns>
        public static string GetMemoryReport()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("MemoryOptimizer.GetMemoryReport", () =>
                {
                    var process = Process.GetCurrentProcess();
                    var sb = new StringBuilder();

                    sb.AppendLine("=== تقرير استخدام الذاكرة ===");
                    sb.AppendLine($"الذاكرة المستخدمة: {FormatMemorySize(process.WorkingSet64)}");
                    sb.AppendLine($"الذاكرة الخاصة: {FormatMemorySize(process.PrivateMemorySize64)}");
                    sb.AppendLine($"ذاكرة الصفحة: {FormatMemorySize(process.PagedMemorySize64)}");
                    sb.AppendLine($"ذاكرة الصفحة الخاصة: {FormatMemorySize(process.PagedSystemMemorySize64)}");
                    sb.AppendLine($"ذاكرة النظام غير المصفحة: {FormatMemorySize(process.NonpagedSystemMemorySize64)}");
                    sb.AppendLine($"أقصى استخدام للذاكرة: {FormatMemorySize(process.PeakWorkingSet64)}");
                    sb.AppendLine($"أقصى استخدام لذاكرة الصفحة: {FormatMemorySize(process.PeakPagedMemorySize64)}");
                    sb.AppendLine($"أقصى استخدام لذاكرة الصفحة الخاصة: {FormatMemorySize(process.PeakVirtualMemorySize64)}");
                    sb.AppendLine($"عدد مرات جمع القمامة (الجيل 0): {GC.CollectionCount(0)}");
                    sb.AppendLine($"عدد مرات جمع القمامة (الجيل 1): {GC.CollectionCount(1)}");
                    sb.AppendLine($"عدد مرات جمع القمامة (الجيل 2): {GC.CollectionCount(2)}");
                    sb.AppendLine($"إجمالي الذاكرة المخصصة: {FormatMemorySize(GC.GetTotalMemory(false))}");
                    sb.AppendLine($"وضع تجميع القمامة: {(GCSettings.IsServerGC ? "خادم" : "محطة عمل")}");
                    sb.AppendLine($"وضع ضغط الكائنات الكبيرة: {GCSettings.LargeObjectHeapCompactionMode}");
                    sb.AppendLine($"وقت تشغيل التطبيق: {process.TotalProcessorTime}");

                    return sb.ToString();
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MemoryOptimizer.GetMemoryReport");
                return "حدث خطأ أثناء إنشاء تقرير استخدام الذاكرة.";
            }
        }
    }
}
