using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Data;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// خدمة تحليل بيانات التطبيق
    /// </summary>
    public class AnalyticsService
    {
        private readonly AppDbContext _context;
        private readonly CacheManager<string, object> _cache;

        /// <summary>
        /// إنشاء خدمة التحليلات
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public AnalyticsService(AppDbContext context)
        {
            _context = context;
            _cache = new CacheManager<string, object>("AnalyticsService", TimeSpan.FromMinutes(15), TimeSpan.FromMinutes(30));
        }

        /// <summary>
        /// الحصول على إحصائيات عامة
        /// </summary>
        /// <returns>الإحصائيات العامة</returns>
        public async Task<GeneralStatistics> GetGeneralStatisticsAsync()
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("AnalyticsService.GetGeneralStatistics", async () =>
                {
                    string cacheKey = "GeneralStatistics";
                    
                    if (_cache.TryGetValue(cacheKey, out var cachedStats))
                    {
                        return (GeneralStatistics)cachedStats;
                    }

                    var stats = new GeneralStatistics
                    {
                        GeneratedAt = DateTime.Now,
                        TotalAccounts = await _context.Accounts.CountAsync(),
                        ActiveAccounts = await _context.Accounts.CountAsync(a => a.IsActive),
                        TotalUsers = await _context.Users.CountAsync(),
                        ActiveUsers = await _context.Users.CountAsync(u => u.IsActive),
                        TotalJournalEntries = await _context.JournalEntries.CountAsync(),
                        TotalInvoices = await _context.Invoices.CountAsync(),
                        TotalAttachments = await _context.Attachments.CountAsync()
                    };

                    // حساب إجمالي قيمة الأصول
                    var assetAccounts = await _context.Accounts
                        .Where(a => a.AccountType == AccountType.Asset && a.IsActive)
                        .Select(a => a.Id)
                        .ToListAsync();

                    stats.TotalAssets = await _context.JournalEntryDetails
                        .Where(d => assetAccounts.Contains(d.AccountId))
                        .SumAsync(d => d.DebitAmount - d.CreditAmount);

                    // حساب إجمالي قيمة الالتزامات
                    var liabilityAccounts = await _context.Accounts
                        .Where(a => a.AccountType == AccountType.Liability && a.IsActive)
                        .Select(a => a.Id)
                        .ToListAsync();

                    stats.TotalLiabilities = await _context.JournalEntryDetails
                        .Where(d => liabilityAccounts.Contains(d.AccountId))
                        .SumAsync(d => d.CreditAmount - d.DebitAmount);

                    // حساب إجمالي الإيرادات للشهر الحالي
                    var currentMonthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                    var revenueAccounts = await _context.Accounts
                        .Where(a => a.AccountType == AccountType.Revenue && a.IsActive)
                        .Select(a => a.Id)
                        .ToListAsync();

                    stats.MonthlyRevenue = await _context.JournalEntryDetails
                        .Where(d => revenueAccounts.Contains(d.AccountId) && 
                                   d.JournalEntry.EntryDate >= currentMonthStart)
                        .SumAsync(d => d.CreditAmount - d.DebitAmount);

                    // حساب إجمالي المصروفات للشهر الحالي
                    var expenseAccounts = await _context.Accounts
                        .Where(a => a.AccountType == AccountType.Expense && a.IsActive)
                        .Select(a => a.Id)
                        .ToListAsync();

                    stats.MonthlyExpenses = await _context.JournalEntryDetails
                        .Where(d => expenseAccounts.Contains(d.AccountId) && 
                                   d.JournalEntry.EntryDate >= currentMonthStart)
                        .SumAsync(d => d.DebitAmount - d.CreditAmount);

                    // حساب صافي الدخل الشهري
                    stats.MonthlyNetIncome = stats.MonthlyRevenue - stats.MonthlyExpenses;

                    _cache.Add(cacheKey, stats);
                    Debug.WriteLine("[ANALYTICS] تم إنشاء الإحصائيات العامة.");
                    return stats;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnalyticsService.GetGeneralStatistics");
                throw;
            }
        }

        /// <summary>
        /// الحصول على تحليل الإيرادات والمصروفات
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>تحليل الإيرادات والمصروفات</returns>
        public async Task<RevenueExpenseAnalysis> GetRevenueExpenseAnalysisAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("AnalyticsService.GetRevenueExpenseAnalysis", async () =>
                {
                    string cacheKey = $"RevenueExpenseAnalysis_{fromDate:yyyyMMdd}_{toDate:yyyyMMdd}";
                    
                    if (_cache.TryGetValue(cacheKey, out var cachedAnalysis))
                    {
                        return (RevenueExpenseAnalysis)cachedAnalysis;
                    }

                    var analysis = new RevenueExpenseAnalysis
                    {
                        FromDate = fromDate,
                        ToDate = toDate,
                        GeneratedAt = DateTime.Now
                    };

                    // تحليل الإيرادات
                    var revenueQuery = from account in _context.Accounts
                                      join detail in _context.JournalEntryDetails on account.Id equals detail.AccountId
                                      join entry in _context.JournalEntries on detail.JournalEntryId equals entry.Id
                                      where account.AccountType == AccountType.Revenue && 
                                            entry.EntryDate >= fromDate && entry.EntryDate <= toDate
                                      group new { account, detail } by new { account.Id, account.Name } into g
                                      select new AccountAnalysis
                                      {
                                          AccountId = g.Key.Id,
                                          AccountName = g.Key.Name,
                                          TotalAmount = g.Sum(x => x.detail.CreditAmount - x.detail.DebitAmount),
                                          TransactionCount = g.Count()
                                      };

                    analysis.RevenueAccounts = await revenueQuery.ToListAsync();
                    analysis.TotalRevenue = analysis.RevenueAccounts.Sum(r => r.TotalAmount);

                    // تحليل المصروفات
                    var expenseQuery = from account in _context.Accounts
                                      join detail in _context.JournalEntryDetails on account.Id equals detail.AccountId
                                      join entry in _context.JournalEntries on detail.JournalEntryId equals entry.Id
                                      where account.AccountType == AccountType.Expense && 
                                            entry.EntryDate >= fromDate && entry.EntryDate <= toDate
                                      group new { account, detail } by new { account.Id, account.Name } into g
                                      select new AccountAnalysis
                                      {
                                          AccountId = g.Key.Id,
                                          AccountName = g.Key.Name,
                                          TotalAmount = g.Sum(x => x.detail.DebitAmount - x.detail.CreditAmount),
                                          TransactionCount = g.Count()
                                      };

                    analysis.ExpenseAccounts = await expenseQuery.ToListAsync();
                    analysis.TotalExpenses = analysis.ExpenseAccounts.Sum(e => e.TotalAmount);

                    // حساب صافي الدخل
                    analysis.NetIncome = analysis.TotalRevenue - analysis.TotalExpenses;

                    // حساب هامش الربح
                    analysis.ProfitMargin = analysis.TotalRevenue != 0 ? 
                        (analysis.NetIncome / analysis.TotalRevenue) * 100 : 0;

                    _cache.Add(cacheKey, analysis);
                    Debug.WriteLine($"[ANALYTICS] تم إنشاء تحليل الإيرادات والمصروفات للفترة {fromDate:yyyy-MM-dd} - {toDate:yyyy-MM-dd}.");
                    return analysis;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnalyticsService.GetRevenueExpenseAnalysis");
                throw;
            }
        }

        /// <summary>
        /// الحصول على تحليل نشاط المستخدمين
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>تحليل نشاط المستخدمين</returns>
        public async Task<UserActivityAnalysis> GetUserActivityAnalysisAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("AnalyticsService.GetUserActivityAnalysis", async () =>
                {
                    string cacheKey = $"UserActivityAnalysis_{fromDate:yyyyMMdd}_{toDate:yyyyMMdd}";
                    
                    if (_cache.TryGetValue(cacheKey, out var cachedAnalysis))
                    {
                        return (UserActivityAnalysis)cachedAnalysis;
                    }

                    var analysis = new UserActivityAnalysis
                    {
                        FromDate = fromDate,
                        ToDate = toDate,
                        GeneratedAt = DateTime.Now
                    };

                    // تحليل نشاط المستخدمين في سجل التدقيق
                    var userActivityQuery = from user in _context.Users
                                           join audit in _context.AuditLogs on user.Id equals audit.UserId
                                           where audit.Timestamp >= fromDate && audit.Timestamp <= toDate
                                           group audit by new { user.Id, user.Username, user.FullName } into g
                                           select new UserActivity
                                           {
                                               UserId = g.Key.Id,
                                               Username = g.Key.Username,
                                               FullName = g.Key.FullName,
                                               ActivityCount = g.Count(),
                                               LastActivity = g.Max(a => a.Timestamp)
                                           };

                    analysis.UserActivities = await userActivityQuery.ToListAsync();

                    // حساب إحصائيات إضافية
                    analysis.TotalUsers = analysis.UserActivities.Count;
                    analysis.TotalActivities = analysis.UserActivities.Sum(u => u.ActivityCount);
                    analysis.AverageActivitiesPerUser = analysis.TotalUsers > 0 ? 
                        (double)analysis.TotalActivities / analysis.TotalUsers : 0;

                    // المستخدم الأكثر نشاطاً
                    analysis.MostActiveUser = analysis.UserActivities
                        .OrderByDescending(u => u.ActivityCount)
                        .FirstOrDefault();

                    _cache.Add(cacheKey, analysis);
                    Debug.WriteLine($"[ANALYTICS] تم إنشاء تحليل نشاط المستخدمين للفترة {fromDate:yyyy-MM-dd} - {toDate:yyyy-MM-dd}.");
                    return analysis;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnalyticsService.GetUserActivityAnalysis");
                throw;
            }
        }

        /// <summary>
        /// الحصول على تحليل الاتجاهات الشهرية
        /// </summary>
        /// <param name="months">عدد الأشهر</param>
        /// <returns>تحليل الاتجاهات الشهرية</returns>
        public async Task<MonthlyTrendAnalysis> GetMonthlyTrendAnalysisAsync(int months = 12)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("AnalyticsService.GetMonthlyTrendAnalysis", async () =>
                {
                    string cacheKey = $"MonthlyTrendAnalysis_{months}";
                    
                    if (_cache.TryGetValue(cacheKey, out var cachedAnalysis))
                    {
                        return (MonthlyTrendAnalysis)cachedAnalysis;
                    }

                    var analysis = new MonthlyTrendAnalysis
                    {
                        Months = months,
                        GeneratedAt = DateTime.Now
                    };

                    var startDate = DateTime.Now.AddMonths(-months);

                    // تحليل الاتجاهات الشهرية للإيرادات والمصروفات
                    for (int i = 0; i < months; i++)
                    {
                        var monthStart = startDate.AddMonths(i);
                        var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                        var monthlyData = await GetRevenueExpenseAnalysisAsync(monthStart, monthEnd);

                        analysis.MonthlyData.Add(new MonthlyDataPoint
                        {
                            Month = monthStart,
                            Revenue = monthlyData.TotalRevenue,
                            Expenses = monthlyData.TotalExpenses,
                            NetIncome = monthlyData.NetIncome,
                            TransactionCount = await _context.JournalEntries
                                .CountAsync(j => j.EntryDate >= monthStart && j.EntryDate <= monthEnd)
                        });
                    }

                    // حساب معدلات النمو
                    for (int i = 1; i < analysis.MonthlyData.Count; i++)
                    {
                        var current = analysis.MonthlyData[i];
                        var previous = analysis.MonthlyData[i - 1];

                        current.RevenueGrowthRate = previous.Revenue != 0 ? 
                            ((current.Revenue - previous.Revenue) / previous.Revenue) * 100 : 0;

                        current.ExpenseGrowthRate = previous.Expenses != 0 ? 
                            ((current.Expenses - previous.Expenses) / previous.Expenses) * 100 : 0;
                    }

                    // حساب المتوسطات
                    analysis.AverageMonthlyRevenue = analysis.MonthlyData.Average(m => m.Revenue);
                    analysis.AverageMonthlyExpenses = analysis.MonthlyData.Average(m => m.Expenses);
                    analysis.AverageMonthlyNetIncome = analysis.MonthlyData.Average(m => m.NetIncome);

                    _cache.Add(cacheKey, analysis);
                    Debug.WriteLine($"[ANALYTICS] تم إنشاء تحليل الاتجاهات الشهرية لـ {months} شهر.");
                    return analysis;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnalyticsService.GetMonthlyTrendAnalysis");
                throw;
            }
        }

        /// <summary>
        /// الحصول على تحليل الأداء المالي
        /// </summary>
        /// <param name="asOfDate">تاريخ التحليل</param>
        /// <returns>تحليل الأداء المالي</returns>
        public async Task<FinancialPerformanceAnalysis> GetFinancialPerformanceAnalysisAsync(DateTime asOfDate)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("AnalyticsService.GetFinancialPerformanceAnalysis", async () =>
                {
                    string cacheKey = $"FinancialPerformanceAnalysis_{asOfDate:yyyyMMdd}";
                    
                    if (_cache.TryGetValue(cacheKey, out var cachedAnalysis))
                    {
                        return (FinancialPerformanceAnalysis)cachedAnalysis;
                    }

                    var analysis = new FinancialPerformanceAnalysis
                    {
                        AsOfDate = asOfDate,
                        GeneratedAt = DateTime.Now
                    };

                    // حساب النسب المالية
                    var generalStats = await GetGeneralStatisticsAsync();

                    // نسبة السيولة (الأصول المتداولة / الالتزامات المتداولة)
                    analysis.CurrentRatio = generalStats.TotalLiabilities != 0 ? 
                        generalStats.TotalAssets / generalStats.TotalLiabilities : 0;

                    // نسبة الدين إلى حقوق الملكية
                    var equity = generalStats.TotalAssets - generalStats.TotalLiabilities;
                    analysis.DebtToEquityRatio = equity != 0 ? 
                        generalStats.TotalLiabilities / equity : 0;

                    // معدل العائد على الأصول (ROA)
                    var yearlyRevenue = await GetYearlyRevenueAsync(asOfDate);
                    analysis.ReturnOnAssets = generalStats.TotalAssets != 0 ? 
                        (yearlyRevenue / generalStats.TotalAssets) * 100 : 0;

                    // معدل العائد على حقوق الملكية (ROE)
                    analysis.ReturnOnEquity = equity != 0 ? 
                        (yearlyRevenue / equity) * 100 : 0;

                    // تقييم الأداء
                    analysis.PerformanceRating = CalculatePerformanceRating(analysis);

                    _cache.Add(cacheKey, analysis);
                    Debug.WriteLine($"[ANALYTICS] تم إنشاء تحليل الأداء المالي لتاريخ {asOfDate:yyyy-MM-dd}.");
                    return analysis;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnalyticsService.GetFinancialPerformanceAnalysis");
                throw;
            }
        }

        /// <summary>
        /// مسح ذاكرة التخزين المؤقت
        /// </summary>
        public void ClearCache()
        {
            try
            {
                PerformanceHelper.MeasureExecutionTime("AnalyticsService.ClearCache", () =>
                {
                    _cache.Clear();
                    Debug.WriteLine("[ANALYTICS] تم مسح ذاكرة التخزين المؤقت للتحليلات.");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AnalyticsService.ClearCache");
            }
        }

        /// <summary>
        /// الحصول على الإيرادات السنوية
        /// </summary>
        private async Task<decimal> GetYearlyRevenueAsync(DateTime asOfDate)
        {
            var yearStart = new DateTime(asOfDate.Year, 1, 1);
            var yearEnd = new DateTime(asOfDate.Year, 12, 31);

            var revenueAccounts = await _context.Accounts
                .Where(a => a.AccountType == AccountType.Revenue && a.IsActive)
                .Select(a => a.Id)
                .ToListAsync();

            return await _context.JournalEntryDetails
                .Where(d => revenueAccounts.Contains(d.AccountId) && 
                           d.JournalEntry.EntryDate >= yearStart && 
                           d.JournalEntry.EntryDate <= yearEnd)
                .SumAsync(d => d.CreditAmount - d.DebitAmount);
        }

        /// <summary>
        /// حساب تقييم الأداء
        /// </summary>
        private string CalculatePerformanceRating(FinancialPerformanceAnalysis analysis)
        {
            int score = 0;

            // تقييم نسبة السيولة
            if (analysis.CurrentRatio >= 2) score += 25;
            else if (analysis.CurrentRatio >= 1.5) score += 20;
            else if (analysis.CurrentRatio >= 1) score += 15;
            else score += 5;

            // تقييم نسبة الدين إلى حقوق الملكية
            if (analysis.DebtToEquityRatio <= 0.3) score += 25;
            else if (analysis.DebtToEquityRatio <= 0.5) score += 20;
            else if (analysis.DebtToEquityRatio <= 1) score += 15;
            else score += 5;

            // تقييم معدل العائد على الأصول
            if (analysis.ReturnOnAssets >= 15) score += 25;
            else if (analysis.ReturnOnAssets >= 10) score += 20;
            else if (analysis.ReturnOnAssets >= 5) score += 15;
            else score += 5;

            // تقييم معدل العائد على حقوق الملكية
            if (analysis.ReturnOnEquity >= 20) score += 25;
            else if (analysis.ReturnOnEquity >= 15) score += 20;
            else if (analysis.ReturnOnEquity >= 10) score += 15;
            else score += 5;

            return score switch
            {
                >= 90 => "ممتاز",
                >= 80 => "جيد جداً",
                >= 70 => "جيد",
                >= 60 => "مقبول",
                _ => "ضعيف"
            };
        }
    }

    // فئات البيانات للتحليلات
    public class GeneralStatistics
    {
        public DateTime GeneratedAt { get; set; }
        public int TotalAccounts { get; set; }
        public int ActiveAccounts { get; set; }
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int TotalJournalEntries { get; set; }
        public int TotalInvoices { get; set; }
        public int TotalAttachments { get; set; }
        public decimal TotalAssets { get; set; }
        public decimal TotalLiabilities { get; set; }
        public decimal MonthlyRevenue { get; set; }
        public decimal MonthlyExpenses { get; set; }
        public decimal MonthlyNetIncome { get; set; }
    }

    public class RevenueExpenseAnalysis
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public DateTime GeneratedAt { get; set; }
        public List<AccountAnalysis> RevenueAccounts { get; set; } = new List<AccountAnalysis>();
        public List<AccountAnalysis> ExpenseAccounts { get; set; } = new List<AccountAnalysis>();
        public decimal TotalRevenue { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetIncome { get; set; }
        public decimal ProfitMargin { get; set; }
    }

    public class UserActivityAnalysis
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public DateTime GeneratedAt { get; set; }
        public List<UserActivity> UserActivities { get; set; } = new List<UserActivity>();
        public int TotalUsers { get; set; }
        public int TotalActivities { get; set; }
        public double AverageActivitiesPerUser { get; set; }
        public UserActivity? MostActiveUser { get; set; }
    }

    public class MonthlyTrendAnalysis
    {
        public int Months { get; set; }
        public DateTime GeneratedAt { get; set; }
        public List<MonthlyDataPoint> MonthlyData { get; set; } = new List<MonthlyDataPoint>();
        public decimal AverageMonthlyRevenue { get; set; }
        public decimal AverageMonthlyExpenses { get; set; }
        public decimal AverageMonthlyNetIncome { get; set; }
    }

    public class FinancialPerformanceAnalysis
    {
        public DateTime AsOfDate { get; set; }
        public DateTime GeneratedAt { get; set; }
        public decimal CurrentRatio { get; set; }
        public decimal DebtToEquityRatio { get; set; }
        public decimal ReturnOnAssets { get; set; }
        public decimal ReturnOnEquity { get; set; }
        public string PerformanceRating { get; set; } = string.Empty;
    }

    public class AccountAnalysis
    {
        public int AccountId { get; set; }
        public string AccountName { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public int TransactionCount { get; set; }
    }

    public class UserActivity
    {
        public int UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public int ActivityCount { get; set; }
        public DateTime LastActivity { get; set; }
    }

    public class MonthlyDataPoint
    {
        public DateTime Month { get; set; }
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public decimal NetIncome { get; set; }
        public int TransactionCount { get; set; }
        public decimal RevenueGrowthRate { get; set; }
        public decimal ExpenseGrowthRate { get; set; }
    }
}
