﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using SmartAccountPro.ViewModels;
using SmartAccountPro.Views;

namespace SmartAccountPro
{
    /// <summary>
    /// النافذة الرئيسية للتطبيق
    /// </summary>
    public partial class MainWindow : Window
    {
        private DispatcherTimer _timer = null!;

        public MainWindow()
        {
            InitializeComponent();
            InitializeTimer();
            SetupEventHandlers();
            InitializeTheme();
            InitializeNotifications();
            ShowDashboard();
        }

        /// <summary>
        /// تهيئة نظام الإشعارات
        /// </summary>
        private async void InitializeNotifications()
        {
            try
            {
                // تهيئة مدير الإشعارات
                await NotificationManager.Instance.InitializeAsync(1); // استخدام معرف المستخدم الحالي

                // الاشتراك في حدث تغيير الإشعارات
                NotificationManager.Instance.NotificationsChanged += OnNotificationsChanged;

                // تحديث عداد الإشعارات
                UpdateNotificationBadge();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.InitializeNotifications");
            }
        }

        /// <summary>
        /// معالج حدث تغيير الإشعارات
        /// </summary>
        private void OnNotificationsChanged(object? sender, EventArgs e)
        {
            UpdateNotificationBadge();
        }

        /// <summary>
        /// تحديث عداد الإشعارات
        /// </summary>
        private void UpdateNotificationBadge()
        {
            try
            {
                var unreadCount = NotificationManager.Instance.UnreadCount;

                if (unreadCount > 0)
                {
                    NotificationCount.Text = unreadCount > 9 ? "9+" : unreadCount.ToString();
                    NotificationBadge.Visibility = Visibility.Visible;
                }
                else
                {
                    NotificationBadge.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.UpdateNotificationBadge");
            }
        }

        /// <summary>
        /// تهيئة السمة
        /// </summary>
        private void InitializeTheme()
        {
            try
            {
                // تهيئة مدير السمات
                ThemeManager.Initialize();

                // تحديث أيقونة السمة
                if (ThemeManager.CurrentTheme == ThemeManager.ThemeType.Dark)
                {
                    ThemeIcon.Text = "\uE708"; // أيقونة الشمس
                }
                else
                {
                    ThemeIcon.Text = "\uE706"; // أيقونة القمر
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.InitializeTheme");
            }
        }

        /// <summary>
        /// تهيئة المؤقت لعرض الوقت الحالي
        /// </summary>
        private void InitializeTimer()
        {
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();
            UpdateDateTime();
        }

        /// <summary>
        /// تحديث الوقت والتاريخ
        /// </summary>
        private void Timer_Tick(object? sender, EventArgs e)
        {
            UpdateDateTime();
        }

        /// <summary>
        /// تحديث نص الوقت والتاريخ
        /// </summary>
        private void UpdateDateTime()
        {
            DateTimeText.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
            StatusDateTimeText.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
        }

        /// <summary>
        /// معالج حدث النقر على زر تبديل الشريط الجانبي
        /// </summary>
        private void ToggleSidebarButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تبديل عرض الشريط الجانبي
                var columnDefinitions = ((Grid)Content).ColumnDefinitions;

                if (columnDefinitions[0].Width.Value == 250)
                {
                    // إخفاء الشريط الجانبي
                    columnDefinitions[0].Width = new GridLength(0);
                }
                else
                {
                    // إظهار الشريط الجانبي
                    columnDefinitions[0].Width = new GridLength(250);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.ToggleSidebarButton_Click");
            }
        }

        /// <summary>
        /// معالج حدث النقر على زر تبديل السمة
        /// </summary>
        private void ThemeToggleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تبديل السمة
                ThemeManager.ToggleTheme();

                // تحديث أيقونة السمة
                if (ThemeManager.CurrentTheme == ThemeManager.ThemeType.Dark)
                {
                    ThemeIcon.Text = "\uE708"; // أيقونة الشمس
                }
                else
                {
                    ThemeIcon.Text = "\uE706"; // أيقونة القمر
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.ThemeToggleButton_Click");
            }
        }

        /// <summary>
        /// معالج حدث النقر على زر الإشعارات
        /// </summary>
        private void NotificationsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // عرض شاشة الإشعارات
                var notificationsView = new NotificationsView();
                MainContent.Content = notificationsView;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.NotificationsButton_Click");
            }
        }

        /// <summary>
        /// معالج حدث النقر على زر إنشاء إشعار تجريبي
        /// </summary>
        private async void TestNotificationButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء إشعار تجريبي
                Random random = new Random();
                var notificationTypes = Enum.GetValues(typeof(NotificationType));
                var randomType = (NotificationType)notificationTypes.GetValue(random.Next(notificationTypes.Length));

                string title = "إشعار تجريبي";
                string content = $"هذا إشعار تجريبي من نوع {randomType}. تم إنشاؤه في {DateTime.Now:yyyy/MM/dd HH:mm:ss}";

                await NotificationManager.Instance.AddNotificationAsync(
                    title,
                    content,
                    randomType,
                    null,
                    null,
                    null,
                    random.Next(2) == 0); // إشعار مهم بشكل عشوائي
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.TestNotificationButton_Click");
            }
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // إعداد معالج حدث تغيير الوقت
            // تم تعيين معالج الحدث بالفعل في InitializeTimer

            // إعداد معالج حدث التنقل في الشريط الجانبي
            if (Sidebar != null)
            {
                var sidebarViewModel = Sidebar.DataContext as ViewModels.SidebarViewModel;
                if (sidebarViewModel != null)
                {
                    sidebarViewModel.Navigate += OnSidebarNavigate;
                    sidebarViewModel.Logout += OnSidebarLogout;
                }
            }
        }

        /// <summary>
        /// معالج حدث تسجيل الخروج
        /// </summary>
        private void OnSidebarLogout(object? sender, EventArgs e)
        {
            try
            {
                // عرض رسالة تأكيد
                MessageBoxResult result = MessageBox.Show(
                    "هل أنت متأكد من أنك تريد تسجيل الخروج؟",
                    "تأكيد تسجيل الخروج",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // عرض شاشة تسجيل الدخول
                    ShowDashboard();
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.OnSidebarLogout");
            }
        }

        /// <summary>
        /// معالج حدث التنقل في الشريط الجانبي
        /// </summary>
        private void OnSidebarNavigate(object? sender, string viewName)
        {
            try
            {
                switch (viewName)
                {
                    case "Dashboard":
                        ShowDashboard();
                        break;
                    case "Settings":
                        ShowSettings();
                        break;
                    case "EnhancedReports":
                        ShowEnhancedReports();
                        break;
                    case "FinancialReports":
                        ShowFinancialReports();
                        break;
                    case "Tasks":
                        ShowTasks();
                        break;
                    // يمكن إضافة المزيد من الحالات هنا
                    default:
                        // عرض رسالة أن الشاشة غير متوفرة حالياً
                        MessageBox.Show($"الشاشة {viewName} غير متوفرة حالياً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                        break;
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.OnSidebarNavigate");
            }
        }

        /// <summary>
        /// عرض لوحة التحكم
        /// </summary>
        private void ShowDashboard()
        {
            // عرض شاشة تسجيل الدخول
            var loginView = new LoginView();
            var loginViewModel = loginView.DataContext as LoginViewModel;

            if (loginViewModel != null)
            {
                // الاشتراك في حدث تسجيل الدخول الناجح
                loginViewModel.LoginSuccessful += (sender, e) =>
                {
                    // عرض لوحة التحكم بعد تسجيل الدخول
                    var dashboardView = new DashboardView();
                    MainContent.Content = dashboardView;
                };
            }

            MainContent.Content = loginView;
        }

        /// <summary>
        /// عرض شاشة الإعدادات
        /// </summary>
        private void ShowSettings()
        {
            try
            {
                var settingsView = new SettingsView();
                MainContent.Content = settingsView;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.ShowSettings");
            }
        }

        /// <summary>
        /// عرض شاشة التقارير المحسنة
        /// </summary>
        private void ShowEnhancedReports()
        {
            try
            {
                var enhancedReportsView = new EnhancedReportsView();
                MainContent.Content = enhancedReportsView;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.ShowEnhancedReports");
            }
        }

        /// <summary>
        /// عرض شاشة المهام
        /// </summary>
        private void ShowTasks()
        {
            try
            {
                var tasksView = new TasksView();
                MainContent.Content = tasksView;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.ShowTasks");
            }
        }

        /// <summary>
        /// عرض شاشة التقارير المالية
        /// </summary>
        private void ShowFinancialReports()
        {
            try
            {
                var financialReportsView = new FinancialReportsView();
                MainContent.Content = financialReportsView;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "MainWindow.ShowFinancialReports");
            }
        }

        /// <summary>
        /// معالج حدث إغلاق التطبيق
        /// </summary>
        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            MessageBoxResult result = MessageBox.Show(
                "هل أنت متأكد من أنك تريد إغلاق التطبيق؟",
                "تأكيد الخروج",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.No)
            {
                e.Cancel = true;
            }
            else
            {
                base.OnClosing(e);
            }
        }
    }
}
