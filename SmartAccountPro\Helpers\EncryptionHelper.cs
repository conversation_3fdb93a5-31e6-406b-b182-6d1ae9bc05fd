using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للتشفير وفك التشفير
    /// </summary>
    public static class EncryptionHelper
    {
        // حجم مفتاح التشفير
        private const int KeySize = 256;
        
        // حجم متجه التهيئة
        private const int IvSize = 16;
        
        // عدد تكرارات الاشتقاق
        private const int DerivationIterations = 1000;

        /// <summary>
        /// تشفير نص
        /// </summary>
        /// <param name="plainText">النص المراد تشفيره</param>
        /// <param name="passPhrase">كلمة المرور</param>
        /// <returns>النص المشفر</returns>
        public static string Encrypt(string plainText, string passPhrase)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("EncryptionHelper.Encrypt", () =>
                {
                    // التحقق من المدخلات
                    if (string.IsNullOrEmpty(plainText))
                        return string.Empty;
                    
                    if (string.IsNullOrEmpty(passPhrase))
                        throw new ArgumentNullException(nameof(passPhrase), "كلمة المرور مطلوبة للتشفير.");

                    // تحويل النص إلى مصفوفة بايت
                    byte[] plainTextBytes = Encoding.UTF8.GetBytes(plainText);
                    
                    // إنشاء متجه التهيئة
                    byte[] saltBytes = GenerateRandomBytes(IvSize);
                    
                    // إنشاء مفتاح التشفير
                    byte[] keyBytes = GenerateKey(passPhrase, saltBytes);
                    
                    // إنشاء خوارزمية التشفير
                    using (var aes = Aes.Create())
                    {
                        aes.KeySize = KeySize;
                        aes.BlockSize = 128;
                        aes.Mode = CipherMode.CBC;
                        aes.Padding = PaddingMode.PKCS7;
                        aes.Key = keyBytes;
                        aes.IV = saltBytes;

                        // إنشاء محول التشفير
                        using (var encryptor = aes.CreateEncryptor())
                        {
                            // تشفير النص
                            using (var memoryStream = new MemoryStream())
                            {
                                using (var cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write))
                                {
                                    cryptoStream.Write(plainTextBytes, 0, plainTextBytes.Length);
                                    cryptoStream.FlushFinalBlock();
                                }

                                // دمج متجه التهيئة مع النص المشفر
                                byte[] cipherTextBytes = saltBytes.Concat(memoryStream.ToArray()).ToArray();
                                
                                // تحويل النص المشفر إلى سلسلة Base64
                                return Convert.ToBase64String(cipherTextBytes);
                            }
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EncryptionHelper.Encrypt");
                throw;
            }
        }

        /// <summary>
        /// فك تشفير نص
        /// </summary>
        /// <param name="cipherText">النص المشفر</param>
        /// <param name="passPhrase">كلمة المرور</param>
        /// <returns>النص الأصلي</returns>
        public static string Decrypt(string cipherText, string passPhrase)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("EncryptionHelper.Decrypt", () =>
                {
                    // التحقق من المدخلات
                    if (string.IsNullOrEmpty(cipherText))
                        return string.Empty;
                    
                    if (string.IsNullOrEmpty(passPhrase))
                        throw new ArgumentNullException(nameof(passPhrase), "كلمة المرور مطلوبة لفك التشفير.");

                    // تحويل النص المشفر من سلسلة Base64 إلى مصفوفة بايت
                    byte[] cipherTextBytes = Convert.FromBase64String(cipherText);
                    
                    // استخراج متجه التهيئة
                    byte[] saltBytes = cipherTextBytes.Take(IvSize).ToArray();
                    
                    // استخراج النص المشفر
                    byte[] encryptedBytes = cipherTextBytes.Skip(IvSize).ToArray();
                    
                    // إنشاء مفتاح التشفير
                    byte[] keyBytes = GenerateKey(passPhrase, saltBytes);
                    
                    // إنشاء خوارزمية التشفير
                    using (var aes = Aes.Create())
                    {
                        aes.KeySize = KeySize;
                        aes.BlockSize = 128;
                        aes.Mode = CipherMode.CBC;
                        aes.Padding = PaddingMode.PKCS7;
                        aes.Key = keyBytes;
                        aes.IV = saltBytes;

                        // إنشاء محول فك التشفير
                        using (var decryptor = aes.CreateDecryptor())
                        {
                            // فك تشفير النص
                            using (var memoryStream = new MemoryStream(encryptedBytes))
                            {
                                using (var cryptoStream = new CryptoStream(memoryStream, decryptor, CryptoStreamMode.Read))
                                {
                                    using (var streamReader = new StreamReader(cryptoStream, Encoding.UTF8))
                                    {
                                        return streamReader.ReadToEnd();
                                    }
                                }
                            }
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EncryptionHelper.Decrypt");
                throw;
            }
        }

        /// <summary>
        /// حساب تجزئة SHA-256 لنص
        /// </summary>
        /// <param name="text">النص</param>
        /// <returns>التجزئة</returns>
        public static string ComputeSha256Hash(string text)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("EncryptionHelper.ComputeSha256Hash", () =>
                {
                    // التحقق من المدخلات
                    if (string.IsNullOrEmpty(text))
                        return string.Empty;

                    // تحويل النص إلى مصفوفة بايت
                    byte[] bytes = Encoding.UTF8.GetBytes(text);
                    
                    // حساب التجزئة
                    using (var sha256 = SHA256.Create())
                    {
                        byte[] hashBytes = sha256.ComputeHash(bytes);
                        
                        // تحويل التجزئة إلى سلسلة سداسية عشرية
                        return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EncryptionHelper.ComputeSha256Hash");
                throw;
            }
        }

        /// <summary>
        /// حساب تجزئة HMAC-SHA-256 لنص
        /// </summary>
        /// <param name="text">النص</param>
        /// <param name="key">المفتاح</param>
        /// <returns>التجزئة</returns>
        public static string ComputeHmacSha256(string text, string key)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("EncryptionHelper.ComputeHmacSha256", () =>
                {
                    // التحقق من المدخلات
                    if (string.IsNullOrEmpty(text))
                        return string.Empty;
                    
                    if (string.IsNullOrEmpty(key))
                        throw new ArgumentNullException(nameof(key), "المفتاح مطلوب لحساب HMAC.");

                    // تحويل النص والمفتاح إلى مصفوفة بايت
                    byte[] textBytes = Encoding.UTF8.GetBytes(text);
                    byte[] keyBytes = Encoding.UTF8.GetBytes(key);
                    
                    // حساب HMAC
                    using (var hmac = new HMACSHA256(keyBytes))
                    {
                        byte[] hashBytes = hmac.ComputeHash(textBytes);
                        
                        // تحويل التجزئة إلى سلسلة سداسية عشرية
                        return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EncryptionHelper.ComputeHmacSha256");
                throw;
            }
        }

        /// <summary>
        /// إنشاء مفتاح تشفير من كلمة مرور
        /// </summary>
        /// <param name="passPhrase">كلمة المرور</param>
        /// <param name="salt">الملح</param>
        /// <returns>مفتاح التشفير</returns>
        private static byte[] GenerateKey(string passPhrase, byte[] salt)
        {
            try
            {
                // استخدام خوارزمية اشتقاق المفتاح
                using (var rfc2898 = new Rfc2898DeriveBytes(passPhrase, salt, DerivationIterations, HashAlgorithmName.SHA256))
                {
                    return rfc2898.GetBytes(KeySize / 8);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EncryptionHelper.GenerateKey");
                throw;
            }
        }

        /// <summary>
        /// إنشاء مصفوفة بايت عشوائية
        /// </summary>
        /// <param name="size">الحجم</param>
        /// <returns>مصفوفة البايت</returns>
        private static byte[] GenerateRandomBytes(int size)
        {
            try
            {
                // إنشاء مصفوفة بايت
                byte[] randomBytes = new byte[size];
                
                // ملء المصفوفة بقيم عشوائية
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(randomBytes);
                }
                
                return randomBytes;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EncryptionHelper.GenerateRandomBytes");
                throw;
            }
        }

        /// <summary>
        /// إنشاء كلمة مرور عشوائية
        /// </summary>
        /// <param name="length">الطول</param>
        /// <returns>كلمة المرور</returns>
        public static string GenerateRandomPassword(int length = 12)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("EncryptionHelper.GenerateRandomPassword", () =>
                {
                    // التحقق من المدخلات
                    if (length < 8)
                        length = 8;

                    // تحديد مجموعات الأحرف
                    const string lowerChars = "abcdefghijklmnopqrstuvwxyz";
                    const string upperChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
                    const string numberChars = "0123456789";
                    const string specialChars = "!@#$%^&*()-_=+[]{}|;:,.<>?";
                    
                    // إنشاء مولد الأرقام العشوائية
                    using (var rng = RandomNumberGenerator.Create())
                    {
                        // إنشاء مصفوفة بايت
                        byte[] randomBytes = new byte[length];
                        
                        // ملء المصفوفة بقيم عشوائية
                        rng.GetBytes(randomBytes);
                        
                        // إنشاء كلمة المرور
                        var password = new StringBuilder();
                        
                        // التأكد من وجود حرف واحد على الأقل من كل مجموعة
                        password.Append(lowerChars[randomBytes[0] % lowerChars.Length]);
                        password.Append(upperChars[randomBytes[1] % upperChars.Length]);
                        password.Append(numberChars[randomBytes[2] % numberChars.Length]);
                        password.Append(specialChars[randomBytes[3] % specialChars.Length]);
                        
                        // إضافة باقي الأحرف
                        for (int i = 4; i < length; i++)
                        {
                            // تحديد مجموعة الأحرف
                            string charGroup = (randomBytes[i] % 4) switch
                            {
                                0 => lowerChars,
                                1 => upperChars,
                                2 => numberChars,
                                _ => specialChars
                            };
                            
                            // إضافة حرف عشوائي من المجموعة
                            password.Append(charGroup[randomBytes[i] % charGroup.Length]);
                        }
                        
                        // خلط الأحرف
                        return new string(password.ToString().ToCharArray().OrderBy(c => Guid.NewGuid()).ToArray());
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EncryptionHelper.GenerateRandomPassword");
                return "P@ssw0rd";
            }
        }
    }
}
