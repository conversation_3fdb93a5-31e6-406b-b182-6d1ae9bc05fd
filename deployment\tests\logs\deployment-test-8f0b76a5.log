﻿=== SmartAccount Pro Deployment Test Log ===
Test Session: 8f0b76a5
Start Time: 05/31/2025 06:04:43
==================================================
[2025-05-31 06:04:44] [WARNING] ملف التكوين غير موجود، سيتم استخدام التكوين الافتراضي
[2025-05-31 06:04:44] [INFO] تهيئة الآلة الافتراضية: Win10-Test-VM
[2025-05-31 06:04:44] [INFO] تهيئة الآلة الافتراضية: Win10-Test-VM
[2025-05-31 06:04:44] [ERROR] خطأ في تهيئة الآلة الافتراضية: The term 'Get-VM' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
[2025-05-31 06:04:44] [ERROR] فشل في تهيئة الآلة الافتراضية
[2025-05-31 06:04:44] [INFO] تهيئة الآلة الافتراضية: Win11-Test-VM
[2025-05-31 06:04:44] [INFO] تهيئة الآلة الافتراضية: Win11-Test-VM
[2025-05-31 06:04:44] [ERROR] خطأ في تهيئة الآلة الافتراضية: The term 'Get-VM' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
[2025-05-31 06:04:44] [ERROR] فشل في تهيئة الآلة الافتراضية
[2025-05-31 06:04:44] [INFO] تهيئة الآلة الافتراضية: WinServer2019-Test-VM
[2025-05-31 06:04:44] [INFO] تهيئة الآلة الافتراضية: WinServer2019-Test-VM
[2025-05-31 06:04:44] [ERROR] خطأ في تهيئة الآلة الافتراضية: The term 'Get-VM' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
[2025-05-31 06:04:44] [ERROR] فشل في تهيئة الآلة الافتراضية
[2025-05-31 06:04:44] [SUCCESS] تم إنشاء تقرير HTML: .\deployment\tests\reports\deployment-test-report-8f0b76a5.html
[2025-05-31 06:04:45] [SUCCESS] تم إنشاء تقرير Markdown: .\deployment\tests\reports\deployment-test-report-8f0b76a5.md
