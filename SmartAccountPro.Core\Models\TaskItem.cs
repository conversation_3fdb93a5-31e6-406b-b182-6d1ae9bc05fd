using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// نموذج المهمة
    /// </summary>
    public class TaskItem
    {
        /// <summary>
        /// معرف المهمة
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// عنوان المهمة
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Title { get; set; }

        /// <summary>
        /// وصف المهمة
        /// </summary>
        [MaxLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ الاستحقاق
        /// </summary>
        public DateTime DueDate { get; set; }

        /// <summary>
        /// تاريخ الإكمال
        /// </summary>
        public DateTime? CompletedDate { get; set; }

        /// <summary>
        /// حالة المهمة
        /// </summary>
        public TaskItemStatus Status { get; set; }

        /// <summary>
        /// أولوية المهمة
        /// </summary>
        public TaskPriority Priority { get; set; }

        /// <summary>
        /// معرف المستخدم المسؤول
        /// </summary>
        public int? AssignedToUserId { get; set; }

        /// <summary>
        /// المستخدم المسؤول
        /// </summary>
        [ForeignKey("AssignedToUserId")]
        public User AssignedToUser { get; set; }

        /// <summary>
        /// معرف المستخدم المنشئ
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// المستخدم المنشئ
        /// </summary>
        [ForeignKey("CreatedByUserId")]
        public User CreatedByUser { get; set; }

        /// <summary>
        /// معرف الحساب المرتبط (اختياري)
        /// </summary>
        public int? RelatedAccountId { get; set; }

        /// <summary>
        /// الحساب المرتبط
        /// </summary>
        [ForeignKey("RelatedAccountId")]
        public Account RelatedAccount { get; set; }

        /// <summary>
        /// معرف القيد المرتبط (اختياري)
        /// </summary>
        public int? RelatedJournalEntryId { get; set; }

        /// <summary>
        /// القيد المرتبط
        /// </summary>
        [ForeignKey("RelatedJournalEntryId")]
        public JournalEntry RelatedJournalEntry { get; set; }

        /// <summary>
        /// معرف الفاتورة المرتبطة (اختياري)
        /// </summary>
        public int? RelatedInvoiceId { get; set; }

        /// <summary>
        /// نسبة الإكمال
        /// </summary>
        [Range(0, 100)]
        public int CompletionPercentage { get; set; }

        /// <summary>
        /// هل المهمة متكررة
        /// </summary>
        public bool IsRecurring { get; set; }

        /// <summary>
        /// نمط التكرار
        /// </summary>
        public RecurrencePattern? RecurrencePattern { get; set; }

        /// <summary>
        /// تاريخ التكرار التالي
        /// </summary>
        public DateTime? NextRecurrenceDate { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// هل تم حذف المهمة
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// هل المهمة مكتملة
        /// </summary>
        [NotMapped]
        public bool IsCompleted => Status == TaskItemStatus.Completed;
    }

    /// <summary>
    /// حالة المهمة
    /// </summary>
    public enum TaskItemStatus
    {
        /// <summary>
        /// جديدة
        /// </summary>
        New = 0,

        /// <summary>
        /// قيد التنفيذ
        /// </summary>
        InProgress = 1,

        /// <summary>
        /// معلقة
        /// </summary>
        OnHold = 2,

        /// <summary>
        /// مكتملة
        /// </summary>
        Completed = 3,

        /// <summary>
        /// ملغاة
        /// </summary>
        Cancelled = 4
    }

    /// <summary>
    /// أولوية المهمة
    /// </summary>
    public enum TaskPriority
    {
        /// <summary>
        /// منخفضة
        /// </summary>
        Low = 0,

        /// <summary>
        /// متوسطة
        /// </summary>
        Medium = 1,

        /// <summary>
        /// عالية
        /// </summary>
        High = 2,

        /// <summary>
        /// حرجة
        /// </summary>
        Critical = 3
    }

    /// <summary>
    /// نمط التكرار
    /// </summary>
    public enum RecurrencePattern
    {
        /// <summary>
        /// يومي
        /// </summary>
        Daily = 0,

        /// <summary>
        /// أسبوعي
        /// </summary>
        Weekly = 1,

        /// <summary>
        /// شهري
        /// </summary>
        Monthly = 2,

        /// <summary>
        /// ربع سنوي
        /// </summary>
        Quarterly = 3,

        /// <summary>
        /// سنوي
        /// </summary>
        Yearly = 4
    }
}
