<UserControl x:Class="SmartAccountPro.Views.FinancialReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SmartAccountPro.Views"
             xmlns:viewmodels="clr-namespace:SmartAccountPro.ViewModels"
             xmlns:converters="clr-namespace:SmartAccountPro.Converters"
             mc:Ignorable="d"
             d:DesignHeight="768" d:DesignWidth="1024"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:DateTimeConverter x:Key="DateTimeConverter" />
        <converters:BooleanToStringConverter x:Key="BooleanToStringConverter" TrueValue="نعم" FalseValue="لا" />
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- مؤشر التحميل -->
        <Border Grid.Row="0" Grid.RowSpan="2"
                Background="#********"
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                Panel.ZIndex="1000">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <Border Background="White"
                        CornerRadius="10"
                        Padding="20">
                    <StackPanel>
                        <TextBlock Text="&#xE895;"
                                   FontFamily="Segoe MDL2 Assets"
                                   FontSize="32"
                                   HorizontalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryColor}">
                            <TextBlock.RenderTransform>
                                <RotateTransform x:Name="SpinnerRotation" Angle="0" CenterX="16" CenterY="16"/>
                            </TextBlock.RenderTransform>
                            <TextBlock.Triggers>
                                <EventTrigger RoutedEvent="Loaded">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="SpinnerRotation"
                                                             Storyboard.TargetProperty="Angle"
                                                             From="0" To="360"
                                                             Duration="0:0:1"
                                                             RepeatBehavior="Forever"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </TextBlock.Triggers>
                        </TextBlock>
                        <TextBlock Text="جاري التحميل..."
                                   Margin="0,10,0,0"
                                   HorizontalAlignment="Center"
                                   FontSize="14"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>

        <!-- العنوان وشريط الأدوات -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- العنوان -->
            <StackPanel Grid.Column="0" Orientation="Vertical">
                <TextBlock Text="التقارير المالية"
                           FontSize="24"
                           FontWeight="Bold"
                           Foreground="{DynamicResource PrimaryColor}"/>
                <TextBlock Text="إدارة وعرض التقارير المالية للشركة"
                           FontSize="14"
                           Foreground="#666666"
                           Margin="0,5,0,0"/>
            </StackPanel>

            <!-- البحث -->
            <Grid Grid.Column="1" Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBox Grid.Column="0"
                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         Padding="10"
                         BorderThickness="1"
                         BorderBrush="#E0E0E0"
                         VerticalContentAlignment="Center"/>
                <Button Grid.Column="1"
                        Command="{Binding SearchCommand}"
                        Style="{StaticResource ModernButton}"
                        Padding="12,8">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE721;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="بحث"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Column="2" Orientation="Horizontal">
                <Button Style="{StaticResource ModernButton}"
                        Margin="0,0,10,0"
                        Padding="12,8"
                        Command="{Binding CreateReportCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE710;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="إنشاء تقرير"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button Style="{StaticResource GreenButton}"
                        Margin="0,0,10,0"
                        Padding="12,8"
                        Command="{Binding EditReportCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE70F;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="تعديل تقرير"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button Style="{StaticResource RedButton}"
                        Margin="0,0,10,0"
                        Padding="12,8"
                        Command="{Binding DeleteReportCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE74D;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="حذف تقرير"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button Style="{StaticResource OrangeButton}"
                        Margin="0,0,10,0"
                        Padding="12,8"
                        Command="{Binding RefreshCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE72C;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="تحديث"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button Style="{StaticResource PurpleButton}"
                        Padding="12,8"
                        Command="{Binding PrintReportCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE749;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="طباعة"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>

        <!-- محتوى الصفحة -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="350"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة التقارير -->
            <Border Grid.Column="0"
                    BorderThickness="1"
                    BorderBrush="#E0E0E0"
                    Background="White"
                    CornerRadius="5"
                    Margin="0,0,10,0">
                <DataGrid ItemsSource="{Binding Reports}"
                          SelectedItem="{Binding SelectedReport}"
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          BorderThickness="0"
                          Background="Transparent"
                          RowBackground="White"
                          AlternatingRowBackground="#F8F9FA"
                          GridLinesVisibility="Horizontal"
                          HorizontalGridLinesBrush="#E0E0E0"
                          SelectionMode="Single"
                          SelectionUnit="FullRow"
                          CanUserSortColumns="True"
                          CanUserResizeColumns="True"
                          CanUserReorderColumns="True"
                          HeadersVisibility="Column"
                          RowHeaderWidth="0">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="العنوان" Width="*" Binding="{Binding Title}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="النوع" Width="100" Binding="{Binding Type}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="الفترة" Width="100" Binding="{Binding Period}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="تاريخ البداية" Width="120" Binding="{Binding StartDate, Converter={StaticResource DateTimeConverter}}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="تاريخ النهاية" Width="120" Binding="{Binding EndDate, Converter={StaticResource DateTimeConverter}}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="الحالة" Width="100" Binding="{Binding Status}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>

            <!-- تفاصيل التقرير المحدد -->
            <Border Grid.Column="1"
                    BorderThickness="1"
                    BorderBrush="#E0E0E0"
                    Background="White"
                    CornerRadius="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان التفاصيل -->
                    <Border Grid.Row="0"
                            Background="{DynamicResource PrimaryColor}"
                            Padding="15">
                        <TextBlock Text="تفاصيل التقرير"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="White"/>
                    </Border>

                    <!-- محتوى التفاصيل -->
                    <ScrollViewer Grid.Row="1"
                                  VerticalScrollBarVisibility="Auto"
                                  Padding="15">
                        <Grid>
                            <StackPanel Visibility="{Binding IsReportSelected, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <TextBlock Text="العنوان" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding SelectedReport.Title}" Margin="0,0,0,15"/>

                                <TextBlock Text="النوع" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding SelectedReport.Type}" Margin="0,0,0,15"/>

                                <TextBlock Text="الفترة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding SelectedReport.Period}" Margin="0,0,0,15"/>

                                <TextBlock Text="تاريخ البداية" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding SelectedReport.StartDate, Converter={StaticResource DateTimeConverter}}" Margin="0,0,0,15"/>

                                <TextBlock Text="تاريخ النهاية" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding SelectedReport.EndDate, Converter={StaticResource DateTimeConverter}}" Margin="0,0,0,15"/>

                                <TextBlock Text="تاريخ الإنشاء" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding SelectedReport.CreatedAt, Converter={StaticResource DateTimeConverter}}" Margin="0,0,0,15"/>

                                <TextBlock Text="الحالة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding SelectedReport.Status}" Margin="0,0,0,15"/>

                                <TextBlock Text="منشور" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding SelectedReport.IsPublished, Converter={StaticResource BooleanToStringConverter}}" Margin="0,0,0,15"/>

                                <TextBlock Text="تاريخ النشر" FontWeight="Bold" Margin="0,0,0,5" Visibility="{Binding SelectedReport.IsPublished, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                <TextBlock Text="{Binding SelectedReport.PublishedAt, Converter={StaticResource DateTimeConverter}}" Margin="0,0,0,15" Visibility="{Binding SelectedReport.IsPublished, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                                <TextBlock Text="ملاحظات" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding SelectedReport.Notes}" TextWrapping="Wrap" Margin="0,0,0,15"/>
                            </StackPanel>

                            <!-- رسالة عند عدم تحديد تقرير -->
                            <TextBlock Text="يرجى تحديد تقرير لعرض التفاصيل"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="#666666"
                                       TextWrapping="Wrap"
                                       Visibility="{Binding IsReportSelected, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Inverse}"/>
                        </Grid>
                    </ScrollViewer>

                    <!-- أزرار الإجراءات -->
                    <StackPanel Grid.Row="2"
                                Orientation="Horizontal"
                                HorizontalAlignment="Center"
                                Margin="15"
                                Visibility="{Binding IsReportSelected, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Button Style="{StaticResource GreenButton}"
                                Margin="0,0,10,0"
                                Padding="12,8"
                                Command="{Binding ExportReportCommand}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="&#xE74E;"
                                           FontFamily="Segoe MDL2 Assets"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                                <TextBlock Text="تصدير"
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                        <Button Style="{StaticResource BlueButton}"
                                Padding="12,8"
                                Command="{Binding PublishReportCommand}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="&#xE724;"
                                           FontFamily="Segoe MDL2 Assets"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                                <TextBlock Text="نشر"
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
