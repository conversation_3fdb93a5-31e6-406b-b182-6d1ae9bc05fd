using System;
using System.Globalization;
using System.Windows.Data;

namespace SmartAccountPro.Converters
{
    /// <summary>
    /// محول التاريخ والوقت
    /// </summary>
    public class DateTimeConverter : IValueConverter
    {
        /// <summary>
        /// تحويل التاريخ إلى نص
        /// </summary>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DateTime dateTime)
            {
                // التحقق من القيمة الافتراضية للتاريخ
                if (dateTime == DateTime.MinValue)
                {
                    return "غير محدد";
                }

                // تنسيق التاريخ حسب المعامل
                string format = parameter as string ?? "yyyy/MM/dd";
                return dateTime.ToString(format);
            }
            else if (value is DateTime?)
            {
                var nullableDateTime = (DateTime?)value;
                if (!nullableDateTime.HasValue || nullableDateTime.Value == DateTime.MinValue)
                {
                    return "غير محدد";
                }

                string format = parameter as string ?? "yyyy/MM/dd";
                return nullableDateTime.Value.ToString(format);
            }

            return "غير محدد";
        }

        /// <summary>
        /// تحويل النص إلى تاريخ
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string dateString)
            {
                if (DateTime.TryParse(dateString, out DateTime result))
                {
                    return result;
                }
            }

            return targetType == typeof(DateTime?) ? (DateTime?)null : DateTime.MinValue;
        }
    }
}
