using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using System;
using System.Windows;
using System.Windows.Input;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// نموذج عرض الشريط الجانبي
    /// </summary>
    public class SidebarViewModel : ViewModelBase
    {
        private User _currentUser = null!;

        /// <summary>
        /// المستخدم الحالي
        /// </summary>
        public User CurrentUser
        {
            get => _currentUser;
            set => SetProperty(ref _currentUser, value);
        }

        /// <summary>
        /// أمر التنقل
        /// </summary>
        public ICommand NavigateCommand { get; }

        /// <summary>
        /// أمر تسجيل الخروج
        /// </summary>
        public ICommand LogoutCommand { get; }

        /// <summary>
        /// حدث التنقل
        /// </summary>
        public event EventHandler<string>? Navigate;

        /// <summary>
        /// حدث تسجيل الخروج
        /// </summary>
        public event EventHandler? Logout;

        public SidebarViewModel()
        {
            // تهيئة الأوامر
            NavigateCommand = new RelayCommand<string>(OnNavigate);
            LogoutCommand = new RelayCommand(OnLogout);

            // تهيئة المستخدم الحالي (مثال)
            CurrentUser = new User
            {
                Id = 1,
                Username = "admin",
                FullName = "مدير النظام",
                Email = "<EMAIL>"
            };
        }

        /// <summary>
        /// معالج التنقل
        /// </summary>
        private void OnNavigate(string viewName)
        {
            try
            {
                Navigate?.Invoke(this, viewName);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SidebarViewModel.OnNavigate");
            }
        }

        /// <summary>
        /// معالج تسجيل الخروج
        /// </summary>
        private void OnLogout(object parameter)
        {
            try
            {
                MessageBoxResult result = MessageBox.Show(
                    "هل أنت متأكد من تسجيل الخروج؟",
                    "تأكيد تسجيل الخروج",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    Logout?.Invoke(this, EventArgs.Empty);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SidebarViewModel.OnLogout");
            }
        }
    }
}
