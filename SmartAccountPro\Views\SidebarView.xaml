<UserControl x:Class="SmartAccountPro.Views.SidebarView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SmartAccountPro.Views"
             xmlns:vm="clr-namespace:SmartAccountPro.ViewModels"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="250"
             FlowDirection="RightToLeft">

    <UserControl.DataContext>
        <vm:SidebarViewModel />
    </UserControl.DataContext>

    <Grid Background="{DynamicResource SidebarBackgroundColor}">
        <Grid.Effect>
            <DropShadowEffect ShadowDepth="3" BlurRadius="10" Opacity="0.2" Direction="0"/>
        </Grid.Effect>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شعار التطبيق -->
        <Border Grid.Row="0"
                Background="{DynamicResource PrimaryColor}"
                CornerRadius="0,0,15,15"
                Margin="0,0,0,10">
            <StackPanel Margin="20,25,20,25"
                        HorizontalAlignment="Center">
                <Image Source="/Resources/Images/logo.png"
                       Width="80"
                       Height="80"
                       Margin="0,0,0,15">
                    <Image.Effect>
                        <DropShadowEffect ShadowDepth="2"
                                          BlurRadius="5"
                                          Opacity="0.3"
                                          Color="#000000"/>
                    </Image.Effect>
                </Image>
                <TextBlock Text="SmartAccount Pro"
                           FontSize="22"
                           FontWeight="Bold"
                           Foreground="White"
                           HorizontalAlignment="Center">
                    <TextBlock.Effect>
                        <DropShadowEffect ShadowDepth="1"
                                          BlurRadius="3"
                                          Opacity="0.3"
                                          Color="#000000"/>
                    </TextBlock.Effect>
                </TextBlock>
                <TextBlock Text="نظام المحاسبة الذكي"
                           FontSize="14"
                           Foreground="#E1F5FE"
                           HorizontalAlignment="Center"
                           Margin="0,5,0,0">
                    <TextBlock.Effect>
                        <DropShadowEffect ShadowDepth="1"
                                          BlurRadius="2"
                                          Opacity="0.2"
                                          Color="#000000"/>
                    </TextBlock.Effect>
                </TextBlock>
            </StackPanel>
        </Border>

        <!-- قائمة التنقل -->
        <ScrollViewer Grid.Row="1"
                      VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="15,10">
                <!-- لوحة التحكم -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="Dashboard">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE80F;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="لوحة التحكم"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- الحسابات -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="Accounts">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE825;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="الحسابات"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- القيود المحاسبية -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="JournalEntries">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE8A5;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="القيود المحاسبية"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- الفواتير -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="Invoices">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE71D;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="الفواتير"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- العملاء -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="Customers">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE77B;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="العملاء"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- الموردين -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="Suppliers">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE748;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="الموردين"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- المنتجات -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="Products">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE7BF;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="المنتجات"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- التقارير -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="Reports">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE9D9;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="التقارير"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- التقارير المحسنة -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="EnhancedReports">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE9D2;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="التقارير المحسنة"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- التقارير المالية -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="FinancialReports">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE8A1;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="التقارير المالية"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- المهام -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="Tasks">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE7C1;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="المهام"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- المستخدمين -->
                <Button Style="{StaticResource SidebarButton}"
                        Command="{Binding NavigateCommand}"
                        CommandParameter="Users">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE716;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="المستخدمين"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </ScrollViewer>

        <!-- الإعدادات وتسجيل الخروج -->
        <StackPanel Grid.Row="2"
                    Margin="10,10,10,20">
            <!-- الإعدادات -->
            <Button Style="{StaticResource SidebarButton}"
                    Command="{Binding NavigateCommand}"
                    CommandParameter="Settings">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="&#xE713;"
                               FontFamily="Segoe MDL2 Assets"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>
                    <TextBlock Text="الإعدادات"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <!-- تسجيل الخروج -->
            <Button Style="{StaticResource SidebarButton}"
                    Command="{Binding LogoutCommand}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="&#xE7E8;"
                               FontFamily="Segoe MDL2 Assets"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>
                    <TextBlock Text="تسجيل الخروج"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <!-- معلومات المستخدم -->
            <Border BorderBrush="{DynamicResource BorderColor}"
                    BorderThickness="0,1,0,0"
                    Margin="0,10,0,0"
                    Padding="0,10,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Ellipse Grid.Column="0"
                             Width="40"
                             Height="40"
                             Fill="{DynamicResource PrimaryColor}">
                        <Ellipse.Effect>
                            <DropShadowEffect ShadowDepth="1"
                                              BlurRadius="3"
                                              Opacity="0.3"/>
                        </Ellipse.Effect>
                    </Ellipse>

                    <TextBlock Grid.Column="0"
                               Text="&#xE77B;"
                               FontFamily="Segoe MDL2 Assets"
                               FontSize="20"
                               Foreground="White"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"/>

                    <StackPanel Grid.Column="1"
                                Margin="10,0,0,0">
                        <TextBlock Text="{Binding CurrentUser.FullName}"
                                   FontWeight="SemiBold"
                                   Foreground="{DynamicResource TextColor}"/>
                        <TextBlock Text="{Binding CurrentUser.Role}"
                                   FontSize="12"
                                   Foreground="{DynamicResource SecondaryTextColor}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </StackPanel>
    </Grid>
</UserControl>
