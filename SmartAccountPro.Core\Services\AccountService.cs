using SmartAccountPro.Core.Models;
using SmartAccountPro.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SmartAccountPro.Core.Services
{
    /// <summary>
    /// تنفيذ خدمة الحسابات
    /// </summary>
    public class AccountService : IAccountService
    {
        private readonly IAccountRepository _accountRepository;

        public AccountService(IAccountRepository accountRepository)
        {
            _accountRepository = accountRepository;
        }

        /// <summary>
        /// الحصول على جميع الحسابات
        /// </summary>
        public async Task<IEnumerable<Account>> GetAllAccountsAsync()
        {
            return await _accountRepository.GetAllAsync();
        }

        /// <summary>
        /// الحصول على الحسابات الرئيسية
        /// </summary>
        public async Task<IEnumerable<Account>> GetRootAccountsAsync()
        {
            return await _accountRepository.GetRootAccountsAsync();
        }

        /// <summary>
        /// الحصول على الحسابات الفرعية لحساب معين
        /// </summary>
        public async Task<IEnumerable<Account>> GetChildAccountsAsync(int parentAccountId)
        {
            return await _accountRepository.GetChildAccountsAsync(parentAccountId);
        }

        /// <summary>
        /// الحصول على حساب بواسطة المعرف
        /// </summary>
        public async Task<Account> GetAccountByIdAsync(int id)
        {
            return await _accountRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// الحصول على حساب بواسطة الكود
        /// </summary>
        public async Task<Account> GetAccountByCodeAsync(string code)
        {
            return await _accountRepository.GetByCodeAsync(code);
        }

        /// <summary>
        /// إنشاء حساب جديد
        /// </summary>
        public async Task<Account> CreateAccountAsync(Account account)
        {
            // التحقق من عدم وجود حساب بنفس الكود
            if (await _accountRepository.ExistsAsync(a => a.Code == account.Code))
                throw new Exception("يوجد حساب آخر بنفس الكود");

            account.CreatedAt = DateTime.Now;
            return await _accountRepository.AddAsync(account);
        }

        /// <summary>
        /// تحديث حساب
        /// </summary>
        public async Task<bool> UpdateAccountAsync(Account account)
        {
            // التحقق من عدم وجود حساب آخر بنفس الكود
            if (await _accountRepository.ExistsAsync(a => a.Code == account.Code && a.Id != account.Id))
                throw new Exception("يوجد حساب آخر بنفس الكود");

            account.UpdatedAt = DateTime.Now;
            return await _accountRepository.UpdateAsync(account);
        }

        /// <summary>
        /// حذف حساب
        /// </summary>
        public async Task<bool> DeleteAccountAsync(int id)
        {
            // التحقق من عدم وجود حسابات فرعية
            if (await _accountRepository.ExistsAsync(a => a.ParentAccountId == id))
                throw new Exception("لا يمكن حذف الحساب لأنه يحتوي على حسابات فرعية");

            // التحقق من عدم وجود حركات على الحساب
            // هنا يجب التحقق من عدم وجود قيود محاسبية تستخدم هذا الحساب

            return await _accountRepository.RemoveByIdAsync(id);
        }

        /// <summary>
        /// الحصول على شجرة الحسابات
        /// </summary>
        public async Task<IEnumerable<Account>> GetAccountTreeAsync()
        {
            return await _accountRepository.GetAccountTreeAsync();
        }

        /// <summary>
        /// الحصول على ميزان المراجعة
        /// </summary>
        public async Task<IEnumerable<Account>> GetTrialBalanceAsync(DateTime fromDate, DateTime toDate)
        {
            return await _accountRepository.GetTrialBalanceAsync(fromDate, toDate);
        }

        /// <summary>
        /// تحديث رصيد الحساب
        /// </summary>
        public async Task<bool> UpdateAccountBalanceAsync(int accountId, decimal amount)
        {
            return await _accountRepository.UpdateBalanceAsync(accountId, amount);
        }
    }
}
