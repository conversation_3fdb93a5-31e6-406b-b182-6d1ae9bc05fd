using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Media;
using Newtonsoft.Json;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// مدير السمات للتطبيق
    /// </summary>
    public static class ThemeManager
    {
        /// <summary>
        /// أنواع السمات
        /// </summary>
        public enum ThemeType
        {
            Light,
            Dark,
            Custom
        }

        private static ThemeType _currentTheme = ThemeType.Light;
        private static string _currentCustomThemeName = string.Empty;
        private static readonly Dictionary<string, ResourceDictionary> _customThemes = new Dictionary<string, ResourceDictionary>();
        private static readonly string _themesPath = Path.Combine(App.AppDataPath, "Themes");

        /// <summary>
        /// السمة الحالية
        /// </summary>
        public static ThemeType CurrentTheme
        {
            get => _currentTheme;
            private set => _currentTheme = value;
        }

        /// <summary>
        /// اسم السمة المخصصة الحالية
        /// </summary>
        public static string CurrentCustomThemeName
        {
            get => _currentCustomThemeName;
            private set => _currentCustomThemeName = value;
        }

        /// <summary>
        /// حدث تغيير السمة
        /// </summary>
        public static event EventHandler<ThemeType>? ThemeChanged;

        /// <summary>
        /// تغيير السمة
        /// </summary>
        public static void ChangeTheme(ThemeType theme, string customThemeName = "")
        {
            try
            {
                // تحديث السمة الحالية
                CurrentTheme = theme;

                // الحصول على قاموس الموارد
                var appResources = Application.Current.Resources;
                var mergedDictionaries = appResources.MergedDictionaries;

                // الحصول على قاموس السمة المطلوبة
                ResourceDictionary? themeDict = null;

                if (theme == ThemeType.Light)
                {
                    themeDict = appResources["LightTheme"] as ResourceDictionary;
                }
                else if (theme == ThemeType.Dark)
                {
                    themeDict = appResources["DarkTheme"] as ResourceDictionary;
                }
                else if (theme == ThemeType.Custom && !string.IsNullOrEmpty(customThemeName))
                {
                    // تحديث اسم السمة المخصصة الحالية
                    CurrentCustomThemeName = customThemeName;

                    // التحقق من وجود السمة المخصصة
                    if (_customThemes.ContainsKey(customThemeName))
                    {
                        themeDict = _customThemes[customThemeName];
                    }
                    else
                    {
                        // محاولة تحميل السمة المخصصة
                        themeDict = LoadCustomTheme(customThemeName);

                        if (themeDict != null)
                        {
                            _customThemes[customThemeName] = themeDict;
                        }
                    }
                }

                if (themeDict == null)
                {
                    throw new InvalidOperationException($"لم يتم العثور على قاموس السمة: {theme} {customThemeName}");
                }

                // تحديث الألوان في قاموس الموارد الرئيسي
                foreach (var key in themeDict.Keys)
                {
                    if (appResources.Contains(key))
                    {
                        appResources[key] = themeDict[key];
                    }
                }

                // حفظ إعداد السمة
                Properties.Settings.Default.Theme = theme.ToString();
                if (theme == ThemeType.Custom)
                {
                    Properties.Settings.Default.CustomThemeName = customThemeName;
                }
                Properties.Settings.Default.Save();

                // إضافة إشعار
                if (theme == ThemeType.Custom)
                {
                    NotificationManager.Instance.AddNotification(
                        "تم تغيير السمة",
                        $"تم تطبيق السمة المخصصة '{customThemeName}' بنجاح.",
                        NotificationType.Info);
                }
                else
                {
                    NotificationManager.Instance.AddNotification(
                        "تم تغيير السمة",
                        $"تم تطبيق السمة '{theme}' بنجاح.",
                        NotificationType.Info);
                }

                // إطلاق حدث تغيير السمة
                ThemeChanged?.Invoke(null, theme);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ThemeManager.ChangeTheme");
            }
        }

        /// <summary>
        /// تبديل السمة
        /// </summary>
        public static void ToggleTheme()
        {
            if (CurrentTheme == ThemeType.Light)
            {
                ChangeTheme(ThemeType.Dark);
            }
            else
            {
                ChangeTheme(ThemeType.Light);
            }
        }

        /// <summary>
        /// تحميل سمة مخصصة
        /// </summary>
        /// <param name="themeName">اسم السمة</param>
        /// <returns>قاموس الموارد للسمة</returns>
        private static ResourceDictionary? LoadCustomTheme(string themeName)
        {
            try
            {
                // التأكد من وجود مجلد السمات
                if (!Directory.Exists(_themesPath))
                {
                    Directory.CreateDirectory(_themesPath);
                }

                // تحديد مسار ملف السمة
                string themePath = Path.Combine(_themesPath, $"{themeName}.json");

                // التحقق من وجود ملف السمة
                if (!File.Exists(themePath))
                {
                    return null;
                }

                // قراءة ملف السمة
                string themeJson = File.ReadAllText(themePath);
                var themeSettings = JsonConvert.DeserializeObject<ThemeSettings>(themeJson);

                if (themeSettings == null)
                {
                    return null;
                }

                // إنشاء قاموس الموارد للسمة
                var resourceDictionary = new ResourceDictionary();

                // إضافة الألوان الأساسية
                resourceDictionary.Add("PrimaryColor", (Color)ColorConverter.ConvertFromString(themeSettings.PrimaryColor));
                resourceDictionary.Add("SecondaryColor", (Color)ColorConverter.ConvertFromString(themeSettings.SecondaryColor));
                resourceDictionary.Add("BackgroundColor", (Color)ColorConverter.ConvertFromString(themeSettings.BackgroundColor));
                resourceDictionary.Add("ForegroundColor", (Color)ColorConverter.ConvertFromString(themeSettings.ForegroundColor));
                resourceDictionary.Add("AccentColor", (Color)ColorConverter.ConvertFromString(themeSettings.AccentColor));

                // إضافة الفرش
                resourceDictionary.Add("PrimaryBrush", new SolidColorBrush((Color)ColorConverter.ConvertFromString(themeSettings.PrimaryColor)));
                resourceDictionary.Add("SecondaryBrush", new SolidColorBrush((Color)ColorConverter.ConvertFromString(themeSettings.SecondaryColor)));
                resourceDictionary.Add("BackgroundBrush", new SolidColorBrush((Color)ColorConverter.ConvertFromString(themeSettings.BackgroundColor)));
                resourceDictionary.Add("ForegroundBrush", new SolidColorBrush((Color)ColorConverter.ConvertFromString(themeSettings.ForegroundColor)));
                resourceDictionary.Add("AccentBrush", new SolidColorBrush((Color)ColorConverter.ConvertFromString(themeSettings.AccentColor)));

                return resourceDictionary;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ThemeManager.LoadCustomTheme");
                return null;
            }
        }

        /// <summary>
        /// إنشاء سمة مخصصة
        /// </summary>
        /// <param name="themeSettings">إعدادات السمة</param>
        /// <returns>نجاح العملية</returns>
        public static bool CreateCustomTheme(ThemeSettings themeSettings)
        {
            try
            {
                // التأكد من وجود مجلد السمات
                if (!Directory.Exists(_themesPath))
                {
                    Directory.CreateDirectory(_themesPath);
                }

                // تحديد مسار ملف السمة
                string themePath = Path.Combine(_themesPath, $"{themeSettings.Name}.json");

                // التحقق من وجود ملف السمة
                if (File.Exists(themePath))
                {
                    MessageBox.Show($"السمة '{themeSettings.Name}' موجودة بالفعل.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // حفظ ملف السمة
                string themeJson = JsonConvert.SerializeObject(themeSettings, Formatting.Indented);
                File.WriteAllText(themePath, themeJson);

                // إنشاء قاموس الموارد للسمة
                var resourceDictionary = new ResourceDictionary();

                // إضافة الألوان الأساسية
                resourceDictionary.Add("PrimaryColor", (Color)ColorConverter.ConvertFromString(themeSettings.PrimaryColor));
                resourceDictionary.Add("SecondaryColor", (Color)ColorConverter.ConvertFromString(themeSettings.SecondaryColor));
                resourceDictionary.Add("BackgroundColor", (Color)ColorConverter.ConvertFromString(themeSettings.BackgroundColor));
                resourceDictionary.Add("ForegroundColor", (Color)ColorConverter.ConvertFromString(themeSettings.ForegroundColor));
                resourceDictionary.Add("AccentColor", (Color)ColorConverter.ConvertFromString(themeSettings.AccentColor));

                // إضافة الفرش
                resourceDictionary.Add("PrimaryBrush", new SolidColorBrush((Color)ColorConverter.ConvertFromString(themeSettings.PrimaryColor)));
                resourceDictionary.Add("SecondaryBrush", new SolidColorBrush((Color)ColorConverter.ConvertFromString(themeSettings.SecondaryColor)));
                resourceDictionary.Add("BackgroundBrush", new SolidColorBrush((Color)ColorConverter.ConvertFromString(themeSettings.BackgroundColor)));
                resourceDictionary.Add("ForegroundBrush", new SolidColorBrush((Color)ColorConverter.ConvertFromString(themeSettings.ForegroundColor)));
                resourceDictionary.Add("AccentBrush", new SolidColorBrush((Color)ColorConverter.ConvertFromString(themeSettings.AccentColor)));

                // إضافة السمة إلى القاموس
                _customThemes[themeSettings.Name] = resourceDictionary;

                // إضافة إشعار
                NotificationManager.Instance.AddNotification(
                    "تم إنشاء سمة جديدة",
                    $"تم إنشاء السمة المخصصة '{themeSettings.Name}' بنجاح.",
                    NotificationType.Success);

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ThemeManager.CreateCustomTheme");
                return false;
            }
        }

        /// <summary>
        /// الحصول على قائمة السمات المخصصة
        /// </summary>
        /// <returns>قائمة السمات المخصصة</returns>
        public static List<string> GetCustomThemes()
        {
            try
            {
                // التأكد من وجود مجلد السمات
                if (!Directory.Exists(_themesPath))
                {
                    Directory.CreateDirectory(_themesPath);
                    return new List<string>();
                }

                // البحث عن ملفات السمات
                string[] themeFiles = Directory.GetFiles(_themesPath, "*.json");

                // استخراج أسماء السمات
                List<string> themeNames = new List<string>();
                foreach (string themeFile in themeFiles)
                {
                    string themeName = Path.GetFileNameWithoutExtension(themeFile);
                    themeNames.Add(themeName);
                }

                return themeNames;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ThemeManager.GetCustomThemes");
                return new List<string>();
            }
        }

        /// <summary>
        /// تهيئة السمة
        /// </summary>
        public static void Initialize()
        {
            try
            {
                // قراءة إعداد السمة المحفوظ
                string savedTheme = Properties.Settings.Default.Theme;

                if (!string.IsNullOrEmpty(savedTheme) && Enum.TryParse<ThemeType>(savedTheme, out var theme))
                {
                    if (theme == ThemeType.Custom)
                    {
                        // قراءة اسم السمة المخصصة
                        string customThemeName = Properties.Settings.Default.CustomThemeName;
                        if (!string.IsNullOrEmpty(customThemeName))
                        {
                            ChangeTheme(theme, customThemeName);
                        }
                        else
                        {
                            // استخدام السمة الافتراضية
                            ChangeTheme(ThemeType.Light);
                        }
                    }
                    else
                    {
                        ChangeTheme(theme);
                    }
                }
                else
                {
                    // استخدام السمة الافتراضية
                    ChangeTheme(ThemeType.Light);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ThemeManager.Initialize");

                // استخدام السمة الافتراضية في حالة حدوث خطأ
                ChangeTheme(ThemeType.Light);
            }
        }
    }

    /// <summary>
    /// إعدادات السمة
    /// </summary>
    public class ThemeSettings
    {
        /// <summary>
        /// اسم السمة
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// اللون الأساسي
        /// </summary>
        public string PrimaryColor { get; set; } = "#1E88E5";

        /// <summary>
        /// اللون الثانوي
        /// </summary>
        public string SecondaryColor { get; set; } = "#26A69A";

        /// <summary>
        /// لون الخلفية
        /// </summary>
        public string BackgroundColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// لون النص
        /// </summary>
        public string ForegroundColor { get; set; } = "#212121";

        /// <summary>
        /// لون التمييز
        /// </summary>
        public string AccentColor { get; set; } = "#FF4081";
    }
}
