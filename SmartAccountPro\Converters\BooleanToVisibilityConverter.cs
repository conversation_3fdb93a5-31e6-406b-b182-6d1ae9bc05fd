using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace SmartAccountPro.Converters
{
    /// <summary>
    /// محول قيمة منطقية إلى قيمة ظهور
    /// </summary>
    public class BooleanToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// تحويل قيمة منطقية إلى قيمة ظهور
        /// </summary>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            
            return Visibility.Collapsed;
        }

        /// <summary>
        /// تحويل قيمة ظهور إلى قيمة منطقية
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Visible;
            }
            
            return false;
        }
    }

    /// <summary>
    /// محول قيمة منطقية معكوسة إلى قيمة ظهور
    /// </summary>
    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// تحويل قيمة منطقية معكوسة إلى قيمة ظهور
        /// </summary>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            
            return Visibility.Visible;
        }

        /// <summary>
        /// تحويل قيمة ظهور إلى قيمة منطقية معكوسة
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility != Visibility.Visible;
            }
            
            return true;
        }
    }
}
