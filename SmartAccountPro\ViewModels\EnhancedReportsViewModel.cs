using Microsoft.Win32;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Data.Services;
using SmartAccountPro.Helpers;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// نموذج عرض التقارير المحسنة
    /// </summary>
    public class EnhancedReportsViewModel : ViewModelBase
    {
        private ObservableCollection<ReportTypeItem> _reportTypes;
        private ReportTypeItem _selectedReportType;
        private DateTime _startDate;
        private DateTime _endDate;
        private ObservableCollection<Account> _accounts;
        private Account _selectedAccount;
        private ObservableCollection<PostingStatusItem> _postingStatuses;
        private PostingStatusItem _selectedPostingStatus;
        private DataTable _reportData;
        private bool _hasNoData;
        private object _chartSeries;
        private string[] _chartXLabels;
        private string _chartXAxisTitle;
        private string _chartYAxisTitle;
        private Func<double, string> _chartYFormatter;
        private object _chartTooltip;

        /// <summary>
        /// أنواع التقارير
        /// </summary>
        public ObservableCollection<ReportTypeItem> ReportTypes
        {
            get => _reportTypes;
            set => SetProperty(ref _reportTypes, value);
        }

        /// <summary>
        /// نوع التقرير المحدد
        /// </summary>
        public ReportTypeItem SelectedReportType
        {
            get => _selectedReportType;
            set
            {
                if (SetProperty(ref _selectedReportType, value))
                {
                    // تحديث البيانات عند تغيير نوع التقرير
                    LoadReportData();
                }
            }
        }

        /// <summary>
        /// تاريخ البداية
        /// </summary>
        public DateTime StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        /// <summary>
        /// تاريخ النهاية
        /// </summary>
        public DateTime EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        /// <summary>
        /// قائمة الحسابات
        /// </summary>
        public ObservableCollection<Account> Accounts
        {
            get => _accounts;
            set => SetProperty(ref _accounts, value);
        }

        /// <summary>
        /// الحساب المحدد
        /// </summary>
        public Account SelectedAccount
        {
            get => _selectedAccount;
            set => SetProperty(ref _selectedAccount, value);
        }

        /// <summary>
        /// حالات الترحيل
        /// </summary>
        public ObservableCollection<PostingStatusItem> PostingStatuses
        {
            get => _postingStatuses;
            set => SetProperty(ref _postingStatuses, value);
        }

        /// <summary>
        /// حالة الترحيل المحددة
        /// </summary>
        public PostingStatusItem SelectedPostingStatus
        {
            get => _selectedPostingStatus;
            set => SetProperty(ref _selectedPostingStatus, value);
        }

        /// <summary>
        /// بيانات التقرير
        /// </summary>
        public DataTable ReportData
        {
            get => _reportData;
            set => SetProperty(ref _reportData, value);
        }

        /// <summary>
        /// هل لا توجد بيانات
        /// </summary>
        public bool HasNoData
        {
            get => _hasNoData;
            set => SetProperty(ref _hasNoData, value);
        }

        /// <summary>
        /// سلسلة بيانات الرسم البياني
        /// </summary>
        public object ChartSeries
        {
            get => _chartSeries;
            set => SetProperty(ref _chartSeries, value);
        }

        /// <summary>
        /// تسميات محور X
        /// </summary>
        public string[] ChartXLabels
        {
            get => _chartXLabels;
            set => SetProperty(ref _chartXLabels, value);
        }

        /// <summary>
        /// عنوان محور X
        /// </summary>
        public string ChartXAxisTitle
        {
            get => _chartXAxisTitle;
            set => SetProperty(ref _chartXAxisTitle, value);
        }

        /// <summary>
        /// عنوان محور Y
        /// </summary>
        public string ChartYAxisTitle
        {
            get => _chartYAxisTitle;
            set => SetProperty(ref _chartYAxisTitle, value);
        }

        /// <summary>
        /// منسق قيم محور Y
        /// </summary>
        public Func<double, string> ChartYFormatter
        {
            get => _chartYFormatter;
            set => SetProperty(ref _chartYFormatter, value);
        }

        /// <summary>
        /// تلميح الرسم البياني
        /// </summary>
        public object ChartTooltip
        {
            get => _chartTooltip;
            set => SetProperty(ref _chartTooltip, value);
        }

        /// <summary>
        /// أمر تطبيق التصفية
        /// </summary>
        public ICommand ApplyFilterCommand { get; }

        /// <summary>
        /// أمر تصدير إلى Excel
        /// </summary>
        public ICommand ExportToExcelCommand { get; }

        /// <summary>
        /// أمر تصدير إلى CSV
        /// </summary>
        public ICommand ExportToCsvCommand { get; }

        /// <summary>
        /// أمر تصدير إلى PDF
        /// </summary>
        public ICommand ExportToPdfCommand { get; }

        /// <summary>
        /// أمر طباعة التقرير
        /// </summary>
        public ICommand PrintReportCommand { get; }

        public EnhancedReportsViewModel()
        {
            // تهيئة الأوامر
            ApplyFilterCommand = new RelayCommand(ApplyFilter);
            ExportToExcelCommand = new RelayCommand(ExportToExcel);
            ExportToCsvCommand = new RelayCommand(ExportToCsv);
            ExportToPdfCommand = new RelayCommand(ExportToPdf);
            PrintReportCommand = new RelayCommand(PrintReport);

            // تهيئة البيانات
            InitializeData();
        }

        /// <summary>
        /// تهيئة البيانات
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // تهيئة أنواع التقارير
                ReportTypes = new ObservableCollection<ReportTypeItem>
                {
                    new ReportTypeItem { Id = 1, Name = "الميزانية العمومية" },
                    new ReportTypeItem { Id = 2, Name = "قائمة الدخل" },
                    new ReportTypeItem { Id = 3, Name = "التدفق النقدي" },
                    new ReportTypeItem { Id = 4, Name = "حركة الحساب" },
                    new ReportTypeItem { Id = 5, Name = "ميزان المراجعة" },
                    new ReportTypeItem { Id = 6, Name = "تقرير المبيعات" },
                    new ReportTypeItem { Id = 7, Name = "تقرير المشتريات" },
                    new ReportTypeItem { Id = 8, Name = "تقرير العملاء" },
                    new ReportTypeItem { Id = 9, Name = "تقرير الموردين" }
                };

                // تهيئة حالات الترحيل
                PostingStatuses = new ObservableCollection<PostingStatusItem>
                {
                    new PostingStatusItem { Id = 0, Name = "الكل" },
                    new PostingStatusItem { Id = 1, Name = "مرحل" },
                    new PostingStatusItem { Id = 2, Name = "غير مرحل" }
                };

                // تهيئة التواريخ
                StartDate = new DateTime(DateTime.Now.Year, 1, 1);
                EndDate = DateTime.Now;

                // تحميل الحسابات
                LoadAccounts();

                // تحديد القيم الافتراضية
                SelectedReportType = ReportTypes.FirstOrDefault();
                SelectedPostingStatus = PostingStatuses.FirstOrDefault();

                // تهيئة منسق قيم محور Y
                ChartYFormatter = value => value.ToString("N0");

                // تم تعطيل تلميح الرسم البياني مؤقتاً بسبب مشكلة توافق المكتبة
                ChartTooltip = null;

                // تحميل بيانات التقرير
                LoadReportData();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EnhancedReportsViewModel.InitializeData");
            }
        }

        /// <summary>
        /// تحميل الحسابات
        /// </summary>
        private void LoadAccounts()
        {
            try
            {
                // تحميل الحسابات من قاعدة البيانات
                var accounts = App.DbContext.Accounts.ToList();

                // إضافة خيار "الكل"
                accounts.Insert(0, new Account { Id = 0, Name = "الكل", Code = "0" });

                Accounts = new ObservableCollection<Account>(accounts);
                SelectedAccount = Accounts.FirstOrDefault();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EnhancedReportsViewModel.LoadAccounts");
            }
        }

        /// <summary>
        /// تحميل بيانات التقرير
        /// </summary>
        private void LoadReportData()
        {
            try
            {
                if (SelectedReportType == null)
                {
                    HasNoData = true;
                    return;
                }

                // إنشاء بيانات تجريبية للعرض
                var dataTable = CreateSampleReportData(SelectedReportType.Id);
                ReportData = dataTable;
                HasNoData = dataTable == null || dataTable.Rows.Count == 0;

                // تحديث الرسم البياني
                UpdateChart();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EnhancedReportsViewModel.LoadReportData");
            }
        }

        /// <summary>
        /// إنشاء بيانات تجريبية للتقرير
        /// </summary>
        private DataTable CreateSampleReportData(int reportTypeId)
        {
            var dataTable = new DataTable();

            switch (reportTypeId)
            {
                case 1: // الميزانية العمومية
                    dataTable.Columns.Add("الحساب", typeof(string));
                    dataTable.Columns.Add("الأصول", typeof(decimal));
                    dataTable.Columns.Add("الخصوم", typeof(decimal));
                    dataTable.Columns.Add("حقوق الملكية", typeof(decimal));

                    dataTable.Rows.Add("النقدية", 50000, 0, 0);
                    dataTable.Rows.Add("المدينون", 25000, 0, 0);
                    dataTable.Rows.Add("المخزون", 35000, 0, 0);
                    dataTable.Rows.Add("الأصول الثابتة", 100000, 0, 0);
                    dataTable.Rows.Add("الدائنون", 0, 30000, 0);
                    dataTable.Rows.Add("القروض", 0, 50000, 0);
                    dataTable.Rows.Add("رأس المال", 0, 0, 100000);
                    dataTable.Rows.Add("الأرباح المحتجزة", 0, 0, 30000);
                    break;

                case 2: // قائمة الدخل
                    dataTable.Columns.Add("البند", typeof(string));
                    dataTable.Columns.Add("المبلغ", typeof(decimal));

                    dataTable.Rows.Add("المبيعات", 200000);
                    dataTable.Rows.Add("تكلفة المبيعات", -120000);
                    dataTable.Rows.Add("مجمل الربح", 80000);
                    dataTable.Rows.Add("مصاريف إدارية", -20000);
                    dataTable.Rows.Add("مصاريف بيع وتسويق", -15000);
                    dataTable.Rows.Add("مصاريف أخرى", -5000);
                    dataTable.Rows.Add("صافي الربح", 40000);
                    break;

                case 6: // تقرير المبيعات
                    dataTable.Columns.Add("الشهر", typeof(string));
                    dataTable.Columns.Add("المبيعات", typeof(decimal));
                    dataTable.Columns.Add("التكلفة", typeof(decimal));
                    dataTable.Columns.Add("الربح", typeof(decimal));

                    dataTable.Rows.Add("يناير", 15000, 9000, 6000);
                    dataTable.Rows.Add("فبراير", 18000, 10800, 7200);
                    dataTable.Rows.Add("مارس", 22000, 13200, 8800);
                    dataTable.Rows.Add("أبريل", 20000, 12000, 8000);
                    dataTable.Rows.Add("مايو", 25000, 15000, 10000);
                    dataTable.Rows.Add("يونيو", 30000, 18000, 12000);
                    dataTable.Rows.Add("يوليو", 28000, 16800, 11200);
                    dataTable.Rows.Add("أغسطس", 32000, 19200, 12800);
                    dataTable.Rows.Add("سبتمبر", 35000, 21000, 14000);
                    dataTable.Rows.Add("أكتوبر", 40000, 24000, 16000);
                    dataTable.Rows.Add("نوفمبر", 45000, 27000, 18000);
                    dataTable.Rows.Add("ديسمبر", 50000, 30000, 20000);
                    break;

                default:
                    // إنشاء بيانات افتراضية
                    dataTable.Columns.Add("العنوان", typeof(string));
                    dataTable.Columns.Add("القيمة", typeof(decimal));

                    Random random = new Random();
                    for (int i = 1; i <= 10; i++)
                    {
                        dataTable.Rows.Add($"بند {i}", random.Next(1000, 50000));
                    }
                    break;
            }

            return dataTable;
        }

        /// <summary>
        /// تحديث الرسم البياني
        /// </summary>
        private void UpdateChart()
        {
            try
            {
                if (ReportData == null || ReportData.Rows.Count == 0)
                {
                    ChartSeries = null;
                    ChartXLabels = new string[0];
                    return;
                }

                // تم تعطيل الرسم البياني مؤقتاً بسبب مشكلة توافق المكتبة
                ChartSeries = null;

                switch (SelectedReportType.Id)
                {
                    case 1: // الميزانية العمومية
                        ChartXAxisTitle = "الحساب";
                        ChartYAxisTitle = "القيمة";
                        ChartXLabels = ReportData.AsEnumerable().Select(row => row.Field<string>("الحساب")).ToArray();

                        // تم تعطيل الرسم البياني مؤقتاً بسبب مشكلة توافق المكتبة
                        break;

                    case 2: // قائمة الدخل
                        ChartXAxisTitle = "البند";
                        ChartYAxisTitle = "المبلغ";
                        ChartXLabels = ReportData.AsEnumerable().Select(row => row.Field<string>("البند")).ToArray();

                        // تم تعطيل الرسم البياني مؤقتاً بسبب مشكلة توافق المكتبة
                        break;

                    case 6: // تقرير المبيعات
                        ChartXAxisTitle = "الشهر";
                        ChartYAxisTitle = "القيمة";
                        ChartXLabels = ReportData.AsEnumerable().Select(row => row.Field<string>("الشهر")).ToArray();

                        // تم تعطيل الرسم البياني مؤقتاً بسبب مشكلة توافق المكتبة
                        break;

                    default:
                        ChartXAxisTitle = "العنوان";
                        ChartYAxisTitle = "القيمة";
                        ChartXLabels = ReportData.AsEnumerable().Select(row => row.Field<string>("العنوان")).ToArray();

                        // تم تعطيل الرسم البياني مؤقتاً بسبب مشكلة توافق المكتبة
                        break;
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EnhancedReportsViewModel.UpdateChart");
            }
        }

        /// <summary>
        /// تطبيق التصفية
        /// </summary>
        private void ApplyFilter(object parameter)
        {
            try
            {
                // تحميل بيانات التقرير بناءً على معايير التصفية
                LoadReportData();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EnhancedReportsViewModel.ApplyFilter");
            }
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void ExportToExcel(object parameter)
        {
            try
            {
                if (ReportData == null || ReportData.Rows.Count == 0)
                {
                    System.Windows.MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var fileName = $"{SelectedReportType.Name}_{DateTime.Now:yyyy-MM-dd}.xlsx";
                var filePath = ReportExporter.ShowSaveFileDialog(fileName, "Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*");

                if (!string.IsNullOrEmpty(filePath))
                {
                    // تحويل DataTable إلى قائمة من الكائنات الديناميكية
                    var data = new List<dynamic>();
                    foreach (DataRow row in ReportData.Rows)
                    {
                        var item = new System.Dynamic.ExpandoObject() as IDictionary<string, object>;
                        foreach (DataColumn column in ReportData.Columns)
                        {
                            item[column.ColumnName] = row[column];
                        }
                        data.Add(item);
                    }

                    if (ReportExporter.ExportToExcel(data, filePath))
                    {
                        System.Windows.MessageBox.Show("تم تصدير التقرير بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EnhancedReportsViewModel.ExportToExcel");
            }
        }

        /// <summary>
        /// تصدير إلى CSV
        /// </summary>
        private void ExportToCsv(object parameter)
        {
            try
            {
                if (ReportData == null || ReportData.Rows.Count == 0)
                {
                    System.Windows.MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var fileName = $"{SelectedReportType.Name}_{DateTime.Now:yyyy-MM-dd}.csv";
                var filePath = ReportExporter.ShowSaveFileDialog(fileName, "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*");

                if (!string.IsNullOrEmpty(filePath))
                {
                    // تحويل DataTable إلى قائمة من الكائنات الديناميكية
                    var data = new List<dynamic>();
                    foreach (DataRow row in ReportData.Rows)
                    {
                        var item = new System.Dynamic.ExpandoObject() as IDictionary<string, object>;
                        foreach (DataColumn column in ReportData.Columns)
                        {
                            item[column.ColumnName] = row[column];
                        }
                        data.Add(item);
                    }

                    if (ReportExporter.ExportToCsv(data, filePath))
                    {
                        System.Windows.MessageBox.Show("تم تصدير التقرير بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EnhancedReportsViewModel.ExportToCsv");
            }
        }

        /// <summary>
        /// تصدير إلى PDF
        /// </summary>
        private void ExportToPdf(object parameter)
        {
            try
            {
                if (ReportData == null || ReportData.Rows.Count == 0)
                {
                    System.Windows.MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var fileName = $"{SelectedReportType.Name}_{DateTime.Now:yyyy-MM-dd}.pdf";
                var filePath = ReportExporter.ShowSaveFileDialog(fileName, "PDF Files (*.pdf)|*.pdf|All Files (*.*)|*.*");

                if (!string.IsNullOrEmpty(filePath))
                {
                    // الحصول على عنصر التقرير
                    var reportElement = Application.Current.MainWindow.FindName("MainContent") as ContentControl;
                    if (reportElement != null && reportElement.Content is FrameworkElement element)
                    {
                        if (ReportExporter.ExportToPdf(element, filePath, SelectedReportType.Name))
                        {
                            System.Windows.MessageBox.Show("تم تصدير التقرير بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EnhancedReportsViewModel.ExportToPdf");
            }
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void PrintReport(object parameter)
        {
            try
            {
                if (ReportData == null || ReportData.Rows.Count == 0)
                {
                    System.Windows.MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء حوار الطباعة
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // الحصول على عنصر التقرير
                    var reportElement = Application.Current.MainWindow.FindName("MainContent") as ContentControl;
                    if (reportElement != null && reportElement.Content is FrameworkElement element)
                    {
                        // حفظ حالة العنصر الأصلية
                        var originalWidth = element.Width;
                        var originalHeight = element.Height;

                        // تعيين الحجم للطباعة
                        element.Width = printDialog.PrintableAreaWidth;
                        element.Height = printDialog.PrintableAreaHeight;
                        element.Measure(new Size(element.Width, element.Height));
                        element.Arrange(new Rect(0, 0, element.Width, element.Height));

                        // طباعة العنصر
                        printDialog.PrintVisual(element, SelectedReportType.Name);

                        // استعادة حالة العنصر الأصلية
                        element.Width = originalWidth;
                        element.Height = originalHeight;
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "EnhancedReportsViewModel.PrintReport");
            }
        }
    }

    /// <summary>
    /// عنصر نوع التقرير
    /// </summary>
    public class ReportTypeItem
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    /// <summary>
    /// عنصر حالة الترحيل
    /// </summary>
    public class PostingStatusItem
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
}
