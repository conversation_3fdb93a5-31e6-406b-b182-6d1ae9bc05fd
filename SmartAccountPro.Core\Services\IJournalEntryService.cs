using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Core.Services
{
    /// <summary>
    /// واجهة خدمة القيود المحاسبية
    /// </summary>
    public interface IJournalEntryService
    {
        /// <summary>
        /// الحصول على جميع القيود المحاسبية
        /// </summary>
        Task<IEnumerable<JournalEntry>> GetAllJournalEntriesAsync();

        /// <summary>
        /// الحصول على القيود المحاسبية في فترة معينة
        /// </summary>
        Task<IEnumerable<JournalEntry>> GetJournalEntriesByDateRangeAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// الحصول على قيد محاسبي بواسطة المعرف
        /// </summary>
        Task<JournalEntry> GetJournalEntryByIdAsync(int id);

        /// <summary>
        /// الحصول على قيد محاسبي بواسطة الرقم
        /// </summary>
        Task<JournalEntry> GetJournalEntryByNumberAsync(string entryNumber);

        /// <summary>
        /// إنشاء قيد محاسبي جديد
        /// </summary>
        Task<JournalEntry> CreateJournalEntryAsync(JournalEntry journalEntry);

        /// <summary>
        /// تحديث قيد محاسبي
        /// </summary>
        Task<bool> UpdateJournalEntryAsync(JournalEntry journalEntry);

        /// <summary>
        /// حذف قيد محاسبي
        /// </summary>
        Task<bool> DeleteJournalEntryAsync(int id);

        /// <summary>
        /// ترحيل قيد محاسبي
        /// </summary>
        Task<bool> PostJournalEntryAsync(int id, int userId);

        /// <summary>
        /// إلغاء ترحيل قيد محاسبي
        /// </summary>
        Task<bool> UnpostJournalEntryAsync(int id);

        /// <summary>
        /// التحقق من توازن القيد المحاسبي
        /// </summary>
        bool IsJournalEntryBalanced(JournalEntry journalEntry);

        /// <summary>
        /// الحصول على دفتر اليومية
        /// </summary>
        Task<IEnumerable<JournalEntry>> GetJournalAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// الحصول على دفتر الأستاذ لحساب معين
        /// </summary>
        Task<IEnumerable<JournalEntryItem>> GetLedgerAsync(int accountId, DateTime fromDate, DateTime toDate);
    }
}
