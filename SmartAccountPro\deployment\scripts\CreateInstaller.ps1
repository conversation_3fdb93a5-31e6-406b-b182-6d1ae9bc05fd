# SmartAccount Pro - سكريبت إنشاء حزم التثبيت الاحترافية
# PowerShell Script for Creating Professional Installation Packages

param(
    [string]$Version = "1.0.0",
    [string]$Configuration = "Release",
    [string]$OutputPath = ".\publish",
    [string]$InstallerPath = ".\deployment\installers",
    [switch]$CreatePortable = $true,
    [switch]$CreateSetup = $true,
    [switch]$CreateMSI = $true,
    [switch]$SignFiles = $false
)

# إعداد المتغيرات
$ProjectName = "SmartAccountPro"
$ProjectPath = Get-Location
$PublishPath = Join-Path $ProjectPath $OutputPath
$DeploymentPath = Join-Path $ProjectPath "deployment"
$ScriptsPath = Join-Path $DeploymentPath "scripts"
$InstallersOutputPath = Join-Path $DeploymentPath "installers"

Write-Host "🚀 SmartAccount Pro - إنشاء حزم التثبيت الاحترافية" -ForegroundColor Green
Write-Host "═══════════════════════════════════════════════════════════" -ForegroundColor Green

# التحقق من المتطلبات
function Test-Prerequisites {
    Write-Host "🔍 التحقق من المتطلبات..." -ForegroundColor Yellow
    
    # التحقق من .NET SDK
    try {
        $dotnetVersion = dotnet --version
        Write-Host "   ✅ .NET SDK: $dotnetVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "   ❌ .NET SDK غير مثبت" -ForegroundColor Red
        return $false
    }
    
    # التحقق من مساحة القرص
    $drive = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq (Split-Path $ProjectPath -Qualifier) }
    $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
    
    if ($freeSpaceGB -gt 2) {
        Write-Host "   ✅ مساحة القرص المتاحة: $freeSpaceGB GB" -ForegroundColor Green
    }
    else {
        Write-Host "   ❌ مساحة القرص غير كافية: $freeSpaceGB GB" -ForegroundColor Red
        return $false
    }
    
    # إنشاء المجلدات المطلوبة
    @($PublishPath, $InstallersOutputPath) | ForEach-Object {
        if (-not (Test-Path $_)) {
            New-Item -Path $_ -ItemType Directory -Force | Out-Null
            Write-Host "   📁 تم إنشاء مجلد: $_" -ForegroundColor Cyan
        }
    }
    
    return $true
}

# تنظيف المجلدات السابقة
function Clear-PreviousBuilds {
    Write-Host "🧹 تنظيف المجلدات السابقة..." -ForegroundColor Yellow
    
    if (Test-Path $PublishPath) {
        Remove-Item $PublishPath -Recurse -Force
        Write-Host "   ✅ تم تنظيف مجلد النشر" -ForegroundColor Green
    }
    
    if (Test-Path $InstallersOutputPath) {
        Get-ChildItem $InstallersOutputPath -File | Remove-Item -Force
        Write-Host "   ✅ تم تنظيف مجلد المثبتات" -ForegroundColor Green
    }
}

# بناء التطبيق
function Build-Application {
    Write-Host "🔨 بناء التطبيق..." -ForegroundColor Yellow
    
    # بناء نسخة Release
    Write-Host "   📦 بناء نسخة Release..." -ForegroundColor Cyan
    $releaseOutput = Join-Path $PublishPath "Release"
    
    $buildArgs = @(
        "publish"
        "$ProjectName.csproj"
        "-c", $Configuration
        "-o", "`"$releaseOutput`""
        "--self-contained", "false"
        "-p:PublishSingleFile=false"
        "-p:PublishReadyToRun=true"
        "-p:IncludeNativeLibrariesForSelfExtract=true"
    )
    
    $process = Start-Process -FilePath "dotnet" -ArgumentList $buildArgs -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "   ✅ تم بناء نسخة Release بنجاح" -ForegroundColor Green
    }
    else {
        Write-Host "   ❌ فشل في بناء نسخة Release" -ForegroundColor Red
        return $false
    }
    
    # بناء نسخة Self-Contained للـ x64
    Write-Host "   📦 بناء نسخة Self-Contained x64..." -ForegroundColor Cyan
    $selfContainedOutput = Join-Path $PublishPath "SelfContained-win-x64"
    
    $buildArgs = @(
        "publish"
        "$ProjectName.csproj"
        "-c", $Configuration
        "-r", "win-x64"
        "-o", "`"$selfContainedOutput`""
        "--self-contained", "true"
        "-p:PublishSingleFile=true"
        "-p:PublishReadyToRun=true"
        "-p:IncludeNativeLibrariesForSelfExtract=true"
    )
    
    $process = Start-Process -FilePath "dotnet" -ArgumentList $buildArgs -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "   ✅ تم بناء نسخة Self-Contained x64 بنجاح" -ForegroundColor Green
    }
    else {
        Write-Host "   ❌ فشل في بناء نسخة Self-Contained x64" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# تحسين الملفات المنشورة
function Optimize-PublishedFiles {
    Write-Host "⚡ تحسين الملفات المنشورة..." -ForegroundColor Yellow
    
    Get-ChildItem $PublishPath -Recurse -Directory | ForEach-Object {
        $dir = $_.FullName
        
        # حذف ملفات التطوير غير الضرورية
        @("*.pdb", "*.xml", "*.dev.json") | ForEach-Object {
            Get-ChildItem $dir -Filter $_ -Recurse | Remove-Item -Force -ErrorAction SilentlyContinue
        }
        
        # حذف مجلدات التطوير
        @("ref", "runtimes\*\native", "cs", "de", "es", "fr", "it", "ja", "ko", "pl", "pt-BR", "ru", "tr", "zh-Hans", "zh-Hant") | ForEach-Object {
            $path = Join-Path $dir $_
            if (Test-Path $path) {
                Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
            }
        }
    }
    
    Write-Host "   ✅ تم تحسين الملفات المنشورة" -ForegroundColor Green
}

# إنشاء ملفات التكوين
function Create-ConfigurationFiles {
    Write-Host "⚙️ إنشاء ملفات التكوين..." -ForegroundColor Yellow
    
    # إنشاء ملف معلومات الإصدار
    $versionInfo = @{
        Version = $Version
        BuildDate = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        Configuration = $Configuration
        Platform = "Windows"
        Framework = "net6.0-windows"
        Features = @(
            "Accounting",
            "Invoicing",
            "Reporting",
            "UserManagement",
            "BackupRestore"
        )
    }
    
    $versionJson = $versionInfo | ConvertTo-Json -Depth 3
    
    Get-ChildItem $PublishPath -Directory | ForEach-Object {
        $versionFile = Join-Path $_.FullName "version.json"
        $versionJson | Out-File -FilePath $versionFile -Encoding UTF8
    }
    
    # إنشاء ملف بيانات اعتماد افتراضية
    $defaultCredentials = @"
SmartAccount Pro - بيانات تسجيل الدخول الافتراضية
═══════════════════════════════════════════════════════

اسم المستخدم: admin
كلمة المرور: SmartAccount2024!

تحذير أمني:
يرجى تغيير كلمة المرور فور تسجيل الدخول الأول لضمان أمان النظام.

تاريخ الإنشاء: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
الإصدار: $Version
"@
    
    Get-ChildItem $PublishPath -Directory | ForEach-Object {
        $credentialsFile = Join-Path $_.FullName "default-credentials.txt"
        $defaultCredentials | Out-File -FilePath $credentialsFile -Encoding UTF8
    }
    
    Write-Host "   ✅ تم إنشاء ملفات التكوين" -ForegroundColor Green
}

# إنشاء checksums للتحقق من التكامل
function Create-Checksums {
    Write-Host "🔐 إنشاء checksums للتحقق من التكامل..." -ForegroundColor Yellow
    
    Get-ChildItem $PublishPath -Directory | ForEach-Object {
        $dir = $_.FullName
        $checksumFile = Join-Path $dir "checksums.sha256"
        
        $checksums = Get-ChildItem $dir -File -Recurse | Where-Object { $_.Name -ne "checksums.sha256" } | ForEach-Object {
            $hash = Get-FileHash $_.FullName -Algorithm SHA256
            $relativePath = $_.FullName.Substring($dir.Length + 1)
            "$($hash.Hash.ToLower())  $relativePath"
        }
        
        $checksums | Out-File -FilePath $checksumFile -Encoding ASCII
    }
    
    Write-Host "   ✅ تم إنشاء checksums" -ForegroundColor Green
}

# إنشاء النسخة المحمولة
function Create-PortableVersion {
    if (-not $CreatePortable) { return }
    
    Write-Host "📦 إنشاء النسخة المحمولة..." -ForegroundColor Yellow
    
    $sourceDir = Join-Path $PublishPath "SelfContained-win-x64"
    $portableZip = Join-Path $InstallersOutputPath "$ProjectName-v$Version-Portable.zip"
    
    if (Test-Path $sourceDir) {
        # إنشاء ملف تشغيل سريع
        $launcherContent = @"
@echo off
title SmartAccount Pro - Portable
echo Starting SmartAccount Pro...
start "" "$ProjectName.exe"
"@
        $launcherFile = Join-Path $sourceDir "Start-SmartAccountPro.bat"
        $launcherContent | Out-File -FilePath $launcherFile -Encoding ASCII
        
        # إنشاء ملف README للنسخة المحمولة
        $readmeContent = @"
SmartAccount Pro - النسخة المحمولة
═══════════════════════════════════

هذه نسخة محمولة من SmartAccount Pro لا تحتاج إلى تثبيت.

طريقة الاستخدام:
1. استخرج جميع الملفات إلى مجلد
2. شغل SmartAccountPro.exe أو Start-SmartAccountPro.bat
3. استخدم التطبيق بشكل طبيعي

المميزات:
- لا تحتاج تثبيت
- يمكن تشغيلها من USB
- لا تؤثر على النظام
- تحتوي على جميع المتطلبات

الإصدار: $Version
تاريخ البناء: $(Get-Date -Format "yyyy-MM-dd")

للدعم الفني: <EMAIL>
"@
        $readmeFile = Join-Path $sourceDir "README-Portable.txt"
        $readmeContent | Out-File -FilePath $readmeFile -Encoding UTF8
        
        # ضغط الملفات
        try {
            Compress-Archive -Path "$sourceDir\*" -DestinationPath $portableZip -Force
            
            $zipSize = [math]::Round((Get-Item $portableZip).Length / 1MB, 2)
            Write-Host "   ✅ تم إنشاء النسخة المحمولة: $zipSize MB" -ForegroundColor Green
        }
        catch {
            Write-Host "   ❌ فشل في إنشاء النسخة المحمولة: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    else {
        Write-Host "   ❌ مجلد المصدر غير موجود: $sourceDir" -ForegroundColor Red
    }
}

# إنشاء سكريبت Inno Setup
function Create-InnoSetupScript {
    if (-not $CreateSetup) { return }
    
    Write-Host "🛠️ إنشاء سكريبت Inno Setup..." -ForegroundColor Yellow
    
    $sourceDir = Join-Path $PublishPath "Release"
    $innoScript = Join-Path $ScriptsPath "$ProjectName-Setup.iss"
    
    $scriptContent = @"
; SmartAccount Pro - Inno Setup Script
; تم إنشاؤه تلقائياً بواسطة سكريبت PowerShell

#define MyAppName "SmartAccount Pro"
#define MyAppVersion "$Version"
#define MyAppPublisher "SmartAccount Pro Team"
#define MyAppURL "https://www.smartaccountpro.com/"
#define MyAppExeName "$ProjectName.exe"
#define MyAppAssocName "SmartAccount Pro Document"
#define MyAppAssocExt ".sap"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppName}
ChangesAssociations=yes
DisableProgramGroupPage=yes
LicenseFile=$DeploymentPath\documentation\License.txt
InfoBeforeFile=$DeploymentPath\documentation\InstallationNotes.txt
OutputDir=$InstallersOutputPath
OutputBaseFilename=$ProjectName-v$Version-Setup
SetupIconFile=$sourceDir\icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
Source: "$sourceDir\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "$sourceDir\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Registry]
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocExt}\OpenWithProgids"; ValueType: string; ValueName: "{#MyAppAssocKey}"; ValueData: ""; Flags: uninsdeletevalue
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}"; ValueType: string; ValueName: ""; ValueData: "{#MyAppAssocName}"; Flags: uninsdeletekey
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppExeName},0"
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""
Root: HKA; Subkey: "Software\Classes\Applications\{#MyAppExeName}\SupportedTypes"; ValueType: string; ValueName: ".myp"; ValueData: ""

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[Code]
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // إنشاء قاعدة البيانات الافتراضية
    // يمكن إضافة كود إضافي هنا
  end;
end;
"@
    
    $scriptContent | Out-File -FilePath $innoScript -Encoding UTF8
    Write-Host "   ✅ تم إنشاء سكريبت Inno Setup" -ForegroundColor Green
    
    # محاولة تجميع المثبت إذا كان Inno Setup مثبت
    $innoCompiler = "${env:ProgramFiles(x86)}\Inno Setup 6\ISCC.exe"
    if (Test-Path $innoCompiler) {
        Write-Host "   🔨 تجميع مثبت Inno Setup..." -ForegroundColor Cyan
        
        try {
            $process = Start-Process -FilePath $innoCompiler -ArgumentList "`"$innoScript`"" -Wait -PassThru -NoNewWindow
            
            if ($process.ExitCode -eq 0) {
                $setupFile = Join-Path $InstallersOutputPath "$ProjectName-v$Version-Setup.exe"
                if (Test-Path $setupFile) {
                    $setupSize = [math]::Round((Get-Item $setupFile).Length / 1MB, 2)
                    Write-Host "   ✅ تم إنشاء مثبت Inno Setup: $setupSize MB" -ForegroundColor Green
                }
            }
            else {
                Write-Host "   ❌ فشل في تجميع مثبت Inno Setup" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "   ❌ خطأ في تجميع مثبت Inno Setup: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    else {
        Write-Host "   ⚠️ Inno Setup غير مثبت - تم إنشاء السكريبت فقط" -ForegroundColor Yellow
        Write-Host "   💡 لتجميع المثبت: قم بتثبيت Inno Setup وشغل السكريبت يدوياً" -ForegroundColor Cyan
    }
}

# إنشاء ملفات WiX للـ MSI
function Create-WiXProject {
    if (-not $CreateMSI) { return }
    
    Write-Host "📦 إنشاء مشروع WiX للـ MSI..." -ForegroundColor Yellow
    
    # إنشاء ملف WiX أساسي
    $wixFile = Join-Path $ScriptsPath "$ProjectName.wxs"
    
    $wixContent = @"
<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*" Name="SmartAccount Pro" Language="1033" Version="$Version" 
           Manufacturer="SmartAccount Pro Team" UpgradeCode="A1B2C3D4-E5F6-7890-ABCD-EF1234567890">
    
    <Package InstallerVersion="200" Compressed="yes" InstallScope="perMachine" />
    
    <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed." />
    <MediaTemplate EmbedCab="yes" />
    
    <Feature Id="ProductFeature" Title="SmartAccount Pro" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
    </Feature>
    
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="SmartAccount Pro" />
      </Directory>
    </Directory>
    
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <!-- سيتم إضافة المكونات تلقائياً -->
    </ComponentGroup>
    
  </Product>
</Wix>
"@
    
    $wixContent | Out-File -FilePath $wixFile -Encoding UTF8
    Write-Host "   ✅ تم إنشاء ملف WiX أساسي" -ForegroundColor Green
    Write-Host "   💡 لإكمال إنشاء MSI: استخدم WiX Toolset لتجميع الملف" -ForegroundColor Cyan
}

# توقيع الملفات (إذا كان متاح)
function Sign-Files {
    if (-not $SignFiles) { return }
    
    Write-Host "✍️ توقيع الملفات..." -ForegroundColor Yellow
    
    # البحث عن أداة التوقيع
    $signTool = "${env:ProgramFiles(x86)}\Windows Kits\10\bin\*\x64\signtool.exe"
    $signToolPath = Get-ChildItem $signTool -ErrorAction SilentlyContinue | Select-Object -Last 1
    
    if ($signToolPath) {
        # توقيع الملفات التنفيذية
        Get-ChildItem $PublishPath -Filter "*.exe" -Recurse | ForEach-Object {
            Write-Host "   🔏 توقيع: $($_.Name)" -ForegroundColor Cyan
            # يمكن إضافة أوامر التوقيع هنا
        }
        
        # توقيع ملفات المثبت
        Get-ChildItem $InstallersOutputPath -Filter "*.exe" | ForEach-Object {
            Write-Host "   🔏 توقيع مثبت: $($_.Name)" -ForegroundColor Cyan
            # يمكن إضافة أوامر التوقيع هنا
        }
        
        Write-Host "   ✅ تم توقيع الملفات" -ForegroundColor Green
    }
    else {
        Write-Host "   ⚠️ أداة التوقيع غير متاحة" -ForegroundColor Yellow
    }
}

# إنشاء تقرير النشر
function Create-DeploymentReport {
    Write-Host "📋 إنشاء تقرير النشر..." -ForegroundColor Yellow
    
    $reportFile = Join-Path $DeploymentPath "deployment-report.md"
    
    $report = @"
# SmartAccount Pro - تقرير النشر

## معلومات البناء
- **الإصدار:** $Version
- **التكوين:** $Configuration
- **تاريخ البناء:** $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
- **المنصة:** Windows x64
- **إطار العمل:** .NET 6.0

## الملفات المنشأة

### النسخة المحمولة
"@
    
    $portableZip = Join-Path $InstallersOutputPath "$ProjectName-v$Version-Portable.zip"
    if (Test-Path $portableZip) {
        $size = [math]::Round((Get-Item $portableZip).Length / 1MB, 2)
        $report += "`n- ✅ **$ProjectName-v$Version-Portable.zip** ($size MB)"
    }
    
    $setupExe = Join-Path $InstallersOutputPath "$ProjectName-v$Version-Setup.exe"
    if (Test-Path $setupExe) {
        $size = [math]::Round((Get-Item $setupExe).Length / 1MB, 2)
        $report += "`n- ✅ **$ProjectName-v$Version-Setup.exe** ($size MB)"
    }
    
    $report += @"

## ملفات الدعم
- ✅ دليل المستخدم
- ✅ دليل التثبيت
- ✅ ملفات التكوين
- ✅ بيانات الاعتماد الافتراضية
- ✅ Checksums للتحقق من التكامل

## الخطوات التالية
1. اختبار المثبتات في بيئات نظيفة
2. توزيع الملفات على المستخدمين
3. تحديث الموقع الإلكتروني
4. إرسال إشعارات التحديث

تم إنشاء هذا التقرير تلقائياً في: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@
    
    $report | Out-File -FilePath $reportFile -Encoding UTF8
    Write-Host "   ✅ تم إنشاء تقرير النشر" -ForegroundColor Green
}

# الدالة الرئيسية
function Main {
    try {
        # التحقق من المتطلبات
        if (-not (Test-Prerequisites)) {
            Write-Host "❌ فشل في التحقق من المتطلبات" -ForegroundColor Red
            return
        }
        
        # تنظيف المجلدات السابقة
        Clear-PreviousBuilds
        
        # بناء التطبيق
        if (-not (Build-Application)) {
            Write-Host "❌ فشل في بناء التطبيق" -ForegroundColor Red
            return
        }
        
        # تحسين الملفات
        Optimize-PublishedFiles
        
        # إنشاء ملفات التكوين
        Create-ConfigurationFiles
        
        # إنشاء checksums
        Create-Checksums
        
        # إنشاء حزم التثبيت
        Create-PortableVersion
        Create-InnoSetupScript
        Create-WiXProject
        
        # توقيع الملفات
        Sign-Files
        
        # إنشاء تقرير النشر
        Create-DeploymentReport
        
        Write-Host "`n🎉 تم إنشاء جميع حزم التثبيت بنجاح!" -ForegroundColor Green
        Write-Host "📁 مجلد المثبتات: $InstallersOutputPath" -ForegroundColor Cyan
        
        # عرض ملخص الملفات المنشأة
        Write-Host "`n📦 الملفات المنشأة:" -ForegroundColor Yellow
        Get-ChildItem $InstallersOutputPath -File | ForEach-Object {
            $size = [math]::Round($_.Length / 1MB, 2)
            Write-Host "   📄 $($_.Name) ($size MB)" -ForegroundColor Cyan
        }
        
    }
    catch {
        Write-Host "❌ خطأ عام: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# تشغيل السكريبت
Main
