using SmartAccountPro.Core.Models;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// نموذج عرض القيد المحاسبي
    /// </summary>
    public class JournalEntryViewModel : ViewModelBase
    {
        private string _entryNumber;
        private DateTime _entryDate;
        private EntrySource _source;
        private string _referenceNumber;
        private string _description;
        private ObservableCollection<JournalEntryItemViewModel> _items;
        private JournalEntryItemViewModel _selectedItem;
        private decimal _totalDebit;
        private decimal _totalCredit;
        private decimal _difference;

        /// <summary>
        /// رقم القيد
        /// </summary>
        public string EntryNumber
        {
            get => _entryNumber;
            set => SetProperty(ref _entryNumber, value);
        }

        /// <summary>
        /// تاريخ القيد
        /// </summary>
        public DateTime EntryDate
        {
            get => _entryDate;
            set => SetProperty(ref _entryDate, value);
        }

        /// <summary>
        /// مصدر القيد
        /// </summary>
        public EntrySource Source
        {
            get => _source;
            set => SetProperty(ref _source, value);
        }

        /// <summary>
        /// رقم المرجع
        /// </summary>
        public string ReferenceNumber
        {
            get => _referenceNumber;
            set => SetProperty(ref _referenceNumber, value);
        }

        /// <summary>
        /// البيان
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>
        /// بنود القيد
        /// </summary>
        public ObservableCollection<JournalEntryItemViewModel> Items
        {
            get => _items;
            set => SetProperty(ref _items, value);
        }

        /// <summary>
        /// البند المحدد
        /// </summary>
        public JournalEntryItemViewModel SelectedItem
        {
            get => _selectedItem;
            set => SetProperty(ref _selectedItem, value);
        }

        /// <summary>
        /// إجمالي المدين
        /// </summary>
        public decimal TotalDebit
        {
            get => _totalDebit;
            set => SetProperty(ref _totalDebit, value);
        }

        /// <summary>
        /// إجمالي الدائن
        /// </summary>
        public decimal TotalCredit
        {
            get => _totalCredit;
            set => SetProperty(ref _totalCredit, value);
        }

        /// <summary>
        /// الفرق
        /// </summary>
        public decimal Difference
        {
            get => _difference;
            set => SetProperty(ref _difference, value);
        }

        /// <summary>
        /// أمر إضافة بند
        /// </summary>
        public ICommand AddItemCommand { get; }

        /// <summary>
        /// أمر حذف بند
        /// </summary>
        public ICommand DeleteItemCommand { get; }

        /// <summary>
        /// أمر حفظ
        /// </summary>
        public ICommand SaveCommand { get; }

        /// <summary>
        /// أمر حفظ وترحيل
        /// </summary>
        public ICommand SaveAndPostCommand { get; }

        /// <summary>
        /// أمر إلغاء
        /// </summary>
        public ICommand CancelCommand { get; }

        public JournalEntryViewModel()
        {
            // تهيئة الخصائص
            _entryNumber = string.Empty;
            _referenceNumber = string.Empty;
            _description = string.Empty;
            _items = new ObservableCollection<JournalEntryItemViewModel>();
            _selectedItem = new JournalEntryItemViewModel();

            EntryDate = DateTime.Now;
            Source = EntrySource.Manual;
            Items = new ObservableCollection<JournalEntryItemViewModel>();

            AddItemCommand = new RelayCommand(AddItem);
            DeleteItemCommand = new RelayCommand(DeleteItem, CanDeleteItem);
            SaveCommand = new RelayCommand(Save, CanSave);
            SaveAndPostCommand = new RelayCommand(SaveAndPost, CanSave);
            CancelCommand = new RelayCommand(Cancel);

            // إضافة بند فارغ
            AddItem(null);

            // توليد رقم قيد جديد
            GenerateEntryNumber();
        }

        /// <summary>
        /// توليد رقم قيد جديد
        /// </summary>
        private void GenerateEntryNumber()
        {
            // هنا يمكن إضافة كود لتوليد رقم قيد جديد من قاعدة البيانات
            // مثال بسيط للتوضيح فقط
            EntryNumber = $"JE-{DateTime.Now.Year}-{DateTime.Now.Month.ToString("D2")}-{DateTime.Now.Day.ToString("D2")}-001";
        }

        /// <summary>
        /// إضافة بند
        /// </summary>
        private void AddItem(object parameter)
        {
            var item = new JournalEntryItemViewModel
            {
                Index = Items.Count + 1
            };
            item.PropertyChanged += Item_PropertyChanged;
            Items.Add(item);
            CalculateTotals();
        }

        /// <summary>
        /// حذف بند
        /// </summary>
        private void DeleteItem(object parameter)
        {
            if (SelectedItem == null)
                return;

            MessageBoxResult result = MessageBox.Show(
                "هل أنت متأكد من حذف البند المحدد؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                SelectedItem.PropertyChanged -= Item_PropertyChanged;
                Items.Remove(SelectedItem);

                // إعادة ترقيم البنود
                for (int i = 0; i < Items.Count; i++)
                {
                    Items[i].Index = i + 1;
                }

                CalculateTotals();
            }
        }

        /// <summary>
        /// التحقق من إمكانية حذف بند
        /// </summary>
        private bool CanDeleteItem(object parameter)
        {
            return SelectedItem != null && Items.Count > 1;
        }

        /// <summary>
        /// حفظ
        /// </summary>
        private void Save(object parameter)
        {
            try
            {
                if (!ValidateEntry())
                    return;

                // هنا يمكن إضافة كود لحفظ القيد في قاعدة البيانات
                MessageBox.Show("تم حفظ القيد بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ القيد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ وترحيل
        /// </summary>
        private void SaveAndPost(object parameter)
        {
            try
            {
                if (!ValidateEntry())
                    return;

                // هنا يمكن إضافة كود لحفظ وترحيل القيد في قاعدة البيانات
                MessageBox.Show("تم حفظ وترحيل القيد بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ وترحيل القيد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// التحقق من إمكانية حفظ
        /// </summary>
        private bool CanSave(object parameter)
        {
            return Items.Count > 0 && Math.Abs(Difference) < 0.01m;
        }

        /// <summary>
        /// إلغاء
        /// </summary>
        private void Cancel(object parameter)
        {
            MessageBoxResult result = MessageBox.Show(
                "هل أنت متأكد من إلغاء القيد؟ سيتم فقدان جميع البيانات المدخلة.",
                "تأكيد الإلغاء",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // هنا يمكن إضافة كود للعودة إلى الشاشة السابقة
                MessageBox.Show("تم إلغاء القيد", "إلغاء", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات القيد
        /// </summary>
        private bool ValidateEntry()
        {
            if (string.IsNullOrEmpty(Description))
            {
                MessageBox.Show("الرجاء إدخال بيان القيد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (Items.Count == 0)
            {
                MessageBox.Show("يجب إضافة بند واحد على الأقل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            foreach (var item in Items)
            {
                if (string.IsNullOrEmpty(item.AccountName))
                {
                    MessageBox.Show($"الرجاء اختيار حساب للبند رقم {item.Index}", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                if (item.DebitAmount == 0 && item.CreditAmount == 0)
                {
                    MessageBox.Show($"الرجاء إدخال مبلغ مدين أو دائن للبند رقم {item.Index}", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }
            }

            if (Math.Abs(Difference) >= 0.01m)
            {
                MessageBox.Show("يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// حساب الإجماليات
        /// </summary>
        private void CalculateTotals()
        {
            TotalDebit = Items.Sum(i => i.DebitAmount);
            TotalCredit = Items.Sum(i => i.CreditAmount);
            Difference = TotalDebit - TotalCredit;
        }

        /// <summary>
        /// معالج حدث تغيير خصائص البند
        /// </summary>
        private void Item_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(JournalEntryItemViewModel.DebitAmount) ||
                e.PropertyName == nameof(JournalEntryItemViewModel.CreditAmount))
            {
                CalculateTotals();
            }
        }
    }

    /// <summary>
    /// نموذج عرض بند القيد المحاسبي
    /// </summary>
    public class JournalEntryItemViewModel : ViewModelBase
    {
        private int _index;
        private int _accountId;
        private string _accountName = string.Empty;
        private string _description = string.Empty;
        private decimal _debitAmount;
        private decimal _creditAmount;

        /// <summary>
        /// الرقم التسلسلي
        /// </summary>
        public int Index
        {
            get => _index;
            set => SetProperty(ref _index, value);
        }

        /// <summary>
        /// معرف الحساب
        /// </summary>
        public int AccountId
        {
            get => _accountId;
            set => SetProperty(ref _accountId, value);
        }

        /// <summary>
        /// اسم الحساب
        /// </summary>
        public string AccountName
        {
            get => _accountName;
            set => SetProperty(ref _accountName, value);
        }

        /// <summary>
        /// البيان
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>
        /// المبلغ المدين
        /// </summary>
        public decimal DebitAmount
        {
            get => _debitAmount;
            set
            {
                if (SetProperty(ref _debitAmount, value) && value > 0)
                {
                    // إذا تم إدخال مبلغ مدين، يتم تصفير المبلغ الدائن
                    CreditAmount = 0;
                }
            }
        }

        /// <summary>
        /// المبلغ الدائن
        /// </summary>
        public decimal CreditAmount
        {
            get => _creditAmount;
            set
            {
                if (SetProperty(ref _creditAmount, value) && value > 0)
                {
                    // إذا تم إدخال مبلغ دائن، يتم تصفير المبلغ المدين
                    DebitAmount = 0;
                }
            }
        }
    }
}
