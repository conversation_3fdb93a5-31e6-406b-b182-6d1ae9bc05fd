using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Data;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للبحث في البيانات
    /// </summary>
    public class SearchHelper
    {
        private readonly AppDbContext _context;
        private readonly CacheManager<string, object> _cache;

        /// <summary>
        /// إنشاء مساعد البحث
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public SearchHelper(AppDbContext context)
        {
            _context = context;
            _cache = new CacheManager<string, object>("SearchHelper", TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(10));
        }

        /// <summary>
        /// البحث في الحسابات
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="maxResults">الحد الأقصى للنتائج</param>
        /// <param name="useCache">استخدام ذاكرة التخزين المؤقت</param>
        /// <returns>نتائج البحث</returns>
        public async Task<List<Account>> SearchAccountsAsync(string searchTerm, int maxResults = 20, bool useCache = true)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("SearchHelper.SearchAccounts", async () =>
                {
                    // التحقق من مصطلح البحث
                    if (string.IsNullOrWhiteSpace(searchTerm))
                    {
                        return new List<Account>();
                    }

                    // تنظيف مصطلح البحث
                    searchTerm = searchTerm.Trim().ToLower();

                    // إنشاء مفتاح ذاكرة التخزين المؤقت
                    string cacheKey = $"SearchAccounts_{searchTerm}_{maxResults}";

                    // التحقق من وجود النتائج في ذاكرة التخزين المؤقت
                    if (useCache && _cache.TryGetValue(cacheKey, out var cachedResults))
                    {
                        return (List<Account>)cachedResults;
                    }

                    // البحث في الحسابات
                    var results = await _context.Accounts
                        .Where(a => a.IsActive &&
                                   (a.Code.ToLower().Contains(searchTerm) ||
                                    a.Name.ToLower().Contains(searchTerm) ||
                                    a.Description.ToLower().Contains(searchTerm)))
                        .OrderBy(a => a.Code)
                        .Take(maxResults)
                        .ToListAsync();

                    // تخزين النتائج في ذاكرة التخزين المؤقت
                    if (useCache)
                    {
                        _cache.Add(cacheKey, results);
                    }

                    Debug.WriteLine($"[SEARCH] تم البحث عن الحسابات بمصطلح '{searchTerm}'. تم العثور على {results.Count} نتيجة.");
                    return results;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SearchHelper.SearchAccounts");
                return new List<Account>();
            }
        }

        /// <summary>
        /// البحث في المستخدمين
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="maxResults">الحد الأقصى للنتائج</param>
        /// <param name="useCache">استخدام ذاكرة التخزين المؤقت</param>
        /// <returns>نتائج البحث</returns>
        public async Task<List<User>> SearchUsersAsync(string searchTerm, int maxResults = 20, bool useCache = true)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("SearchHelper.SearchUsers", async () =>
                {
                    // التحقق من مصطلح البحث
                    if (string.IsNullOrWhiteSpace(searchTerm))
                    {
                        return new List<User>();
                    }

                    // تنظيف مصطلح البحث
                    searchTerm = searchTerm.Trim().ToLower();

                    // إنشاء مفتاح ذاكرة التخزين المؤقت
                    string cacheKey = $"SearchUsers_{searchTerm}_{maxResults}";

                    // التحقق من وجود النتائج في ذاكرة التخزين المؤقت
                    if (useCache && _cache.TryGetValue(cacheKey, out var cachedResults))
                    {
                        return (List<User>)cachedResults;
                    }

                    // البحث في المستخدمين
                    var results = await _context.Users
                        .Where(u => u.IsActive &&
                                   (u.Username.ToLower().Contains(searchTerm) ||
                                    u.FullName.ToLower().Contains(searchTerm) ||
                                    u.Email.ToLower().Contains(searchTerm)))
                        .OrderBy(u => u.Username)
                        .Take(maxResults)
                        .ToListAsync();

                    // تخزين النتائج في ذاكرة التخزين المؤقت
                    if (useCache)
                    {
                        _cache.Add(cacheKey, results);
                    }

                    Debug.WriteLine($"[SEARCH] تم البحث عن المستخدمين بمصطلح '{searchTerm}'. تم العثور على {results.Count} نتيجة.");
                    return results;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SearchHelper.SearchUsers");
                return new List<User>();
            }
        }

        /// <summary>
        /// البحث في القيود المحاسبية
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="maxResults">الحد الأقصى للنتائج</param>
        /// <param name="useCache">استخدام ذاكرة التخزين المؤقت</param>
        /// <returns>نتائج البحث</returns>
        public async Task<List<JournalEntry>> SearchJournalEntriesAsync(string searchTerm, DateTime? fromDate = null, DateTime? toDate = null, int maxResults = 20, bool useCache = true)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("SearchHelper.SearchJournalEntries", async () =>
                {
                    // التحقق من مصطلح البحث
                    if (string.IsNullOrWhiteSpace(searchTerm) && !fromDate.HasValue && !toDate.HasValue)
                    {
                        return new List<JournalEntry>();
                    }

                    // تنظيف مصطلح البحث
                    searchTerm = searchTerm?.Trim().ToLower() ?? string.Empty;

                    // إنشاء مفتاح ذاكرة التخزين المؤقت
                    string cacheKey = $"SearchJournalEntries_{searchTerm}_{fromDate}_{toDate}_{maxResults}";

                    // التحقق من وجود النتائج في ذاكرة التخزين المؤقت
                    if (useCache && _cache.TryGetValue(cacheKey, out var cachedResults))
                    {
                        return (List<JournalEntry>)cachedResults;
                    }

                    // بناء الاستعلام
                    var query = _context.JournalEntries.AsQueryable();

                    // تطبيق فلتر مصطلح البحث
                    if (!string.IsNullOrEmpty(searchTerm))
                    {
                        query = query.Where(j => j.ReferenceNumber.ToLower().Contains(searchTerm) ||
                                               j.Description.ToLower().Contains(searchTerm));
                    }

                    // تطبيق فلتر التاريخ
                    if (fromDate.HasValue)
                    {
                        query = query.Where(j => j.EntryDate >= fromDate.Value);
                    }

                    if (toDate.HasValue)
                    {
                        query = query.Where(j => j.EntryDate <= toDate.Value);
                    }

                    // تنفيذ الاستعلام
                    var results = await query
                        .OrderByDescending(j => j.EntryDate)
                        .ThenByDescending(j => j.Id)
                        .Take(maxResults)
                        .ToListAsync();

                    // تخزين النتائج في ذاكرة التخزين المؤقت
                    if (useCache)
                    {
                        _cache.Add(cacheKey, results);
                    }

                    Debug.WriteLine($"[SEARCH] تم البحث عن القيود المحاسبية بمصطلح '{searchTerm}'. تم العثور على {results.Count} نتيجة.");
                    return results;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SearchHelper.SearchJournalEntries");
                return new List<JournalEntry>();
            }
        }

        /// <summary>
        /// البحث في الفواتير
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="maxResults">الحد الأقصى للنتائج</param>
        /// <param name="useCache">استخدام ذاكرة التخزين المؤقت</param>
        /// <returns>نتائج البحث</returns>
        public async Task<List<Invoice>> SearchInvoicesAsync(string searchTerm, DateTime? fromDate = null, DateTime? toDate = null, int maxResults = 20, bool useCache = true)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("SearchHelper.SearchInvoices", async () =>
                {
                    // التحقق من مصطلح البحث
                    if (string.IsNullOrWhiteSpace(searchTerm) && !fromDate.HasValue && !toDate.HasValue)
                    {
                        return new List<Invoice>();
                    }

                    // تنظيف مصطلح البحث
                    searchTerm = searchTerm?.Trim().ToLower() ?? string.Empty;

                    // إنشاء مفتاح ذاكرة التخزين المؤقت
                    string cacheKey = $"SearchInvoices_{searchTerm}_{fromDate}_{toDate}_{maxResults}";

                    // التحقق من وجود النتائج في ذاكرة التخزين المؤقت
                    if (useCache && _cache.TryGetValue(cacheKey, out var cachedResults))
                    {
                        return (List<Invoice>)cachedResults;
                    }

                    // بناء الاستعلام
                    var query = _context.Invoices
                        .Include(i => i.Customer)
                        .Include(i => i.Supplier)
                        .AsQueryable();

                    // تطبيق فلتر مصطلح البحث
                    if (!string.IsNullOrEmpty(searchTerm))
                    {
                        query = query.Where(i => i.InvoiceNumber.ToLower().Contains(searchTerm) ||
                                               (i.Customer != null && i.Customer.Name.ToLower().Contains(searchTerm)) ||
                                               (i.Supplier != null && i.Supplier.Name.ToLower().Contains(searchTerm)) ||
                                               (i.Notes != null && i.Notes.ToLower().Contains(searchTerm)));
                    }

                    // تطبيق فلتر التاريخ
                    if (fromDate.HasValue)
                    {
                        query = query.Where(i => i.InvoiceDate >= fromDate.Value);
                    }

                    if (toDate.HasValue)
                    {
                        query = query.Where(i => i.InvoiceDate <= toDate.Value);
                    }

                    // تنفيذ الاستعلام
                    var results = await query
                        .OrderByDescending(i => i.InvoiceDate)
                        .ThenByDescending(i => i.Id)
                        .Take(maxResults)
                        .ToListAsync();

                    // تخزين النتائج في ذاكرة التخزين المؤقت
                    if (useCache)
                    {
                        _cache.Add(cacheKey, results);
                    }

                    Debug.WriteLine($"[SEARCH] تم البحث عن الفواتير بمصطلح '{searchTerm}'. تم العثور على {results.Count} نتيجة.");
                    return results;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SearchHelper.SearchInvoices");
                return new List<Invoice>();
            }
        }

        /// <summary>
        /// البحث في سجل التدقيق
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="maxResults">الحد الأقصى للنتائج</param>
        /// <param name="useCache">استخدام ذاكرة التخزين المؤقت</param>
        /// <returns>نتائج البحث</returns>
        public async Task<List<AuditLog>> SearchAuditLogsAsync(string searchTerm, DateTime? fromDate = null, DateTime? toDate = null, int maxResults = 20, bool useCache = true)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("SearchHelper.SearchAuditLogs", async () =>
                {
                    // التحقق من مصطلح البحث
                    if (string.IsNullOrWhiteSpace(searchTerm) && !fromDate.HasValue && !toDate.HasValue)
                    {
                        return new List<AuditLog>();
                    }

                    // تنظيف مصطلح البحث
                    searchTerm = searchTerm?.Trim().ToLower() ?? string.Empty;

                    // إنشاء مفتاح ذاكرة التخزين المؤقت
                    string cacheKey = $"SearchAuditLogs_{searchTerm}_{fromDate}_{toDate}_{maxResults}";

                    // التحقق من وجود النتائج في ذاكرة التخزين المؤقت
                    if (useCache && _cache.TryGetValue(cacheKey, out var cachedResults))
                    {
                        return (List<AuditLog>)cachedResults;
                    }

                    // بناء الاستعلام
                    var query = _context.AuditLogs.AsQueryable();

                    // تطبيق فلتر مصطلح البحث
                    if (!string.IsNullOrEmpty(searchTerm))
                    {
                        query = query.Where(a => a.Action.ToLower().Contains(searchTerm) ||
                                               a.EntityType.ToLower().Contains(searchTerm) ||
                                               a.Details.ToLower().Contains(searchTerm));
                    }

                    // تطبيق فلتر التاريخ
                    if (fromDate.HasValue)
                    {
                        query = query.Where(a => a.Timestamp >= fromDate.Value);
                    }

                    if (toDate.HasValue)
                    {
                        query = query.Where(a => a.Timestamp <= toDate.Value);
                    }

                    // تنفيذ الاستعلام
                    var results = await query
                        .OrderByDescending(a => a.Timestamp)
                        .ThenByDescending(a => a.Id)
                        .Take(maxResults)
                        .ToListAsync();

                    // تخزين النتائج في ذاكرة التخزين المؤقت
                    if (useCache)
                    {
                        _cache.Add(cacheKey, results);
                    }

                    Debug.WriteLine($"[SEARCH] تم البحث عن سجل التدقيق بمصطلح '{searchTerm}'. تم العثور على {results.Count} نتيجة.");
                    return results;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SearchHelper.SearchAuditLogs");
                return new List<AuditLog>();
            }
        }

        /// <summary>
        /// البحث الشامل
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="maxResults">الحد الأقصى للنتائج</param>
        /// <returns>نتائج البحث</returns>
        public async Task<GlobalSearchResult> GlobalSearchAsync(string searchTerm, int maxResults = 5)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("SearchHelper.GlobalSearch", async () =>
                {
                    // التحقق من مصطلح البحث
                    if (string.IsNullOrWhiteSpace(searchTerm))
                    {
                        return new GlobalSearchResult();
                    }

                    // تنظيف مصطلح البحث
                    searchTerm = searchTerm.Trim().ToLower();

                    // إنشاء نتيجة البحث الشامل
                    var result = new GlobalSearchResult
                    {
                        SearchTerm = searchTerm,
                        Timestamp = DateTime.Now
                    };

                    // البحث في الحسابات
                    result.Accounts = await SearchAccountsAsync(searchTerm, maxResults, true);

                    // البحث في المستخدمين
                    result.Users = await SearchUsersAsync(searchTerm, maxResults, true);

                    // البحث في القيود المحاسبية
                    result.JournalEntries = await SearchJournalEntriesAsync(searchTerm, null, null, maxResults, true);

                    // البحث في الفواتير
                    result.Invoices = await SearchInvoicesAsync(searchTerm, null, null, maxResults, true);

                    Debug.WriteLine($"[SEARCH] تم البحث الشامل بمصطلح '{searchTerm}'. تم العثور على {result.TotalResults} نتيجة.");
                    return result;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SearchHelper.GlobalSearch");
                return new GlobalSearchResult
                {
                    SearchTerm = searchTerm,
                    Timestamp = DateTime.Now
                };
            }
        }

        /// <summary>
        /// مسح ذاكرة التخزين المؤقت
        /// </summary>
        public void ClearCache()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("SearchHelper.ClearCache", () =>
                {
                    // مسح ذاكرة التخزين المؤقت
                    _cache.Clear();

                    Debug.WriteLine("[SEARCH] تم مسح ذاكرة التخزين المؤقت للبحث.");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SearchHelper.ClearCache");
            }
        }
    }

    /// <summary>
    /// نتيجة البحث الشامل
    /// </summary>
    public class GlobalSearchResult
    {
        /// <summary>
        /// مصطلح البحث
        /// </summary>
        public string SearchTerm { get; set; } = string.Empty;

        /// <summary>
        /// وقت البحث
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// نتائج البحث في الحسابات
        /// </summary>
        public List<Account> Accounts { get; set; } = new List<Account>();

        /// <summary>
        /// نتائج البحث في المستخدمين
        /// </summary>
        public List<User> Users { get; set; } = new List<User>();

        /// <summary>
        /// نتائج البحث في القيود المحاسبية
        /// </summary>
        public List<JournalEntry> JournalEntries { get; set; } = new List<JournalEntry>();

        /// <summary>
        /// نتائج البحث في الفواتير
        /// </summary>
        public List<Invoice> Invoices { get; set; } = new List<Invoice>();

        /// <summary>
        /// إجمالي عدد النتائج
        /// </summary>
        public int TotalResults => Accounts.Count + Users.Count + JournalEntries.Count + Invoices.Count;
    }
}
