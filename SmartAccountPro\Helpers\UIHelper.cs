using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لواجهة المستخدم
    /// </summary>
    public static class UIHelper
    {
        /// <summary>
        /// إظهار عنصر بتأثير متلاشي
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="duration">مدة التأثير</param>
        /// <returns>مهمة</returns>
        public static async Task FadeInAsync(UIElement element, TimeSpan duration)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // تعيين الشفافية إلى 0
                element.Opacity = 0;
                element.Visibility = Visibility.Visible;

                // إنشاء تأثير متلاشي
                var animation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = duration
                };

                // تشغيل التأثير
                element.BeginAnimation(UIElement.OpacityProperty, animation);

                // انتظار انتهاء التأثير
                await Task.Delay(duration);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "UIHelper.FadeIn");
            }
        }

        /// <summary>
        /// إخفاء عنصر بتأثير متلاشي
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="duration">مدة التأثير</param>
        /// <returns>مهمة</returns>
        public static async Task FadeOutAsync(UIElement element, TimeSpan duration)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء تأثير متلاشي
                var animation = new DoubleAnimation
                {
                    From = element.Opacity,
                    To = 0,
                    Duration = duration
                };

                // تعيين حدث انتهاء التأثير
                animation.Completed += (s, e) => element.Visibility = Visibility.Collapsed;

                // تشغيل التأثير
                element.BeginAnimation(UIElement.OpacityProperty, animation);

                // انتظار انتهاء التأثير
                await Task.Delay(duration);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "UIHelper.FadeOut");
            }
        }

        /// <summary>
        /// تحريك عنصر
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="fromX">من X</param>
        /// <param name="toX">إلى X</param>
        /// <param name="fromY">من Y</param>
        /// <param name="toY">إلى Y</param>
        /// <param name="duration">مدة التأثير</param>
        /// <returns>مهمة</returns>
        public static async Task MoveAsync(UIElement element, double fromX, double toX, double fromY, double toY, TimeSpan duration)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء تحويل
                var transform = new TranslateTransform(fromX, fromY);
                element.RenderTransform = transform;

                // إنشاء تأثير حركة X
                var animationX = new DoubleAnimation
                {
                    From = fromX,
                    To = toX,
                    Duration = duration
                };

                // إنشاء تأثير حركة Y
                var animationY = new DoubleAnimation
                {
                    From = fromY,
                    To = toY,
                    Duration = duration
                };

                // تشغيل التأثير
                transform.BeginAnimation(TranslateTransform.XProperty, animationX);
                transform.BeginAnimation(TranslateTransform.YProperty, animationY);

                // انتظار انتهاء التأثير
                await Task.Delay(duration);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "UIHelper.Move");
            }
        }

        /// <summary>
        /// تغيير حجم عنصر
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="fromScale">من مقياس</param>
        /// <param name="toScale">إلى مقياس</param>
        /// <param name="duration">مدة التأثير</param>
        /// <returns>مهمة</returns>
        public static async Task ScaleAsync(UIElement element, double fromScale, double toScale, TimeSpan duration)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء تحويل
                var transform = new ScaleTransform(fromScale, fromScale);
                element.RenderTransform = transform;

                // إنشاء تأثير تغيير الحجم X
                var animationX = new DoubleAnimation
                {
                    From = fromScale,
                    To = toScale,
                    Duration = duration
                };

                // إنشاء تأثير تغيير الحجم Y
                var animationY = new DoubleAnimation
                {
                    From = fromScale,
                    To = toScale,
                    Duration = duration
                };

                // تشغيل التأثير
                transform.BeginAnimation(ScaleTransform.ScaleXProperty, animationX);
                transform.BeginAnimation(ScaleTransform.ScaleYProperty, animationY);

                // انتظار انتهاء التأثير
                await Task.Delay(duration);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "UIHelper.Scale");
            }
        }

        /// <summary>
        /// تدوير عنصر
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="fromAngle">من زاوية</param>
        /// <param name="toAngle">إلى زاوية</param>
        /// <param name="duration">مدة التأثير</param>
        /// <returns>مهمة</returns>
        public static async Task RotateAsync(UIElement element, double fromAngle, double toAngle, TimeSpan duration)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء تحويل
                var transform = new RotateTransform(fromAngle);
                element.RenderTransform = transform;

                // إنشاء تأثير دوران
                var animation = new DoubleAnimation
                {
                    From = fromAngle,
                    To = toAngle,
                    Duration = duration
                };

                // تشغيل التأثير
                transform.BeginAnimation(RotateTransform.AngleProperty, animation);

                // انتظار انتهاء التأثير
                await Task.Delay(duration);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "UIHelper.Rotate");
            }
        }

        /// <summary>
        /// تغيير لون عنصر
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="fromColor">من لون</param>
        /// <param name="toColor">إلى لون</param>
        /// <param name="duration">مدة التأثير</param>
        /// <param name="property">خاصية اللون</param>
        /// <returns>مهمة</returns>
        public static async Task ChangeColorAsync(Control element, Color fromColor, Color toColor, TimeSpan duration, DependencyProperty property)
        {
            try
            {
                // التحقق من العنصر
                if (element == null)
                    return;

                // إنشاء فرشاة
                var brush = new SolidColorBrush(fromColor);
                element.SetValue(property, brush);

                // إنشاء تأثير تغيير اللون
                var animation = new ColorAnimation
                {
                    From = fromColor,
                    To = toColor,
                    Duration = duration
                };

                // تشغيل التأثير
                brush.BeginAnimation(SolidColorBrush.ColorProperty, animation);

                // انتظار انتهاء التأثير
                await Task.Delay(duration);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "UIHelper.ChangeColor");
            }
        }

        /// <summary>
        /// تمرير إلى عنصر
        /// </summary>
        /// <param name="scrollViewer">عارض التمرير</param>
        /// <param name="element">العنصر</param>
        /// <param name="duration">مدة التأثير</param>
        /// <returns>مهمة</returns>
        public static async Task ScrollToElementAsync(ScrollViewer scrollViewer, UIElement element, TimeSpan duration)
        {
            try
            {
                // التحقق من العناصر
                if (scrollViewer == null || element == null)
                    return;

                // الحصول على موقع العنصر
                var position = element.TranslatePoint(new Point(0, 0), scrollViewer);

                // تمرير إلى العنصر
                scrollViewer.ScrollToVerticalOffset(position.Y);

                // انتظار انتهاء التأثير
                await Task.Delay(duration);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "UIHelper.ScrollToElement");
            }
        }

        /// <summary>
        /// تطبيق نمط على عنصر
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="style">النمط</param>
        public static void ApplyStyle(FrameworkElement element, Style style)
        {
            try
            {
                // التحقق من العناصر
                if (element == null || style == null)
                    return;

                // تطبيق النمط
                element.Style = style;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "UIHelper.ApplyStyle");
            }
        }

        /// <summary>
        /// تطبيق سمة على عنصر
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="theme">السمة</param>
        public static void ApplyTheme(FrameworkElement element, ResourceDictionary theme)
        {
            try
            {
                // التحقق من العناصر
                if (element == null || theme == null)
                    return;

                // تطبيق السمة
                element.Resources.MergedDictionaries.Clear();
                element.Resources.MergedDictionaries.Add(theme);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "UIHelper.ApplyTheme");
            }
        }
    }
}
