using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Data;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة النسخ الاحتياطية
    /// </summary>
    public class BackupManager
    {
        private readonly AppDbContext _context;
        private readonly string _backupPath;
        private readonly string _databasePath;

        /// <summary>
        /// إنشاء مدير النسخ الاحتياطية
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        /// <param name="databasePath">مسار قاعدة البيانات</param>
        public BackupManager(AppDbContext context, string databasePath)
        {
            _context = context;
            _databasePath = databasePath;
            _backupPath = Path.Combine(App.AppDataPath, "Backups");

            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            if (!Directory.Exists(_backupPath))
            {
                Directory.CreateDirectory(_backupPath);
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        /// <param name="includeAttachments">تضمين المرفقات</param>
        /// <param name="includeSettings">تضمين الإعدادات</param>
        /// <param name="includeExports">تضمين الصادرات</param>
        /// <param name="description">وصف النسخة الاحتياطية</param>
        /// <returns>معلومات النسخة الاحتياطية</returns>
        public async Task<BackupInfo> CreateBackupAsync(bool includeAttachments = true, bool includeSettings = true, bool includeExports = false, string? description = null)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("BackupManager.CreateBackup", async () =>
                {
                    // إنشاء معلومات النسخة الاحتياطية
                    var backupInfo = new BackupInfo
                    {
                        Id = Guid.NewGuid().ToString(),
                        CreatedAt = DateTime.Now,
                        Description = description ?? $"نسخة احتياطية تلقائية - {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                        IncludeAttachments = includeAttachments,
                        IncludeSettings = includeSettings,
                        IncludeExports = includeExports,
                        Size = 0,
                        FilePath = Path.Combine(_backupPath, $"Backup_{DateTime.Now:yyyyMMdd_HHmmss}.zip")
                    };

                    // إنشاء مجلد مؤقت
                    string tempPath = Path.Combine(Path.GetTempPath(), $"SmartAccountPro_Backup_{backupInfo.Id}");
                    Directory.CreateDirectory(tempPath);

                    try
                    {
                        // إغلاق الاتصال بقاعدة البيانات
                        await _context.Database.CloseConnectionAsync();

                        // نسخ ملف قاعدة البيانات
                        string dbFileName = Path.GetFileName(_databasePath);
                        string tempDbPath = Path.Combine(tempPath, dbFileName);
                        File.Copy(_databasePath, tempDbPath);

                        // إنشاء ملف معلومات النسخة الاحتياطية
                        string infoJson = System.Text.Json.JsonSerializer.Serialize(backupInfo);
                        File.WriteAllText(Path.Combine(tempPath, "backup_info.json"), infoJson);

                        // نسخ المرفقات
                        if (includeAttachments)
                        {
                            string attachmentsPath = Path.Combine(App.AppDataPath, "Attachments");
                            if (Directory.Exists(attachmentsPath))
                            {
                                string tempAttachmentsPath = Path.Combine(tempPath, "Attachments");
                                CopyDirectory(attachmentsPath, tempAttachmentsPath);
                            }
                        }

                        // نسخ الإعدادات
                        if (includeSettings)
                        {
                            string settingsPath = Path.Combine(App.AppDataPath, "Settings");
                            if (Directory.Exists(settingsPath))
                            {
                                string tempSettingsPath = Path.Combine(tempPath, "Settings");
                                CopyDirectory(settingsPath, tempSettingsPath);
                            }
                        }

                        // نسخ الصادرات
                        if (includeExports)
                        {
                            string exportsPath = Path.Combine(App.AppDataPath, "Exports");
                            if (Directory.Exists(exportsPath))
                            {
                                string tempExportsPath = Path.Combine(tempPath, "Exports");
                                CopyDirectory(exportsPath, tempExportsPath);
                            }
                        }

                        // إنشاء ملف الضغط
                        ZipFile.CreateFromDirectory(tempPath, backupInfo.FilePath);

                        // تحديث حجم النسخة الاحتياطية
                        var fileInfo = new FileInfo(backupInfo.FilePath);
                        backupInfo.Size = fileInfo.Length;

                        // إعادة فتح الاتصال بقاعدة البيانات
                        await _context.Database.OpenConnectionAsync();

                        // إضافة إشعار
                        NotificationManager.Instance.AddNotification(
                            "تم إنشاء نسخة احتياطية",
                            $"تم إنشاء نسخة احتياطية بنجاح: {Path.GetFileName(backupInfo.FilePath)}",
                            NotificationType.Success);

                        // تسجيل النسخة الاحتياطية
                        AuditTrailManager.LogAction("إنشاء نسخة احتياطية", "Backup", 0, $"تم إنشاء نسخة احتياطية: {backupInfo.Description}", 0);

                        Debug.WriteLine($"[BACKUP] تم إنشاء نسخة احتياطية: {backupInfo.FilePath}");
                        return backupInfo;
                    }
                    finally
                    {
                        // حذف المجلد المؤقت
                        if (Directory.Exists(tempPath))
                        {
                            Directory.Delete(tempPath, true);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackupManager.CreateBackup");
                throw;
            }
        }

        /// <summary>
        /// استعادة نسخة احتياطية
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <param name="restoreAttachments">استعادة المرفقات</param>
        /// <param name="restoreSettings">استعادة الإعدادات</param>
        /// <param name="restoreExports">استعادة الصادرات</param>
        /// <returns>نجاح العملية</returns>
        public async Task<bool> RestoreBackupAsync(string backupPath, bool restoreAttachments = true, bool restoreSettings = true, bool restoreExports = false)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("BackupManager.RestoreBackup", async () =>
                {
                    // التحقق من وجود ملف النسخة الاحتياطية
                    if (!File.Exists(backupPath))
                    {
                        throw new FileNotFoundException("ملف النسخة الاحتياطية غير موجود.", backupPath);
                    }

                    // إنشاء مجلد مؤقت
                    string tempPath = Path.Combine(Path.GetTempPath(), $"SmartAccountPro_Restore_{Guid.NewGuid()}");
                    Directory.CreateDirectory(tempPath);

                    try
                    {
                        // استخراج ملف الضغط
                        ZipFile.ExtractToDirectory(backupPath, tempPath);

                        // قراءة ملف معلومات النسخة الاحتياطية
                        string infoPath = Path.Combine(tempPath, "backup_info.json");
                        if (!File.Exists(infoPath))
                        {
                            throw new FileNotFoundException("ملف معلومات النسخة الاحتياطية غير موجود.", infoPath);
                        }

                        string infoJson = File.ReadAllText(infoPath);
                        var backupInfo = System.Text.Json.JsonSerializer.Deserialize<BackupInfo>(infoJson);

                        if (backupInfo == null)
                        {
                            throw new InvalidOperationException("فشل قراءة معلومات النسخة الاحتياطية.");
                        }

                        // إغلاق الاتصال بقاعدة البيانات
                        await _context.Database.CloseConnectionAsync();

                        // استعادة ملف قاعدة البيانات
                        string dbFileName = Path.GetFileName(_databasePath);
                        string tempDbPath = Path.Combine(tempPath, dbFileName);
                        if (File.Exists(tempDbPath))
                        {
                            File.Copy(tempDbPath, _databasePath, true);
                        }
                        else
                        {
                            throw new FileNotFoundException("ملف قاعدة البيانات غير موجود في النسخة الاحتياطية.", tempDbPath);
                        }

                        // استعادة المرفقات
                        if (restoreAttachments && backupInfo.IncludeAttachments)
                        {
                            string tempAttachmentsPath = Path.Combine(tempPath, "Attachments");
                            if (Directory.Exists(tempAttachmentsPath))
                            {
                                string attachmentsPath = Path.Combine(App.AppDataPath, "Attachments");
                                if (Directory.Exists(attachmentsPath))
                                {
                                    Directory.Delete(attachmentsPath, true);
                                }
                                CopyDirectory(tempAttachmentsPath, attachmentsPath);
                            }
                        }

                        // استعادة الإعدادات
                        if (restoreSettings && backupInfo.IncludeSettings)
                        {
                            string tempSettingsPath = Path.Combine(tempPath, "Settings");
                            if (Directory.Exists(tempSettingsPath))
                            {
                                string settingsPath = Path.Combine(App.AppDataPath, "Settings");
                                if (Directory.Exists(settingsPath))
                                {
                                    Directory.Delete(settingsPath, true);
                                }
                                CopyDirectory(tempSettingsPath, settingsPath);
                            }
                        }

                        // استعادة الصادرات
                        if (restoreExports && backupInfo.IncludeExports)
                        {
                            string tempExportsPath = Path.Combine(tempPath, "Exports");
                            if (Directory.Exists(tempExportsPath))
                            {
                                string exportsPath = Path.Combine(App.AppDataPath, "Exports");
                                if (Directory.Exists(exportsPath))
                                {
                                    Directory.Delete(exportsPath, true);
                                }
                                CopyDirectory(tempExportsPath, exportsPath);
                            }
                        }

                        // إعادة فتح الاتصال بقاعدة البيانات
                        await _context.Database.OpenConnectionAsync();

                        // إضافة إشعار
                        NotificationManager.Instance.AddNotification(
                            "تم استعادة النسخة الاحتياطية",
                            "تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.",
                            NotificationType.Success);

                        // تسجيل استعادة النسخة الاحتياطية
                        AuditTrailManager.LogAction("استعادة نسخة احتياطية", "Backup", 0, $"تم استعادة النسخة الاحتياطية: {backupInfo.Description}", 0);

                        Debug.WriteLine($"[BACKUP] تم استعادة النسخة الاحتياطية: {backupPath}");
                        return true;
                    }
                    finally
                    {
                        // حذف المجلد المؤقت
                        if (Directory.Exists(tempPath))
                        {
                            Directory.Delete(tempPath, true);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackupManager.RestoreBackup");
                throw;
            }
        }

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية
        /// </summary>
        /// <returns>قائمة النسخ الاحتياطية</returns>
        public async Task<List<BackupInfo>> GetBackupsAsync()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("BackupManager.GetBackups", async () =>
                {
                    // البحث عن ملفات النسخ الاحتياطية
                    var backupFiles = Directory.GetFiles(_backupPath, "*.zip");
                    var backups = new List<BackupInfo>();

                    foreach (var backupFile in backupFiles)
                    {
                        try
                        {
                            // إنشاء مجلد مؤقت
                            string tempPath = Path.Combine(Path.GetTempPath(), $"SmartAccountPro_Info_{Guid.NewGuid()}");
                            Directory.CreateDirectory(tempPath);

                            try
                            {
                                // استخراج ملف معلومات النسخة الاحتياطية
                                using (var archive = ZipFile.OpenRead(backupFile))
                                {
                                    var infoEntry = archive.GetEntry("backup_info.json");
                                    if (infoEntry != null)
                                    {
                                        infoEntry.ExtractToFile(Path.Combine(tempPath, "backup_info.json"));
                                    }
                                }

                                // قراءة ملف معلومات النسخة الاحتياطية
                                string infoPath = Path.Combine(tempPath, "backup_info.json");
                                if (File.Exists(infoPath))
                                {
                                    string infoJson = File.ReadAllText(infoPath);
                                    var backupInfo = System.Text.Json.JsonSerializer.Deserialize<BackupInfo>(infoJson);

                                    if (backupInfo != null)
                                    {
                                        // تحديث مسار الملف
                                        backupInfo.FilePath = backupFile;

                                        // تحديث حجم الملف
                                        var fileInfo = new FileInfo(backupFile);
                                        backupInfo.Size = fileInfo.Length;

                                        backups.Add(backupInfo);
                                    }
                                }
                            }
                            finally
                            {
                                // حذف المجلد المؤقت
                                if (Directory.Exists(tempPath))
                                {
                                    Directory.Delete(tempPath, true);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            ExceptionHandler.HandleException(ex, $"BackupManager.GetBackups.ProcessFile: {backupFile}");
                        }
                    }

                    // ترتيب النسخ الاحتياطية حسب تاريخ الإنشاء
                    return backups.OrderByDescending(b => b.CreatedAt).ToList();
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackupManager.GetBackups");
                return new List<BackupInfo>();
            }
        }

        /// <summary>
        /// حذف نسخة احتياطية
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>نجاح العملية</returns>
        public async Task<bool> DeleteBackupAsync(string backupPath)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("BackupManager.DeleteBackup", async () =>
                {
                    // التحقق من وجود ملف النسخة الاحتياطية
                    if (!File.Exists(backupPath))
                    {
                        throw new FileNotFoundException("ملف النسخة الاحتياطية غير موجود.", backupPath);
                    }

                    // حذف ملف النسخة الاحتياطية
                    File.Delete(backupPath);

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم حذف النسخة الاحتياطية",
                        $"تم حذف النسخة الاحتياطية بنجاح: {Path.GetFileName(backupPath)}",
                        NotificationType.Success);

                    // تسجيل حذف النسخة الاحتياطية
                    AuditTrailManager.LogAction("حذف نسخة احتياطية", "Backup", 0, $"تم حذف النسخة الاحتياطية: {Path.GetFileName(backupPath)}", 0);

                    Debug.WriteLine($"[BACKUP] تم حذف النسخة الاحتياطية: {backupPath}");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackupManager.DeleteBackup");
                throw;
            }
        }

        /// <summary>
        /// نسخ مجلد
        /// </summary>
        /// <param name="sourceDir">المجلد المصدر</param>
        /// <param name="destDir">المجلد الهدف</param>
        private void CopyDirectory(string sourceDir, string destDir)
        {
            try
            {
                // إنشاء المجلد الهدف إذا لم يكن موجوداً
                if (!Directory.Exists(destDir))
                {
                    Directory.CreateDirectory(destDir);
                }

                // نسخ الملفات
                foreach (string file in Directory.GetFiles(sourceDir))
                {
                    string destFile = Path.Combine(destDir, Path.GetFileName(file));
                    File.Copy(file, destFile, true);
                }

                // نسخ المجلدات الفرعية
                foreach (string subDir in Directory.GetDirectories(sourceDir))
                {
                    string destSubDir = Path.Combine(destDir, Path.GetFileName(subDir));
                    CopyDirectory(subDir, destSubDir);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackupManager.CopyDirectory");
                throw;
            }
        }
    }

    /// <summary>
    /// معلومات النسخة الاحتياطية
    /// </summary>
    public class BackupInfo
    {
        /// <summary>
        /// معرف النسخة الاحتياطية
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ إنشاء النسخة الاحتياطية
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// وصف النسخة الاحتياطية
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// حجم النسخة الاحتياطية
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// مسار ملف النسخة الاحتياطية
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// تضمين المرفقات
        /// </summary>
        public bool IncludeAttachments { get; set; }

        /// <summary>
        /// تضمين الإعدادات
        /// </summary>
        public bool IncludeSettings { get; set; }

        /// <summary>
        /// تضمين الصادرات
        /// </summary>
        public bool IncludeExports { get; set; }
    }
}
