using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Xps;
using System.Windows.Xps.Packaging;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// مساعد لتصدير التقارير بصيغ مختلفة
    /// </summary>
    public static class ReportExporter
    {
        /// <summary>
        /// تصدير البيانات إلى ملف CSV
        /// </summary>
        public static bool ExportToCsv<T>(IEnumerable<T> data, string filePath, string delimiter = ",")
        {
            try
            {
                if (data == null || !data.Any())
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                var properties = typeof(T).GetProperties()
                    .Where(p => p.CanRead && (p.PropertyType.IsPrimitive ||
                                             p.PropertyType == typeof(string) ||
                                             p.PropertyType == typeof(DateTime) ||
                                             p.PropertyType == typeof(decimal) ||
                                             p.PropertyType.IsEnum))
                    .ToList();

                if (!properties.Any())
                {
                    MessageBox.Show("لا توجد خصائص قابلة للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                using (var writer = new StreamWriter(filePath, false, Encoding.UTF8))
                {
                    // كتابة رؤوس الأعمدة
                    writer.WriteLine(string.Join(delimiter, properties.Select(p => $"\"{p.Name}\"")));

                    // كتابة البيانات
                    foreach (var item in data)
                    {
                        var values = properties.Select(p =>
                        {
                            var value = p.GetValue(item);
                            if (value == null)
                                return "\"\"";

                            // تنسيق القيم حسب النوع
                            if (value is DateTime dateTime)
                                return $"\"{dateTime:yyyy-MM-dd HH:mm:ss}\"";
                            else if (value is decimal decimalValue)
                                return $"\"{decimalValue:0.00}\"";
                            else
                                return $"\"{value.ToString().Replace("\"", "\"\"")}\"";
                        });

                        writer.WriteLine(string.Join(delimiter, values));
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ReportExporter.ExportToCsv");
                return false;
            }
        }

        /// <summary>
        /// تصدير البيانات إلى ملف Excel
        /// </summary>
        public static bool ExportToExcel<T>(IEnumerable<T> data, string filePath)
        {
            try
            {
                if (data == null || !data.Any())
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                // تحويل البيانات إلى DataTable
                var dataTable = new DataTable();
                var properties = typeof(T).GetProperties()
                    .Where(p => p.CanRead && (p.PropertyType.IsPrimitive ||
                                             p.PropertyType == typeof(string) ||
                                             p.PropertyType == typeof(DateTime) ||
                                             p.PropertyType == typeof(decimal) ||
                                             p.PropertyType.IsEnum))
                    .ToList();

                if (!properties.Any())
                {
                    MessageBox.Show("لا توجد خصائص قابلة للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                // إضافة الأعمدة
                foreach (var property in properties)
                {
                    dataTable.Columns.Add(property.Name, Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType);
                }

                // إضافة الصفوف
                foreach (var item in data)
                {
                    var row = dataTable.NewRow();
                    foreach (var property in properties)
                    {
                        row[property.Name] = property.GetValue(item) ?? DBNull.Value;
                    }
                    dataTable.Rows.Add(row);
                }

                // تصدير DataTable إلى Excel
                using (var writer = new StreamWriter(filePath, false, Encoding.UTF8))
                {
                    // كتابة رؤوس الأعمدة
                    writer.WriteLine(string.Join("\t", dataTable.Columns.Cast<DataColumn>().Select(c => c.ColumnName)));

                    // كتابة البيانات
                    foreach (DataRow row in dataTable.Rows)
                    {
                        var values = row.ItemArray.Select(value =>
                        {
                            if (value is DBNull)
                                return "";
                            else if (value is DateTime dateTime)
                                return dateTime.ToString("yyyy-MM-dd HH:mm:ss");
                            else if (value is decimal decimalValue)
                                return decimalValue.ToString("0.00");
                            else
                                return value.ToString().Replace("\t", " ");
                        });

                        writer.WriteLine(string.Join("\t", values));
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ReportExporter.ExportToExcel");
                return false;
            }
        }

        /// <summary>
        /// تصدير البيانات إلى ملف PDF
        /// </summary>
        public static bool ExportToPdf(FrameworkElement element, string filePath, string title = "")
        {
            try
            {
                // حفظ حالة العنصر الأصلية
                var originalWidth = element.Width;
                var originalHeight = element.Height;

                // تعيين الحجم للطباعة
                element.Width = 794; // A4 width in pixels at 96 DPI
                element.Height = 1123; // A4 height in pixels at 96 DPI
                element.Measure(new Size(element.Width, element.Height));
                element.Arrange(new Rect(0, 0, element.Width, element.Height));

                // إنشاء مستند للطباعة
                var document = new FixedDocument();
                var pageContent = new PageContent();
                var fixedPage = new FixedPage();
                fixedPage.Width = element.Width;
                fixedPage.Height = element.Height;

                // إضافة العنوان إذا كان موجوداً
                if (!string.IsNullOrEmpty(title))
                {
                    var titleTextBlock = new TextBlock
                    {
                        Text = title,
                        FontSize = 18,
                        FontWeight = FontWeights.Bold,
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 20, 0, 20)
                    };

                    FixedPage.SetTop(titleTextBlock, 20);
                    FixedPage.SetLeft(titleTextBlock, 0);
                    fixedPage.Children.Add(titleTextBlock);
                }

                // إضافة العنصر إلى الصفحة
                FixedPage.SetTop(element, title.Length > 0 ? 60 : 20);
                FixedPage.SetLeft(element, 0);
                fixedPage.Children.Add(element);

                ((IAddChild)pageContent).AddChild(fixedPage);
                document.Pages.Add(pageContent);

                // إنشاء ملف XPS
                var xpsFile = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName() + ".xps");
                using (var xpsDocument = new XpsDocument(xpsFile, FileAccess.ReadWrite))
                {
                    var xpsWriter = XpsDocument.CreateXpsDocumentWriter(xpsDocument);
                    xpsWriter.Write(document);
                }

                // تحويل XPS إلى PDF
                // في هذا المثال نستخدم طريقة بسيطة لإنشاء ملف PDF
                // في التطبيق الحقيقي يمكن استخدام مكتبة مثل PdfSharp أو iTextSharp

                // إنشاء ملف PDF بسيط يحتوي على صورة من العنصر
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    // إنشاء نص ترويسة PDF
                    var pdfHeader = "%PDF-1.4\n";
                    var headerBytes = Encoding.ASCII.GetBytes(pdfHeader);
                    stream.Write(headerBytes, 0, headerBytes.Length);

                    // إضافة محتوى XPS كملف مضمن
                    var xpsBytes = File.ReadAllBytes(xpsFile);
                    stream.Write(xpsBytes, 0, xpsBytes.Length);

                    // إضافة تذييل PDF
                    var pdfFooter = "\n%%EOF";
                    var footerBytes = Encoding.ASCII.GetBytes(pdfFooter);
                    stream.Write(footerBytes, 0, footerBytes.Length);
                }

                // استعادة حالة العنصر الأصلية
                element.Width = originalWidth;
                element.Height = originalHeight;

                // حذف ملف XPS المؤقت
                try { File.Delete(xpsFile); } catch { }

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ReportExporter.ExportToPdf");
                return false;
            }
        }

        /// <summary>
        /// عرض حوار حفظ الملف
        /// </summary>
        public static string ShowSaveFileDialog(string defaultFileName, string filter)
        {
            var saveFileDialog = new SaveFileDialog
            {
                FileName = defaultFileName,
                Filter = filter,
                AddExtension = true
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                return saveFileDialog.FileName;
            }

            return null;
        }
    }
}
