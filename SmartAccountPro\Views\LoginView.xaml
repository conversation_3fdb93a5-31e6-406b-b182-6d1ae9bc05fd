<UserControl x:Class="SmartAccountPro.Views.LoginView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SmartAccountPro.Views"
             xmlns:vm="clr-namespace:SmartAccountPro.ViewModels"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800"
             FlowDirection="RightToLeft">
    <UserControl.DataContext>
        <vm:LoginViewModel />
    </UserControl.DataContext>
    <Grid>
        <Border Width="400" Height="300"
                Background="White"
                BorderBrush="#CCCCCC"
                BorderThickness="1"
                CornerRadius="8"
                Padding="20">
            <StackPanel>
                <TextBlock Text="تسجيل الدخول"
                           FontSize="24"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,20"/>

                <TextBlock Text="اسم المستخدم:"
                           Margin="0,10,0,5"/>
                <Border x:Name="UsernameBorder"
                        BorderBrush="#CCCCCC"
                        BorderThickness="1"
                        CornerRadius="4">
                    <TextBox x:Name="UsernameTextBox"
                             Height="30"
                             Padding="5"
                             BorderThickness="0"
                             Text="{Binding Username, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"/>
                </Border>
                <TextBlock x:Name="UsernameErrorText"
                           Foreground="Red"
                           FontSize="11"
                           Margin="5,2,0,0"
                           Visibility="Collapsed"/>

                <TextBlock Text="كلمة المرور:"
                           Margin="0,10,0,5"/>
                <Border x:Name="PasswordBorder"
                        BorderBrush="#CCCCCC"
                        BorderThickness="1"
                        CornerRadius="4">
                    <PasswordBox x:Name="PasswordBox"
                                 Height="30"
                                 Padding="5"
                                 BorderThickness="0"/>
                </Border>
                <TextBlock x:Name="PasswordErrorText"
                           Foreground="Red"
                           FontSize="11"
                           Margin="5,2,0,0"
                           Visibility="Collapsed"/>

                <CheckBox x:Name="RememberMeCheckBox"
                          Content="تذكرني"
                          Margin="0,15,0,0"
                          IsChecked="{Binding RememberMe}"/>

                <Button x:Name="LoginButton"
                        Height="35"
                        Background="#2196F3"
                        Foreground="White"
                        FontWeight="Bold"
                        Margin="0,20,0,0"
                        Command="{Binding LoginCommand}"
                        CommandParameter="{Binding ElementName=PasswordBox, Path=Password}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE72E;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="تسجيل الدخول"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <TextBlock x:Name="ErrorMessageText"
                           Foreground="Red"
                           TextWrapping="Wrap"
                           Margin="0,10,0,0"
                           TextAlignment="Center"
                           Text="{Binding ErrorMessage}"
                           Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
