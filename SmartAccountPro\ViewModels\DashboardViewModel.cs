using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using LiveChartsCore.SkiaSharpView.VisualElements;
using SkiaSharp;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// نموذج عرض لوحة التحكم
    /// </summary>
    public class DashboardViewModel : ViewModelBase
    {
        private decimal _totalRevenue;
        private decimal _totalExpenses;
        private decimal _netProfit;
        private int _invoiceCount;
        private ISeries[] _revenueSeries;
        private ISeries[] _expensesSeries;
        private ISeries[] _profitDistributionSeries;
        private List<string> _monthLabels;
        private string _currentDate;
        private ObservableCollection<ISeries> _revenueExpenseSeries;
        private ObservableCollection<ISeries> _expenseDistributionSeries;
        private ObservableCollection<ISeries> _profitTrendSeries;
        private Axis[] _xAxes;
        private Axis[] _yAxes;

        /// <summary>
        /// إجمالي الإيرادات
        /// </summary>
        public decimal TotalRevenue
        {
            get => _totalRevenue;
            set => SetProperty(ref _totalRevenue, value);
        }

        /// <summary>
        /// إجمالي المصروفات
        /// </summary>
        public decimal TotalExpenses
        {
            get => _totalExpenses;
            set => SetProperty(ref _totalExpenses, value);
        }

        /// <summary>
        /// صافي الربح
        /// </summary>
        public decimal NetProfit
        {
            get => _netProfit;
            set => SetProperty(ref _netProfit, value);
        }

        /// <summary>
        /// عدد الفواتير
        /// </summary>
        public int InvoiceCount
        {
            get => _invoiceCount;
            set => SetProperty(ref _invoiceCount, value);
        }

        /// <summary>
        /// سلسلة بيانات الإيرادات
        /// </summary>
        public ISeries[] RevenueSeries
        {
            get => _revenueSeries;
            set => SetProperty(ref _revenueSeries, value);
        }

        /// <summary>
        /// سلسلة بيانات المصروفات
        /// </summary>
        public ISeries[] ExpensesSeries
        {
            get => _expensesSeries;
            set => SetProperty(ref _expensesSeries, value);
        }

        /// <summary>
        /// سلسلة بيانات توزيع الأرباح
        /// </summary>
        public ISeries[] ProfitDistributionSeries
        {
            get => _profitDistributionSeries;
            set => SetProperty(ref _profitDistributionSeries, value);
        }

        /// <summary>
        /// سلسلة بيانات الإيرادات والمصروفات للرسم البياني
        /// </summary>
        public ObservableCollection<ISeries> RevenueExpenseSeries
        {
            get => _revenueExpenseSeries;
            set => SetProperty(ref _revenueExpenseSeries, value);
        }

        /// <summary>
        /// سلسلة بيانات توزيع المصروفات للرسم البياني
        /// </summary>
        public ObservableCollection<ISeries> ExpenseDistributionSeries
        {
            get => _expenseDistributionSeries;
            set => SetProperty(ref _expenseDistributionSeries, value);
        }

        /// <summary>
        /// سلسلة بيانات اتجاه الأرباح للرسم البياني
        /// </summary>
        public ObservableCollection<ISeries> ProfitTrendSeries
        {
            get => _profitTrendSeries;
            set => SetProperty(ref _profitTrendSeries, value);
        }

        /// <summary>
        /// محاور X للرسم البياني
        /// </summary>
        public Axis[] XAxes
        {
            get => _xAxes;
            set => SetProperty(ref _xAxes, value);
        }

        /// <summary>
        /// محاور Y للرسم البياني
        /// </summary>
        public Axis[] YAxes
        {
            get => _yAxes;
            set => SetProperty(ref _yAxes, value);
        }

        /// <summary>
        /// تسميات الأشهر
        /// </summary>
        public List<string> MonthLabels
        {
            get => _monthLabels;
            set => SetProperty(ref _monthLabels, value);
        }

        /// <summary>
        /// التاريخ الحالي
        /// </summary>
        public string CurrentDate
        {
            get => _currentDate;
            set => SetProperty(ref _currentDate, value);
        }

        /// <summary>
        /// أمر إنشاء قيد محاسبي
        /// </summary>
        public ICommand CreateJournalEntryCommand { get; }

        /// <summary>
        /// أمر إنشاء فاتورة
        /// </summary>
        public ICommand CreateInvoiceCommand { get; }

        /// <summary>
        /// أمر عرض التقارير
        /// </summary>
        public ICommand ViewReportsCommand { get; }

        /// <summary>
        /// أمر إدارة الحسابات
        /// </summary>
        public ICommand ManageAccountsCommand { get; }

        public DashboardViewModel()
        {
            // تهيئة الخصائص
            _revenueSeries = Array.Empty<ISeries>();
            _expensesSeries = Array.Empty<ISeries>();
            _profitDistributionSeries = Array.Empty<ISeries>();
            _monthLabels = new List<string>();
            _currentDate = string.Empty;
            _revenueExpenseSeries = new ObservableCollection<ISeries>();
            _expenseDistributionSeries = new ObservableCollection<ISeries>();
            _profitTrendSeries = new ObservableCollection<ISeries>();
            _xAxes = Array.Empty<Axis>();
            _yAxes = Array.Empty<Axis>();

            // تهيئة الأوامر
            CreateJournalEntryCommand = new RelayCommand(CreateJournalEntry);
            CreateInvoiceCommand = new RelayCommand(CreateInvoice);
            ViewReportsCommand = new RelayCommand(ViewReports);
            ManageAccountsCommand = new RelayCommand(ManageAccounts);

            // تعيين التاريخ الحالي
            CurrentDate = DateTime.Now.ToString("yyyy/MM/dd");

            // تحميل البيانات
            LoadDashboardData();
        }

        /// <summary>
        /// تحميل بيانات لوحة التحكم
        /// </summary>
        private void LoadDashboardData()
        {
            try
            {
                // هنا يمكن إضافة كود لتحميل البيانات من قاعدة البيانات
                // مثال بسيط للتوضيح فقط
                TotalRevenue = 125000;
                TotalExpenses = 75000;
                NetProfit = TotalRevenue - TotalExpenses;
                InvoiceCount = 45;

                // تهيئة بيانات الرسم البياني للإيرادات والمصروفات
                MonthLabels = new List<string> { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو" };

                // تهيئة الرسومات البيانية
                InitializeCharts();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات لوحة التحكم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Helpers.ExceptionHandler.HandleException(ex, "DashboardViewModel.LoadDashboardData");
            }
        }

        /// <summary>
        /// تهيئة الرسومات البيانية
        /// </summary>
        private void InitializeCharts()
        {
            try
            {
                // تهيئة محاور الرسم البياني
                XAxes = new Axis[]
                {
                    new Axis
                    {
                        Labels = MonthLabels.ToArray(),
                        LabelsRotation = 0,
                        SeparatorsPaint = new SolidColorPaint(new SKColor(200, 200, 200)),
                        SeparatorsAtCenter = true,
                        TicksPaint = new SolidColorPaint(new SKColor(35, 35, 35)),
                        LabelsPaint = new SolidColorPaint(new SKColor(70, 70, 70))
                    }
                };

                YAxes = new Axis[]
                {
                    new Axis
                    {
                        Name = "المبلغ (ر.س)",
                        NamePaint = new SolidColorPaint(new SKColor(70, 70, 70)),
                        LabelsPaint = new SolidColorPaint(new SKColor(70, 70, 70)),
                        TicksPaint = new SolidColorPaint(new SKColor(35, 35, 35)),
                        SeparatorsPaint = new SolidColorPaint(new SKColor(200, 200, 200))
                    }
                };

                // تهيئة بيانات الإيرادات والمصروفات
                var revenueData = new double[] { 45000, 52000, 59000, 75000, 85000, 125000 };
                var expensesData = new double[] { 30000, 35000, 40000, 45000, 60000, 75000 };
                var profitData = new double[6];

                for (int i = 0; i < 6; i++)
                {
                    profitData[i] = revenueData[i] - expensesData[i];
                }

                // إنشاء سلسلة بيانات الإيرادات والمصروفات
                RevenueExpenseSeries = new ObservableCollection<ISeries>
                {
                    new LineSeries<double>
                    {
                        Name = "الإيرادات",
                        Values = revenueData,
                        Stroke = new SolidColorPaint(new SKColor(76, 175, 80), 3),
                        GeometryStroke = new SolidColorPaint(new SKColor(76, 175, 80), 3),
                        GeometryFill = new SolidColorPaint(SKColors.White),
                        GeometrySize = 10,
                        Fill = null
                    },
                    new LineSeries<double>
                    {
                        Name = "المصروفات",
                        Values = expensesData,
                        Stroke = new SolidColorPaint(new SKColor(244, 67, 54), 3),
                        GeometryStroke = new SolidColorPaint(new SKColor(244, 67, 54), 3),
                        GeometryFill = new SolidColorPaint(SKColors.White),
                        GeometrySize = 10,
                        Fill = null
                    },
                    new LineSeries<double>
                    {
                        Name = "صافي الربح",
                        Values = profitData,
                        Stroke = new SolidColorPaint(new SKColor(26, 115, 232), 3),
                        GeometryStroke = new SolidColorPaint(new SKColor(26, 115, 232), 3),
                        GeometryFill = new SolidColorPaint(SKColors.White),
                        GeometrySize = 10,
                        Fill = null
                    }
                };

                // إنشاء سلسلة بيانات توزيع المصروفات
                ExpenseDistributionSeries = new ObservableCollection<ISeries>
                {
                    new PieSeries<double>
                    {
                        Name = "الرواتب",
                        Values = new double[] { 30000 },
                        Fill = new SolidColorPaint(new SKColor(244, 67, 54))
                    },
                    new PieSeries<double>
                    {
                        Name = "الإيجار",
                        Values = new double[] { 15000 },
                        Fill = new SolidColorPaint(new SKColor(76, 175, 80))
                    },
                    new PieSeries<double>
                    {
                        Name = "المرافق",
                        Values = new double[] { 8000 },
                        Fill = new SolidColorPaint(new SKColor(255, 152, 0))
                    },
                    new PieSeries<double>
                    {
                        Name = "التسويق",
                        Values = new double[] { 12000 },
                        Fill = new SolidColorPaint(new SKColor(26, 115, 232))
                    },
                    new PieSeries<double>
                    {
                        Name = "أخرى",
                        Values = new double[] { 10000 },
                        Fill = new SolidColorPaint(new SKColor(156, 39, 176))
                    }
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة الرسومات البيانية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Helpers.ExceptionHandler.HandleException(ex, "DashboardViewModel.InitializeCharts");
            }
        }

        /// <summary>
        /// إنشاء قيد محاسبي
        /// </summary>
        private void CreateJournalEntry(object parameter)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة إنشاء قيد محاسبي جديد", "إنشاء قيد محاسبي", MessageBoxButton.OK, MessageBoxImage.Information);
                // هنا يمكن إضافة كود للانتقال إلى شاشة إنشاء قيد محاسبي
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء فاتورة
        /// </summary>
        private void CreateInvoice(object parameter)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة إنشاء فاتورة جديدة", "إنشاء فاتورة", MessageBoxButton.OK, MessageBoxImage.Information);
                // هنا يمكن إضافة كود للانتقال إلى شاشة إنشاء فاتورة
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض التقارير
        /// </summary>
        private void ViewReports(object parameter)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة التقارير", "عرض التقارير", MessageBoxButton.OK, MessageBoxImage.Information);
                // هنا يمكن إضافة كود للانتقال إلى شاشة التقارير
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إدارة الحسابات
        /// </summary>
        private void ManageAccounts(object parameter)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة إدارة الحسابات", "إدارة الحسابات", MessageBoxButton.OK, MessageBoxImage.Information);
                // هنا يمكن إضافة كود للانتقال إلى شاشة إدارة الحسابات
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
