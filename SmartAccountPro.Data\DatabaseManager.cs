using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;

namespace SmartAccountPro.Data
{
    /// <summary>
    /// مدير قاعدة البيانات
    /// </summary>
    public class DatabaseManager
    {
        private readonly AppDbContext _context;

        public DatabaseManager(AppDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// إنشاء قاعدة البيانات وتهيئتها
        /// </summary>
        public async Task InitializeDatabaseAsync()
        {
            // التأكد من إنشاء قاعدة البيانات
            await _context.Database.EnsureCreatedAsync();

            // إضافة البيانات الأولية إذا كانت قاعدة البيانات فارغة
            if (!_context.Users.Any())
            {
                await SeedDataAsync();
            }
        }

        /// <summary>
        /// إضافة البيانات الأولية
        /// </summary>
        private async Task SeedDataAsync()
        {
            // إضافة المستخدمين والأدوار
            await SeedUsersAndRolesAsync();

            // إضافة شجرة الحسابات
            await SeedAccountsAsync();
        }

        /// <summary>
        /// إضافة المستخدمين والأدوار
        /// </summary>
        private async Task SeedUsersAndRolesAsync()
        {
            // إضافة الأدوار
            var adminRole = new Role
            {
                Name = "مدير",
                Description = "مدير النظام"
            };

            var accountantRole = new Role
            {
                Name = "محاسب",
                Description = "محاسب"
            };

            var viewerRole = new Role
            {
                Name = "مشاهد",
                Description = "مشاهد فقط"
            };

            _context.Roles.AddRange(adminRole, accountantRole, viewerRole);
            await _context.SaveChangesAsync();

            // إضافة المستخدمين
            var adminUser = new User
            {
                Username = "admin",
                FullName = "مدير النظام",
                Email = "<EMAIL>",
                PasswordHash = HashPassword("admin"),
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            var accountantUser = new User
            {
                Username = "accountant",
                FullName = "محاسب النظام",
                Email = "<EMAIL>",
                PasswordHash = HashPassword("accountant"),
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            _context.Users.AddRange(adminUser, accountantUser);
            await _context.SaveChangesAsync();

            // إضافة علاقات المستخدمين والأدوار
            _context.UserRoles.Add(new UserRole { UserId = adminUser.Id, RoleId = adminRole.Id });
            _context.UserRoles.Add(new UserRole { UserId = accountantUser.Id, RoleId = accountantRole.Id });
            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// إضافة شجرة الحسابات
        /// </summary>
        private async Task SeedAccountsAsync()
        {
            // إضافة الحسابات الرئيسية
            var assets = new Account
            {
                Code = "1",
                Name = "الأصول",
                Type = AccountType.Asset,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            var liabilities = new Account
            {
                Code = "2",
                Name = "الخصوم",
                Type = AccountType.Liability,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            var equity = new Account
            {
                Code = "3",
                Name = "حقوق الملكية",
                Type = AccountType.Equity,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            var revenue = new Account
            {
                Code = "4",
                Name = "الإيرادات",
                Type = AccountType.Revenue,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            var expenses = new Account
            {
                Code = "5",
                Name = "المصروفات",
                Type = AccountType.Expense,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            _context.Accounts.AddRange(assets, liabilities, equity, revenue, expenses);
            await _context.SaveChangesAsync();

            // إضافة الحسابات الفرعية للأصول
            var cashAndBank = new Account
            {
                Code = "11",
                Name = "النقدية والبنوك",
                Type = AccountType.Asset,
                ParentAccountId = assets.Id,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            var receivables = new Account
            {
                Code = "12",
                Name = "المدينون",
                Type = AccountType.Asset,
                ParentAccountId = assets.Id,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            var inventory = new Account
            {
                Code = "13",
                Name = "المخزون",
                Type = AccountType.Asset,
                ParentAccountId = assets.Id,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            _context.Accounts.AddRange(cashAndBank, receivables, inventory);
            await _context.SaveChangesAsync();

            // إضافة الحسابات الفرعية للنقدية والبنوك
            var cash = new Account
            {
                Code = "111",
                Name = "الصندوق",
                Type = AccountType.Asset,
                ParentAccountId = cashAndBank.Id,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            var bank = new Account
            {
                Code = "112",
                Name = "البنك",
                Type = AccountType.Asset,
                ParentAccountId = cashAndBank.Id,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            _context.Accounts.AddRange(cash, bank);
            await _context.SaveChangesAsync();

            // إضافة الحسابات الفرعية للمدينون
            var customers = new Account
            {
                Code = "121",
                Name = "العملاء",
                Type = AccountType.Asset,
                ParentAccountId = receivables.Id,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            var notes = new Account
            {
                Code = "122",
                Name = "أوراق القبض",
                Type = AccountType.Asset,
                ParentAccountId = receivables.Id,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            _context.Accounts.AddRange(customers, notes);
            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        private string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
            }
        }

        /// <summary>
        /// الحصول على الحسابات مع التحميل الكسول
        /// </summary>
        public async Task<List<Account>> GetAccountsAsync(bool includeInactive = false)
        {
            try
            {
                // قياس وقت تنفيذ الاستعلام
                var stopwatch = new Stopwatch();
                stopwatch.Start();

                // إنشاء استعلام أساسي
                var query = _context.Accounts.AsNoTracking();

                // تطبيق الفلترة حسب حالة النشاط
                if (!includeInactive)
                {
                    query = query.Where(a => a.IsActive);
                }

                // تنفيذ الاستعلام مع تحميل البيانات المرتبطة بشكل كسول
                var accounts = await query
                    .Include(a => a.ParentAccount)
                    .OrderBy(a => a.Code)
                    .ToListAsync();

                stopwatch.Stop();
                Debug.WriteLine($"تم تحميل {accounts.Count} حساب في {stopwatch.ElapsedMilliseconds} مللي ثانية");

                return accounts;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تحميل الحسابات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على القيود المحاسبية مع التحميل الكسول
        /// </summary>
        public async Task<List<JournalEntry>> GetJournalEntriesAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                // قياس وقت تنفيذ الاستعلام
                var stopwatch = new Stopwatch();
                stopwatch.Start();

                // إنشاء استعلام أساسي
                var query = _context.JournalEntries.AsNoTracking();

                // تطبيق الفلترة حسب التاريخ
                if (fromDate.HasValue)
                {
                    query = query.Where(j => j.EntryDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(j => j.EntryDate <= toDate.Value);
                }

                // تنفيذ الاستعلام مع تحميل البيانات المرتبطة بشكل كسول
                var journalEntries = await query
                    .Include(j => j.Items)
                        .ThenInclude(i => i.Account)
                    .Include(j => j.CreatedByUser)
                    .OrderByDescending(j => j.EntryDate)
                    .ToListAsync();

                stopwatch.Stop();
                Debug.WriteLine($"تم تحميل {journalEntries.Count} قيد محاسبي في {stopwatch.ElapsedMilliseconds} مللي ثانية");

                return journalEntries ?? new List<JournalEntry>();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تحميل القيود المحاسبية: {ex.Message}");
                throw;
            }
        }
    }
}
