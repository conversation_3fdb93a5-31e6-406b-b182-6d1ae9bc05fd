using System;
using System.Globalization;
using System.Windows.Data;

namespace SmartAccountPro.Converters
{
    /// <summary>
    /// محول لتحويل القيم المنطقية إلى نصوص
    /// </summary>
    public class BooleanToStringConverter : IValueConverter
    {
        /// <summary>
        /// النص المقابل للقيمة true
        /// </summary>
        public string TrueValue { get; set; } = "نعم";

        /// <summary>
        /// النص المقابل للقيمة false
        /// </summary>
        public string FalseValue { get; set; } = "لا";

        /// <summary>
        /// تحويل القيمة المنطقية إلى نص
        /// </summary>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? TrueValue : FalseValue;
            }

            return FalseValue;
        }

        /// <summary>
        /// تحويل النص إلى قيمة منطقية
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return stringValue.Equals(TrueValue, StringComparison.OrdinalIgnoreCase);
            }

            return false;
        }
    }
}
