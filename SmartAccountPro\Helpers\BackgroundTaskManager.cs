using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة المهام الخلفية
    /// </summary>
    public static class BackgroundTaskManager
    {
        private static readonly ConcurrentDictionary<string, BackgroundTask> _tasks = new ConcurrentDictionary<string, BackgroundTask>();
        private static readonly ConcurrentDictionary<string, CancellationTokenSource> _cancellationTokens = new ConcurrentDictionary<string, CancellationTokenSource>();
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(5, 5); // الحد الأقصى للمهام المتزامنة

        /// <summary>
        /// إضافة مهمة خلفية
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <param name="taskName">اسم المهمة</param>
        /// <param name="action">الإجراء</param>
        /// <param name="priority">الأولوية</param>
        /// <returns>نجاح العملية</returns>
        public static bool AddTask(string taskId, string taskName, Func<CancellationToken, Task> action, TaskPriority priority = TaskPriority.Normal)
        {
            try
            {
                // التحقق من وجود المهمة
                if (_tasks.ContainsKey(taskId))
                {
                    Debug.WriteLine($"[TASK] المهمة '{taskId}' موجودة بالفعل.");
                    return false;
                }

                // إنشاء رمز الإلغاء
                var cancellationTokenSource = new CancellationTokenSource();
                _cancellationTokens[taskId] = cancellationTokenSource;

                // إنشاء المهمة
                var task = new BackgroundTask
                {
                    Id = taskId,
                    Name = taskName,
                    Status = TaskStatus.Created,
                    Priority = priority,
                    CreatedAt = DateTime.Now,
                    StartedAt = null,
                    CompletedAt = null,
                    Progress = 0,
                    Error = null
                };

                // إضافة المهمة إلى القاموس
                _tasks[taskId] = task;

                // بدء تنفيذ المهمة
                Task.Run(async () =>
                {
                    try
                    {
                        // تحديث حالة المهمة
                        task.Status = TaskStatus.Waiting;
                        task.StartedAt = DateTime.Now;
                        UpdateTask(task);

                        // انتظار السماح بتنفيذ المهمة
                        await _semaphore.WaitAsync(cancellationTokenSource.Token);

                        try
                        {
                            // تحديث حالة المهمة
                            task.Status = TaskStatus.Running;
                            UpdateTask(task);

                            // تنفيذ المهمة
                            await action(cancellationTokenSource.Token);

                            // تحديث حالة المهمة
                            task.Status = TaskStatus.Completed;
                            task.CompletedAt = DateTime.Now;
                            task.Progress = 100;
                            UpdateTask(task);

                            // إضافة إشعار
                            NotificationManager.Instance.AddNotification(
                                "تم إكمال المهمة",
                                $"تم إكمال المهمة '{taskName}' بنجاح.",
                                NotificationType.Success);
                        }
                        finally
                        {
                            // تحرير السماح بتنفيذ مهمة أخرى
                            _semaphore.Release();
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        // تحديث حالة المهمة
                        task.Status = TaskStatus.Canceled;
                        task.CompletedAt = DateTime.Now;
                        UpdateTask(task);

                        // إضافة إشعار
                        NotificationManager.Instance.AddNotification(
                            "تم إلغاء المهمة",
                            $"تم إلغاء المهمة '{taskName}'.",
                            NotificationType.Warning);
                    }
                    catch (Exception ex)
                    {
                        // تحديث حالة المهمة
                        task.Status = TaskStatus.Failed;
                        task.CompletedAt = DateTime.Now;
                        task.Error = ex.Message;
                        UpdateTask(task);

                        // إضافة إشعار
                        NotificationManager.Instance.AddNotification(
                            "فشلت المهمة",
                            $"فشلت المهمة '{taskName}': {ex.Message}",
                            NotificationType.Error);

                        // تسجيل الخطأ
                        ExceptionHandler.HandleException(ex, $"BackgroundTaskManager.AddTask.{taskId}");
                    }
                    finally
                    {
                        // إزالة رمز الإلغاء
                        _cancellationTokens.TryRemove(taskId, out _);
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackgroundTaskManager.AddTask");
                return false;
            }
        }

        /// <summary>
        /// إلغاء مهمة خلفية
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>نجاح العملية</returns>
        public static bool CancelTask(string taskId)
        {
            try
            {
                // التحقق من وجود المهمة
                if (!_tasks.TryGetValue(taskId, out var task))
                {
                    Debug.WriteLine($"[TASK] المهمة '{taskId}' غير موجودة.");
                    return false;
                }

                // التحقق من حالة المهمة
                if (task.Status == TaskStatus.Completed || task.Status == TaskStatus.Canceled || task.Status == TaskStatus.Failed)
                {
                    Debug.WriteLine($"[TASK] المهمة '{taskId}' مكتملة أو ملغاة أو فاشلة بالفعل.");
                    return false;
                }

                // التحقق من وجود رمز الإلغاء
                if (!_cancellationTokens.TryGetValue(taskId, out var cancellationTokenSource))
                {
                    Debug.WriteLine($"[TASK] رمز إلغاء المهمة '{taskId}' غير موجود.");
                    return false;
                }

                // إلغاء المهمة
                cancellationTokenSource.Cancel();

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackgroundTaskManager.CancelTask");
                return false;
            }
        }

        /// <summary>
        /// الحصول على مهمة خلفية
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>المهمة الخلفية</returns>
        public static BackgroundTask? GetTask(string taskId)
        {
            try
            {
                // التحقق من وجود المهمة
                if (!_tasks.TryGetValue(taskId, out var task))
                {
                    Debug.WriteLine($"[TASK] المهمة '{taskId}' غير موجودة.");
                    return null;
                }

                return task;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackgroundTaskManager.GetTask");
                return null;
            }
        }

        /// <summary>
        /// الحصول على قائمة المهام الخلفية
        /// </summary>
        /// <returns>قائمة المهام الخلفية</returns>
        public static List<BackgroundTask> GetTasks()
        {
            try
            {
                return _tasks.Values.ToList();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackgroundTaskManager.GetTasks");
                return new List<BackgroundTask>();
            }
        }

        /// <summary>
        /// تحديث تقدم مهمة خلفية
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <param name="progress">التقدم</param>
        /// <returns>نجاح العملية</returns>
        public static bool UpdateTaskProgress(string taskId, int progress)
        {
            try
            {
                // التحقق من وجود المهمة
                if (!_tasks.TryGetValue(taskId, out var task))
                {
                    Debug.WriteLine($"[TASK] المهمة '{taskId}' غير موجودة.");
                    return false;
                }

                // التحقق من حالة المهمة
                if (task.Status != TaskStatus.Running)
                {
                    Debug.WriteLine($"[TASK] المهمة '{taskId}' ليست قيد التنفيذ.");
                    return false;
                }

                // تحديث تقدم المهمة
                task.Progress = Math.Clamp(progress, 0, 100);
                UpdateTask(task);

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackgroundTaskManager.UpdateTaskProgress");
                return false;
            }
        }

        /// <summary>
        /// تحديث مهمة خلفية
        /// </summary>
        /// <param name="task">المهمة الخلفية</param>
        private static void UpdateTask(BackgroundTask task)
        {
            try
            {
                // تحديث المهمة في القاموس
                _tasks[task.Id] = task;

                // تسجيل حالة المهمة
                Debug.WriteLine($"[TASK] المهمة '{task.Id}' ({task.Name}): {task.Status}, التقدم: {task.Progress}%");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackgroundTaskManager.UpdateTask");
            }
        }

        /// <summary>
        /// إزالة مهمة خلفية
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>نجاح العملية</returns>
        public static bool RemoveTask(string taskId)
        {
            try
            {
                // التحقق من وجود المهمة
                if (!_tasks.TryGetValue(taskId, out var task))
                {
                    Debug.WriteLine($"[TASK] المهمة '{taskId}' غير موجودة.");
                    return false;
                }

                // التحقق من حالة المهمة
                if (task.Status == TaskStatus.Running || task.Status == TaskStatus.Waiting)
                {
                    Debug.WriteLine($"[TASK] المهمة '{taskId}' قيد التنفيذ أو الانتظار.");
                    return false;
                }

                // إزالة المهمة من القاموس
                return _tasks.TryRemove(taskId, out _);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackgroundTaskManager.RemoveTask");
                return false;
            }
        }

        /// <summary>
        /// إزالة جميع المهام المكتملة
        /// </summary>
        /// <returns>عدد المهام التي تمت إزالتها</returns>
        public static int RemoveCompletedTasks()
        {
            try
            {
                // البحث عن المهام المكتملة
                var completedTasks = _tasks.Where(kvp => kvp.Value.Status == TaskStatus.Completed || kvp.Value.Status == TaskStatus.Canceled || kvp.Value.Status == TaskStatus.Failed)
                                          .Select(kvp => kvp.Key)
                                          .ToList();

                // إزالة المهام المكتملة
                int count = 0;
                foreach (var taskId in completedTasks)
                {
                    if (_tasks.TryRemove(taskId, out _))
                    {
                        count++;
                    }
                }

                return count;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "BackgroundTaskManager.RemoveCompletedTasks");
                return 0;
            }
        }
    }

    /// <summary>
    /// مهمة خلفية
    /// </summary>
    public class BackgroundTask
    {
        /// <summary>
        /// معرف المهمة
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// اسم المهمة
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// حالة المهمة
        /// </summary>
        public TaskStatus Status { get; set; }

        /// <summary>
        /// أولوية المهمة
        /// </summary>
        public TaskPriority Priority { get; set; }

        /// <summary>
        /// وقت إنشاء المهمة
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// وقت بدء المهمة
        /// </summary>
        public DateTime? StartedAt { get; set; }

        /// <summary>
        /// وقت إكمال المهمة
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// تقدم المهمة
        /// </summary>
        public int Progress { get; set; }

        /// <summary>
        /// خطأ المهمة
        /// </summary>
        public string? Error { get; set; }
    }

    /// <summary>
    /// أولوية المهمة
    /// </summary>
    public enum TaskPriority
    {
        /// <summary>
        /// منخفضة
        /// </summary>
        Low,

        /// <summary>
        /// عادية
        /// </summary>
        Normal,

        /// <summary>
        /// عالية
        /// </summary>
        High,

        /// <summary>
        /// حرجة
        /// </summary>
        Critical
    }
}
