using System;
using System.Globalization;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لتنسيق البيانات المالية
    /// </summary>
    public static class FinancialFormatter
    {
        /// <summary>
        /// تنسيق المبلغ المالي
        /// </summary>
        /// <param name="amount">المبلغ المالي</param>
        /// <param name="currencySymbol">رمز العملة</param>
        /// <param name="decimalPlaces">عدد المنازل العشرية</param>
        /// <returns>المبلغ المالي المنسق</returns>
        public static string FormatCurrency(decimal amount, string currencySymbol = "", int decimalPlaces = 2)
        {
            try
            {
                // تنسيق المبلغ المالي
                string formattedAmount = amount.ToString($"N{decimalPlaces}", CultureInfo.CurrentCulture);

                // إضافة رمز العملة
                if (!string.IsNullOrEmpty(currencySymbol))
                {
                    formattedAmount = $"{formattedAmount} {currencySymbol}";
                }

                return formattedAmount;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialFormatter.FormatCurrency");
                return amount.ToString();
            }
        }

        /// <summary>
        /// تنسيق النسبة المئوية
        /// </summary>
        /// <param name="percentage">النسبة المئوية</param>
        /// <param name="decimalPlaces">عدد المنازل العشرية</param>
        /// <returns>النسبة المئوية المنسقة</returns>
        public static string FormatPercentage(decimal percentage, int decimalPlaces = 2)
        {
            try
            {
                // تنسيق النسبة المئوية
                return percentage.ToString($"N{decimalPlaces}", CultureInfo.CurrentCulture) + "%";
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialFormatter.FormatPercentage");
                return percentage.ToString() + "%";
            }
        }

        /// <summary>
        /// تنسيق الرقم
        /// </summary>
        /// <param name="number">الرقم</param>
        /// <param name="decimalPlaces">عدد المنازل العشرية</param>
        /// <returns>الرقم المنسق</returns>
        public static string FormatNumber(decimal number, int decimalPlaces = 2)
        {
            try
            {
                // تنسيق الرقم
                return number.ToString($"N{decimalPlaces}", CultureInfo.CurrentCulture);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialFormatter.FormatNumber");
                return number.ToString();
            }
        }

        /// <summary>
        /// تنسيق رقم الحساب المصرفي
        /// </summary>
        /// <param name="accountNumber">رقم الحساب المصرفي</param>
        /// <returns>رقم الحساب المصرفي المنسق</returns>
        public static string FormatBankAccountNumber(string accountNumber)
        {
            try
            {
                // إزالة المسافات
                accountNumber = accountNumber.Replace(" ", "");

                // تنسيق رقم الحساب المصرفي
                if (accountNumber.Length > 4)
                {
                    string formattedNumber = "";
                    for (int i = 0; i < accountNumber.Length; i++)
                    {
                        formattedNumber += accountNumber[i];
                        if ((i + 1) % 4 == 0 && i < accountNumber.Length - 1)
                        {
                            formattedNumber += " ";
                        }
                    }
                    return formattedNumber;
                }
                else
                {
                    return accountNumber;
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialFormatter.FormatBankAccountNumber");
                return accountNumber;
            }
        }

        /// <summary>
        /// تنسيق رقم IBAN
        /// </summary>
        /// <param name="iban">رقم IBAN</param>
        /// <returns>رقم IBAN المنسق</returns>
        public static string FormatIBAN(string iban)
        {
            try
            {
                // إزالة المسافات
                iban = iban.Replace(" ", "");

                // تنسيق رقم IBAN
                if (iban.Length > 4)
                {
                    string formattedIBAN = "";
                    for (int i = 0; i < iban.Length; i++)
                    {
                        formattedIBAN += iban[i];
                        if ((i + 1) % 4 == 0 && i < iban.Length - 1)
                        {
                            formattedIBAN += " ";
                        }
                    }
                    return formattedIBAN;
                }
                else
                {
                    return iban;
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialFormatter.FormatIBAN");
                return iban;
            }
        }

        /// <summary>
        /// تنسيق رقم بطاقة الائتمان
        /// </summary>
        /// <param name="creditCardNumber">رقم بطاقة الائتمان</param>
        /// <returns>رقم بطاقة الائتمان المنسق</returns>
        public static string FormatCreditCardNumber(string creditCardNumber)
        {
            try
            {
                // إزالة المسافات والشرطات
                creditCardNumber = creditCardNumber.Replace(" ", "").Replace("-", "");

                // تنسيق رقم بطاقة الائتمان
                if (creditCardNumber.Length > 4)
                {
                    string formattedNumber = "";
                    for (int i = 0; i < creditCardNumber.Length; i++)
                    {
                        formattedNumber += creditCardNumber[i];
                        if ((i + 1) % 4 == 0 && i < creditCardNumber.Length - 1)
                        {
                            formattedNumber += " ";
                        }
                    }
                    return formattedNumber;
                }
                else
                {
                    return creditCardNumber;
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialFormatter.FormatCreditCardNumber");
                return creditCardNumber;
            }
        }

        /// <summary>
        /// تنسيق رقم الفاتورة
        /// </summary>
        /// <param name="invoiceNumber">رقم الفاتورة</param>
        /// <param name="prefix">بادئة</param>
        /// <param name="padLength">طول التعبئة</param>
        /// <returns>رقم الفاتورة المنسق</returns>
        public static string FormatInvoiceNumber(string invoiceNumber, string prefix = "INV-", int padLength = 6)
        {
            try
            {
                // التحقق من أن رقم الفاتورة رقمي
                if (int.TryParse(invoiceNumber, out int number))
                {
                    // تنسيق رقم الفاتورة
                    return $"{prefix}{number.ToString().PadLeft(padLength, '0')}";
                }
                else
                {
                    // إذا كان رقم الفاتورة ليس رقمياً، إرجاع القيمة كما هي
                    return invoiceNumber;
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialFormatter.FormatInvoiceNumber");
                return invoiceNumber;
            }
        }

        /// <summary>
        /// تنسيق تاريخ الفاتورة
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <param name="format">تنسيق التاريخ</param>
        /// <returns>التاريخ المنسق</returns>
        public static string FormatDate(DateTime date, string format = "yyyy/MM/dd")
        {
            try
            {
                // تنسيق التاريخ
                return date.ToString(format, CultureInfo.CurrentCulture);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialFormatter.FormatDate");
                return date.ToString();
            }
        }

        /// <summary>
        /// تنسيق رقم الضريبة
        /// </summary>
        /// <param name="taxNumber">رقم الضريبة</param>
        /// <returns>رقم الضريبة المنسق</returns>
        public static string FormatTaxNumber(string taxNumber)
        {
            try
            {
                // إزالة المسافات
                taxNumber = taxNumber.Replace(" ", "");

                // تنسيق رقم الضريبة
                if (taxNumber.Length > 3)
                {
                    string formattedNumber = "";
                    for (int i = 0; i < taxNumber.Length; i++)
                    {
                        formattedNumber += taxNumber[i];
                        if ((i + 1) % 3 == 0 && i < taxNumber.Length - 1)
                        {
                            formattedNumber += " ";
                        }
                    }
                    return formattedNumber;
                }
                else
                {
                    return taxNumber;
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialFormatter.FormatTaxNumber");
                return taxNumber;
            }
        }
    }
}
