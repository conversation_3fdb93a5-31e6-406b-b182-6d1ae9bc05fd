using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للاتصال بالخدمات الخارجية
    /// </summary>
    public class ApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly Dictionary<string, string> _defaultHeaders;
        private string? _authToken;
        private readonly CacheManager<string, ApiResponse> _cache;

        /// <summary>
        /// إنشاء عميل API جديد
        /// </summary>
        /// <param name="baseUrl">عنوان URL الأساسي</param>
        public ApiClient(string baseUrl)
        {
            _baseUrl = baseUrl.TrimEnd('/');
            _httpClient = new HttpClient();
            _defaultHeaders = new Dictionary<string, string>();
            _cache = new CacheManager<string, ApiResponse>("ApiClient", TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(10));
        }

        /// <summary>
        /// تعيين رمز المصادقة
        /// </summary>
        /// <param name="token">رمز المصادقة</param>
        /// <param name="scheme">نوع المصادقة</param>
        public void SetAuthToken(string token, string scheme = "Bearer")
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("ApiClient.SetAuthToken", () =>
                {
                    _authToken = token;
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(scheme, token);
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ApiClient.SetAuthToken");
            }
        }

        /// <summary>
        /// إزالة رمز المصادقة
        /// </summary>
        public void RemoveAuthToken()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("ApiClient.RemoveAuthToken", () =>
                {
                    _authToken = null;
                    _httpClient.DefaultRequestHeaders.Authorization = null;
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ApiClient.RemoveAuthToken");
            }
        }

        /// <summary>
        /// إضافة رأس افتراضي
        /// </summary>
        /// <param name="name">اسم الرأس</param>
        /// <param name="value">قيمة الرأس</param>
        public void AddDefaultHeader(string name, string value)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("ApiClient.AddDefaultHeader", () =>
                {
                    _defaultHeaders[name] = value;
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ApiClient.AddDefaultHeader");
            }
        }

        /// <summary>
        /// إزالة رأس افتراضي
        /// </summary>
        /// <param name="name">اسم الرأس</param>
        public void RemoveDefaultHeader(string name)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("ApiClient.RemoveDefaultHeader", () =>
                {
                    _defaultHeaders.Remove(name);
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ApiClient.RemoveDefaultHeader");
            }
        }

        /// <summary>
        /// إرسال طلب GET
        /// </summary>
        /// <typeparam name="T">نوع البيانات المتوقعة</typeparam>
        /// <param name="endpoint">نقطة النهاية</param>
        /// <param name="parameters">معلمات الطلب</param>
        /// <param name="headers">رؤوس الطلب</param>
        /// <param name="useCache">استخدام ذاكرة التخزين المؤقت</param>
        /// <returns>استجابة API</returns>
        public async Task<ApiResponse<T>> GetAsync<T>(string endpoint, Dictionary<string, string>? parameters = null, Dictionary<string, string>? headers = null, bool useCache = false)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ApiClient.GetAsync", async () =>
                {
                    // بناء عنوان URL
                    string url = BuildUrl(endpoint, parameters);

                    // التحقق من وجود البيانات في ذاكرة التخزين المؤقت
                    if (useCache && _cache.TryGetValue(url, out var cachedResponse))
                    {
                        return (ApiResponse<T>)cachedResponse;
                    }

                    // إنشاء طلب HTTP
                    var request = new HttpRequestMessage(HttpMethod.Get, url);

                    // إضافة الرؤوس
                    AddHeaders(request, headers);

                    // إرسال الطلب
                    var response = await _httpClient.SendAsync(request);

                    // معالجة الاستجابة
                    var apiResponse = await ProcessResponseAsync<T>(response);

                    // تخزين الاستجابة في ذاكرة التخزين المؤقت
                    if (useCache && apiResponse.IsSuccess)
                    {
                        _cache.Add(url, apiResponse);
                    }

                    return apiResponse;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ApiClient.GetAsync");
                return new ApiResponse<T>
                {
                    IsSuccess = false,
                    StatusCode = 0,
                    Message = $"حدث خطأ أثناء إرسال الطلب: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// إرسال طلب POST
        /// </summary>
        /// <typeparam name="T">نوع البيانات المتوقعة</typeparam>
        /// <param name="endpoint">نقطة النهاية</param>
        /// <param name="data">البيانات</param>
        /// <param name="headers">رؤوس الطلب</param>
        /// <returns>استجابة API</returns>
        public async Task<ApiResponse<T>> PostAsync<T>(string endpoint, object data, Dictionary<string, string>? headers = null)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ApiClient.PostAsync", async () =>
                {
                    // بناء عنوان URL
                    string url = BuildUrl(endpoint);

                    // تحويل البيانات إلى JSON
                    string json = JsonConvert.SerializeObject(data);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    // إنشاء طلب HTTP
                    var request = new HttpRequestMessage(HttpMethod.Post, url)
                    {
                        Content = content
                    };

                    // إضافة الرؤوس
                    AddHeaders(request, headers);

                    // إرسال الطلب
                    var response = await _httpClient.SendAsync(request);

                    // معالجة الاستجابة
                    return await ProcessResponseAsync<T>(response);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ApiClient.PostAsync");
                return new ApiResponse<T>
                {
                    IsSuccess = false,
                    StatusCode = 0,
                    Message = $"حدث خطأ أثناء إرسال الطلب: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// إرسال طلب PUT
        /// </summary>
        /// <typeparam name="T">نوع البيانات المتوقعة</typeparam>
        /// <param name="endpoint">نقطة النهاية</param>
        /// <param name="data">البيانات</param>
        /// <param name="headers">رؤوس الطلب</param>
        /// <returns>استجابة API</returns>
        public async Task<ApiResponse<T>> PutAsync<T>(string endpoint, object data, Dictionary<string, string>? headers = null)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ApiClient.PutAsync", async () =>
                {
                    // بناء عنوان URL
                    string url = BuildUrl(endpoint);

                    // تحويل البيانات إلى JSON
                    string json = JsonConvert.SerializeObject(data);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    // إنشاء طلب HTTP
                    var request = new HttpRequestMessage(HttpMethod.Put, url)
                    {
                        Content = content
                    };

                    // إضافة الرؤوس
                    AddHeaders(request, headers);

                    // إرسال الطلب
                    var response = await _httpClient.SendAsync(request);

                    // معالجة الاستجابة
                    return await ProcessResponseAsync<T>(response);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ApiClient.PutAsync");
                return new ApiResponse<T>
                {
                    IsSuccess = false,
                    StatusCode = 0,
                    Message = $"حدث خطأ أثناء إرسال الطلب: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// إرسال طلب DELETE
        /// </summary>
        /// <typeparam name="T">نوع البيانات المتوقعة</typeparam>
        /// <param name="endpoint">نقطة النهاية</param>
        /// <param name="headers">رؤوس الطلب</param>
        /// <returns>استجابة API</returns>
        public async Task<ApiResponse<T>> DeleteAsync<T>(string endpoint, Dictionary<string, string>? headers = null)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ApiClient.DeleteAsync", async () =>
                {
                    // بناء عنوان URL
                    string url = BuildUrl(endpoint);

                    // إنشاء طلب HTTP
                    var request = new HttpRequestMessage(HttpMethod.Delete, url);

                    // إضافة الرؤوس
                    AddHeaders(request, headers);

                    // إرسال الطلب
                    var response = await _httpClient.SendAsync(request);

                    // معالجة الاستجابة
                    return await ProcessResponseAsync<T>(response);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ApiClient.DeleteAsync");
                return new ApiResponse<T>
                {
                    IsSuccess = false,
                    StatusCode = 0,
                    Message = $"حدث خطأ أثناء إرسال الطلب: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// بناء عنوان URL
        /// </summary>
        /// <param name="endpoint">نقطة النهاية</param>
        /// <param name="parameters">معلمات الطلب</param>
        /// <returns>عنوان URL</returns>
        private string BuildUrl(string endpoint, Dictionary<string, string>? parameters = null)
        {
            try
            {
                // إزالة الشرطة المائلة من بداية نقطة النهاية
                endpoint = endpoint.TrimStart('/');

                // بناء عنوان URL الأساسي
                var url = $"{_baseUrl}/{endpoint}";

                // إضافة معلمات الطلب
                if (parameters != null && parameters.Count > 0)
                {
                    var queryString = string.Join("&", parameters.Select(p => $"{Uri.EscapeDataString(p.Key)}={Uri.EscapeDataString(p.Value)}"));
                    url = $"{url}?{queryString}";
                }

                return url;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ApiClient.BuildUrl");
                return $"{_baseUrl}/{endpoint}";
            }
        }

        /// <summary>
        /// إضافة رؤوس الطلب
        /// </summary>
        /// <param name="request">طلب HTTP</param>
        /// <param name="headers">رؤوس الطلب</param>
        private void AddHeaders(HttpRequestMessage request, Dictionary<string, string>? headers = null)
        {
            try
            {
                // إضافة الرؤوس الافتراضية
                foreach (var header in _defaultHeaders)
                {
                    request.Headers.Add(header.Key, header.Value);
                }

                // إضافة الرؤوس المخصصة
                if (headers != null)
                {
                    foreach (var header in headers)
                    {
                        request.Headers.Add(header.Key, header.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ApiClient.AddHeaders");
            }
        }

        /// <summary>
        /// معالجة استجابة HTTP
        /// </summary>
        /// <typeparam name="T">نوع البيانات المتوقعة</typeparam>
        /// <param name="response">استجابة HTTP</param>
        /// <returns>استجابة API</returns>
        private async Task<ApiResponse<T>> ProcessResponseAsync<T>(HttpResponseMessage response)
        {
            try
            {
                // قراءة محتوى الاستجابة
                var content = await response.Content.ReadAsStringAsync();

                // إنشاء استجابة API
                var apiResponse = new ApiResponse<T>
                {
                    IsSuccess = response.IsSuccessStatusCode,
                    StatusCode = (int)response.StatusCode,
                    Headers = response.Headers.ToDictionary(h => h.Key, h => string.Join(", ", h.Value))
                };

                // معالجة الاستجابة الناجحة
                if (response.IsSuccessStatusCode)
                {
                    // التحقق من نوع البيانات المتوقعة
                    if (typeof(T) == typeof(string))
                    {
                        apiResponse.Data = (T)(object)content;
                    }
                    else
                    {
                        // تحويل البيانات من JSON
                        apiResponse.Data = JsonConvert.DeserializeObject<T>(content);
                    }

                    apiResponse.Message = "تم تنفيذ الطلب بنجاح.";
                }
                else
                {
                    // معالجة الاستجابة الفاشلة
                    apiResponse.Message = $"فشل الطلب: {response.StatusCode} - {response.ReasonPhrase}";

                    // محاولة استخراج رسالة الخطأ من الاستجابة
                    try
                    {
                        var errorResponse = JsonConvert.DeserializeObject<ErrorResponse>(content);
                        if (errorResponse != null && !string.IsNullOrEmpty(errorResponse.Message))
                        {
                            apiResponse.Message = errorResponse.Message;
                        }
                    }
                    catch
                    {
                        // تجاهل أي أخطاء أثناء معالجة رسالة الخطأ
                    }
                }

                return apiResponse;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ApiClient.ProcessResponseAsync");
                return new ApiResponse<T>
                {
                    IsSuccess = false,
                    StatusCode = 0,
                    Message = $"حدث خطأ أثناء معالجة الاستجابة: {ex.Message}"
                };
            }
        }
    }

    /// <summary>
    /// استجابة API
    /// </summary>
    public class ApiResponse
    {
        /// <summary>
        /// هل الطلب ناجح
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// رمز الحالة
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// رسالة الاستجابة
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// رؤوس الاستجابة
        /// </summary>
        public Dictionary<string, string> Headers { get; set; } = new Dictionary<string, string>();
    }

    /// <summary>
    /// استجابة API مع بيانات
    /// </summary>
    /// <typeparam name="T">نوع البيانات</typeparam>
    public class ApiResponse<T> : ApiResponse
    {
        /// <summary>
        /// بيانات الاستجابة
        /// </summary>
        public T? Data { get; set; }
    }

    /// <summary>
    /// استجابة خطأ
    /// </summary>
    public class ErrorResponse
    {
        /// <summary>
        /// رسالة الخطأ
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// تفاصيل الخطأ
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// رمز الخطأ
        /// </summary>
        public string? Code { get; set; }
    }
}
