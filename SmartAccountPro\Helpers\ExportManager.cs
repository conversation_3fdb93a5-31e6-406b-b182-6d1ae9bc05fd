using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Xps;
using System.Windows.Xps.Packaging;
using Newtonsoft.Json;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لتصدير البيانات بتنسيقات مختلفة
    /// </summary>
    public class ExportManager
    {
        private readonly string _exportsPath;

        /// <summary>
        /// إنشاء مدير التصدير
        /// </summary>
        public ExportManager()
        {
            // تحديد مسار ملفات التصدير
            _exportsPath = Path.Combine(App.AppDataPath, "Exports");

            // إنشاء مجلد التصدير إذا لم يكن موجوداً
            if (!Directory.Exists(_exportsPath))
            {
                Directory.CreateDirectory(_exportsPath);
            }

            // تعيين ترخيص EPPlus
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        /// <summary>
        /// تصدير بيانات إلى ملف Excel
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="fileName">اسم الملف</param>
        /// <param name="sheetName">اسم ورقة العمل</param>
        /// <param name="headers">عناوين الأعمدة</param>
        /// <param name="properties">خصائص البيانات</param>
        /// <returns>مسار الملف</returns>
        public async Task<string> ExportToExcelAsync<T>(IEnumerable<T> data, string fileName, string sheetName, List<string>? headers = null, List<string>? properties = null)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ExportManager.ExportToExcel", async () =>
                {
                    // تحديد مسار الملف
                    string filePath = Path.Combine(_exportsPath, $"{fileName}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");

                    // إنشاء حزمة Excel
                    using (var package = new ExcelPackage())
                    {
                        // إضافة ورقة عمل
                        var worksheet = package.Workbook.Worksheets.Add(sheetName);

                        // تحديد خصائص البيانات
                        if (properties == null)
                        {
                            // استخدام جميع الخصائص العامة
                            properties = typeof(T).GetProperties()
                                .Where(p => p.CanRead && !p.GetGetMethod()!.IsStatic)
                                .Select(p => p.Name)
                                .ToList();
                        }

                        // تحديد عناوين الأعمدة
                        if (headers == null)
                        {
                            // استخدام أسماء الخصائص كعناوين
                            headers = properties;
                        }

                        // التحقق من تطابق عدد العناوين والخصائص
                        if (headers.Count != properties.Count)
                        {
                            throw new ArgumentException("عدد العناوين يجب أن يساوي عدد الخصائص.");
                        }

                        // كتابة عناوين الأعمدة
                        for (int i = 0; i < headers.Count; i++)
                        {
                            worksheet.Cells[1, i + 1].Value = headers[i];
                        }

                        // تنسيق عناوين الأعمدة
                        using (var range = worksheet.Cells[1, 1, 1, headers.Count])
                        {
                            range.Style.Font.Bold = true;
                            range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                            range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                            range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                            range.Style.Border.Bottom.Color.SetColor(System.Drawing.Color.Black);
                            range.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        }

                        // كتابة البيانات
                        int row = 2;
                        foreach (var item in data)
                        {
                            for (int i = 0; i < properties.Count; i++)
                            {
                                // الحصول على قيمة الخاصية
                                var property = typeof(T).GetProperty(properties[i]);
                                if (property != null)
                                {
                                    var value = property.GetValue(item);
                                    worksheet.Cells[row, i + 1].Value = value;

                                    // تنسيق القيمة حسب نوعها
                                    if (value is DateTime)
                                    {
                                        worksheet.Cells[row, i + 1].Style.Numberformat.Format = "yyyy-MM-dd HH:mm:ss";
                                    }
                                    else if (value is decimal || value is double || value is float)
                                    {
                                        worksheet.Cells[row, i + 1].Style.Numberformat.Format = "#,##0.00";
                                    }
                                }
                            }
                            row++;
                        }

                        // تنسيق الجدول
                        using (var range = worksheet.Cells[1, 1, row - 1, headers.Count])
                        {
                            range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                            range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                            range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                            range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        }

                        // تعديل عرض الأعمدة
                        worksheet.Cells.AutoFitColumns();

                        // حفظ الملف
                        await package.SaveAsAsync(new FileInfo(filePath));
                    }

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم تصدير البيانات",
                        $"تم تصدير البيانات إلى ملف Excel بنجاح: {Path.GetFileName(filePath)}",
                        NotificationType.Success);

                    Debug.WriteLine($"[EXPORT] تم تصدير البيانات إلى ملف Excel: {filePath}");
                    return filePath;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ExportManager.ExportToExcel");
                throw;
            }
        }

        /// <summary>
        /// تصدير بيانات إلى ملف CSV
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="fileName">اسم الملف</param>
        /// <param name="headers">عناوين الأعمدة</param>
        /// <param name="properties">خصائص البيانات</param>
        /// <param name="delimiter">فاصل الحقول</param>
        /// <returns>مسار الملف</returns>
        public async Task<string> ExportToCsvAsync<T>(IEnumerable<T> data, string fileName, List<string>? headers = null, List<string>? properties = null, char delimiter = ',')
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ExportManager.ExportToCsv", async () =>
                {
                    // تحديد مسار الملف
                    string filePath = Path.Combine(_exportsPath, $"{fileName}_{DateTime.Now:yyyyMMdd_HHmmss}.csv");

                    // تحديد خصائص البيانات
                    if (properties == null)
                    {
                        // استخدام جميع الخصائص العامة
                        properties = typeof(T).GetProperties()
                            .Where(p => p.CanRead && !p.GetGetMethod()!.IsStatic)
                            .Select(p => p.Name)
                            .ToList();
                    }

                    // تحديد عناوين الأعمدة
                    if (headers == null)
                    {
                        // استخدام أسماء الخصائص كعناوين
                        headers = properties;
                    }

                    // التحقق من تطابق عدد العناوين والخصائص
                    if (headers.Count != properties.Count)
                    {
                        throw new ArgumentException("عدد العناوين يجب أن يساوي عدد الخصائص.");
                    }

                    // إنشاء محتوى الملف
                    var sb = new StringBuilder();

                    // كتابة عناوين الأعمدة
                    sb.AppendLine(string.Join(delimiter, headers.Select(h => EscapeCsvField(h, delimiter))));

                    // كتابة البيانات
                    foreach (var item in data)
                    {
                        var values = new List<string>();
                        for (int i = 0; i < properties.Count; i++)
                        {
                            // الحصول على قيمة الخاصية
                            var property = typeof(T).GetProperty(properties[i]);
                            if (property != null)
                            {
                                var value = property.GetValue(item);
                                values.Add(EscapeCsvField(value?.ToString() ?? "", delimiter));
                            }
                            else
                            {
                                values.Add("");
                            }
                        }
                        sb.AppendLine(string.Join(delimiter, values));
                    }

                    // حفظ الملف
                    await File.WriteAllTextAsync(filePath, sb.ToString(), Encoding.UTF8);

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم تصدير البيانات",
                        $"تم تصدير البيانات إلى ملف CSV بنجاح: {Path.GetFileName(filePath)}",
                        NotificationType.Success);

                    Debug.WriteLine($"[EXPORT] تم تصدير البيانات إلى ملف CSV: {filePath}");
                    return filePath;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ExportManager.ExportToCsv");
                throw;
            }
        }

        /// <summary>
        /// تصدير بيانات إلى ملف JSON
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="fileName">اسم الملف</param>
        /// <returns>مسار الملف</returns>
        public async Task<string> ExportToJsonAsync<T>(IEnumerable<T> data, string fileName)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ExportManager.ExportToJson", async () =>
                {
                    // تحديد مسار الملف
                    string filePath = Path.Combine(_exportsPath, $"{fileName}_{DateTime.Now:yyyyMMdd_HHmmss}.json");

                    // تحويل البيانات إلى JSON
                    string json = JsonConvert.SerializeObject(data, Formatting.Indented);

                    // حفظ الملف
                    await File.WriteAllTextAsync(filePath, json, Encoding.UTF8);

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم تصدير البيانات",
                        $"تم تصدير البيانات إلى ملف JSON بنجاح: {Path.GetFileName(filePath)}",
                        NotificationType.Success);

                    Debug.WriteLine($"[EXPORT] تم تصدير البيانات إلى ملف JSON: {filePath}");
                    return filePath;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ExportManager.ExportToJson");
                throw;
            }
        }

        /// <summary>
        /// تصدير مستند إلى ملف PDF
        /// </summary>
        /// <param name="document">المستند</param>
        /// <param name="fileName">اسم الملف</param>
        /// <returns>مسار الملف</returns>
        public async Task<string> ExportToPdfAsync(FlowDocument document, string fileName)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ExportManager.ExportToPdf", async () =>
                {
                    // تحديد مسار الملف
                    string filePath = Path.Combine(_exportsPath, $"{fileName}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf");

                    // تعيين إعدادات المستند
                    document.PageHeight = 1056; // A4 height in pixels at 96 DPI
                    document.PageWidth = 816; // A4 width in pixels at 96 DPI
                    document.PagePadding = new Thickness(50);
                    document.ColumnGap = 0;
                    document.ColumnWidth = 716; // Page width - padding

                    // إنشاء مستند XPS
                    var xpsDocument = new XpsDocument(filePath, FileAccess.ReadWrite);
                    var xpsWriter = XpsDocument.CreateXpsDocumentWriter(xpsDocument);
                    var paginator = ((IDocumentPaginatorSource)document).DocumentPaginator;
                    xpsWriter.Write(paginator);
                    xpsDocument.Close();

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم تصدير المستند",
                        $"تم تصدير المستند إلى ملف PDF بنجاح: {Path.GetFileName(filePath)}",
                        NotificationType.Success);

                    Debug.WriteLine($"[EXPORT] تم تصدير المستند إلى ملف PDF: {filePath}");
                    return filePath;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ExportManager.ExportToPdf");
                throw;
            }
        }

        /// <summary>
        /// تصدير عنصر واجهة المستخدم إلى ملف صورة
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="fileName">اسم الملف</param>
        /// <param name="format">تنسيق الصورة</param>
        /// <returns>مسار الملف</returns>
        public async Task<string> ExportToImageAsync(FrameworkElement element, string fileName, ImageFormat format = ImageFormat.PNG)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ExportManager.ExportToImage", async () =>
                {
                    // تحديد امتداد الملف
                    string extension = format.ToString().ToLower();
                    
                    // تحديد مسار الملف
                    string filePath = Path.Combine(_exportsPath, $"{fileName}_{DateTime.Now:yyyyMMdd_HHmmss}.{extension}");

                    // حفظ حالة العنصر
                    var originalWidth = element.Width;
                    var originalHeight = element.Height;

                    // تعيين حجم العنصر
                    element.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                    var size = element.DesiredSize;
                    element.Arrange(new Rect(0, 0, size.Width, size.Height));

                    // إنشاء صورة
                    var renderTargetBitmap = new RenderTargetBitmap(
                        (int)size.Width, (int)size.Height, 96, 96, PixelFormats.Pbgra32);
                    renderTargetBitmap.Render(element);

                    // حفظ الصورة
                    BitmapEncoder encoder;

                    switch (format)
                    {
                        case ImageFormat.PNG:
                            encoder = new PngBitmapEncoder();
                            break;
                        case ImageFormat.JPEG:
                            encoder = new JpegBitmapEncoder();
                            break;
                        case ImageFormat.BMP:
                            encoder = new BmpBitmapEncoder();
                            break;
                        case ImageFormat.GIF:
                            encoder = new GifBitmapEncoder();
                            break;
                        case ImageFormat.TIFF:
                            encoder = new TiffBitmapEncoder();
                            break;
                        default:
                            encoder = new PngBitmapEncoder();
                            break;
                    }

                    encoder.Frames.Add(BitmapFrame.Create(renderTargetBitmap));

                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        encoder.Save(fileStream);
                    }

                    // استعادة حالة العنصر
                    element.Width = originalWidth;
                    element.Height = originalHeight;
                    element.Measure(new Size(originalWidth, originalHeight));
                    element.Arrange(new Rect(0, 0, originalWidth, originalHeight));

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم تصدير العنصر",
                        $"تم تصدير العنصر إلى ملف صورة بنجاح: {Path.GetFileName(filePath)}",
                        NotificationType.Success);

                    Debug.WriteLine($"[EXPORT] تم تصدير العنصر إلى ملف صورة: {filePath}");
                    return filePath;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ExportManager.ExportToImage");
                throw;
            }
        }

        /// <summary>
        /// تهروب حقل CSV
        /// </summary>
        /// <param name="field">الحقل</param>
        /// <param name="delimiter">الفاصل</param>
        /// <returns>الحقل المهروب</returns>
        private string EscapeCsvField(string field, char delimiter)
        {
            // التحقق من الحاجة إلى تهروب الحقل
            if (field.Contains(delimiter) || field.Contains("\"") || field.Contains("\n"))
            {
                // تهروب علامات الاقتباس
                field = field.Replace("\"", "\"\"");
                
                // إحاطة الحقل بعلامات اقتباس
                return $"\"{field}\"";
            }
            
            return field;
        }

        /// <summary>
        /// فتح مجلد التصدير
        /// </summary>
        public void OpenExportsFolder()
        {
            try
            {
                // فتح مجلد التصدير
                Process.Start("explorer.exe", _exportsPath);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ExportManager.OpenExportsFolder");
            }
        }
    }

    /// <summary>
    /// تنسيق الصورة
    /// </summary>
    public enum ImageFormat
    {
        /// <summary>
        /// PNG
        /// </summary>
        PNG,
        
        /// <summary>
        /// JPEG
        /// </summary>
        JPEG,
        
        /// <summary>
        /// BMP
        /// </summary>
        BMP,
        
        /// <summary>
        /// GIF
        /// </summary>
        GIF,
        
        /// <summary>
        /// TIFF
        /// </summary>
        TIFF
    }
}
