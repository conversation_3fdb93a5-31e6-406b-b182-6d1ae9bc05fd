using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// يمثل عميل في النظام
    /// </summary>
    public class Customer
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(20)]
        public string Code { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [MaxLength(100)]
        public string ContactPerson { get; set; }

        [MaxLength(100)]
        [EmailAddress]
        public string Email { get; set; }

        [MaxLength(20)]
        public string Phone { get; set; }

        [MaxLength(20)]
        public string Mobile { get; set; }

        [MaxLength(255)]
        public string Address { get; set; }

        [MaxLength(50)]
        public string City { get; set; }

        [MaxLength(50)]
        public string Country { get; set; }

        [MaxLength(20)]
        public string PostalCode { get; set; }

        [MaxLength(50)]
        public string TaxNumber { get; set; }

        /// <summary>
        /// حساب العميل في شجرة الحسابات
        /// </summary>
        public int? AccountId { get; set; }
        public virtual Account Account { get; set; }

        /// <summary>
        /// الرصيد الحالي للعميل
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// الحد الائتماني للعميل
        /// </summary>
        public decimal CreditLimit { get; set; }

        /// <summary>
        /// هل العميل نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ملاحظات
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// تاريخ إنشاء العميل
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث للعميل
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// فواتير العميل
        /// </summary>
        public virtual ICollection<Invoice> Invoices { get; set; }

        public Customer()
        {
            Invoices = new HashSet<Invoice>();
        }
    }
}
