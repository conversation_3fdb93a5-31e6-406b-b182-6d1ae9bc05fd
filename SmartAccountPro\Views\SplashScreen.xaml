<Window x:Class="SmartAccountPro.Views.SplashScreen"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SmartAccountPro.Views"
        mc:Ignorable="d"
        Title="SmartAccount Pro" Height="400" Width="600"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        ResizeMode="NoResize"
        AllowsTransparency="True"
        Background="Transparent">
    <Border CornerRadius="20" BorderThickness="1" BorderBrush="#2196F3">
        <Border.Effect>
            <DropShadowEffect ShadowDepth="3" BlurRadius="15" Opacity="0.3" Color="#000000"/>
        </Border.Effect>
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#1A237E" Offset="0"/>
                <GradientStop Color="#0D47A1" Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0"
                       Text="SmartAccount Pro"
                       FontSize="36"
                       FontWeight="Bold"
                       Foreground="White"
                       HorizontalAlignment="Center"
                       Margin="0,40,0,0">
                <TextBlock.Effect>
                    <DropShadowEffect ShadowDepth="2" BlurRadius="5" Opacity="0.5" Color="#000000"/>
                </TextBlock.Effect>
            </TextBlock>

            <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                <Image Source="/Resources/Images/logo.png" Width="150" Height="150" Margin="0,0,0,20">
                    <Image.Effect>
                        <DropShadowEffect ShadowDepth="2" BlurRadius="10" Opacity="0.5" Color="#000000"/>
                    </Image.Effect>
                </Image>
                <TextBlock Text="نظام المحاسبة الذكي"
                           FontSize="24"
                           FontWeight="SemiBold"
                           Foreground="White"
                           HorizontalAlignment="Center">
                    <TextBlock.Effect>
                        <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.5" Color="#000000"/>
                    </TextBlock.Effect>
                </TextBlock>
                <TextBlock Text="إصدار 1.0"
                           FontSize="16"
                           Foreground="#BBDEFB"
                           HorizontalAlignment="Center"
                           Margin="0,10,0,0"/>
                <ProgressBar Width="300"
                             Height="8"
                             Margin="0,30,0,0"
                             IsIndeterminate="True"
                             Foreground="#4FC3F7">
                    <ProgressBar.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </ProgressBar.Resources>
                </ProgressBar>
            </StackPanel>

            <TextBlock Grid.Row="2"
                       Text="جميع الحقوق محفوظة © 2023"
                       FontSize="12"
                       Foreground="#BBDEFB"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,20"/>
        </Grid>
    </Border>
</Window>
