﻿=== SmartAccount Pro Deployment Test Log ===
Test Session: 25d6bea3
Start Time: 05/31/2025 11:31:18
==================================================
[2025-05-31 11:31:19] [SUCCESS] تم تحميل تكوين الاختبار من: .\deployment\tests\test-config.json
[2025-05-31 11:31:19] [INFO] تهيئة الآلة الافتراضية: Win10-Test-VM
[2025-05-31 11:31:19] [INFO] تهيئة الآلة الافتراضية: Win10-Test-VM
[2025-05-31 11:31:19] [ERROR] خطأ في تهيئة الآلة الافتراضية: The term 'Get-VM' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
[2025-05-31 11:31:19] [ERROR] فشل في تهيئة الآلة الافتراضية
[2025-05-31 11:31:19] [INFO] تهيئة الآلة الافتراضية: Win11-Test-VM
[2025-05-31 11:31:19] [INFO] تهيئة الآلة الافتراضية: Win11-Test-VM
[2025-05-31 11:31:19] [ERROR] خطأ في تهيئة الآلة الافتراضية: The term 'Get-VM' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
[2025-05-31 11:31:19] [ERROR] فشل في تهيئة الآلة الافتراضية
[2025-05-31 11:31:19] [INFO] تهيئة الآلة الافتراضية: WinServer2019-Test-VM
[2025-05-31 11:31:19] [INFO] تهيئة الآلة الافتراضية: WinServer2019-Test-VM
[2025-05-31 11:31:19] [ERROR] خطأ في تهيئة الآلة الافتراضية: The term 'Get-VM' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
[2025-05-31 11:31:19] [ERROR] فشل في تهيئة الآلة الافتراضية
[2025-05-31 11:31:19] [SUCCESS] تم إنشاء تقرير HTML: .\deployment\tests\reports\deployment-test-report-25d6bea3.html
[2025-05-31 11:31:19] [SUCCESS] تم إنشاء تقرير Markdown: .\deployment\tests\reports\deployment-test-report-25d6bea3.md
