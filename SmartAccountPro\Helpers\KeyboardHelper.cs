using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للتعامل مع لوحة المفاتيح
    /// </summary>
    public static class KeyboardHelper
    {
        private static readonly Dictionary<UIElement, Dictionary<Key, Action>> _keyBindings = new Dictionary<UIElement, Dictionary<Key, Action>>();
        private static readonly Dictionary<UIElement, Dictionary<KeyGesture, Action>> _keyGestureBindings = new Dictionary<UIElement, Dictionary<KeyGesture, Action>>();

        /// <summary>
        /// إضافة اختصار لوحة المفاتيح
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="key">المفتاح</param>
        /// <param name="action">الإجراء</param>
        public static void AddKeyBinding(UIElement element, Key key, Action action)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("KeyboardHelper.AddKeyBinding", () =>
                {
                    // التحقق من العنصر
                    if (element == null || action == null)
                        return;

                    // إضافة العنصر إلى القاموس إذا لم يكن موجوداً
                    if (!_keyBindings.ContainsKey(element))
                    {
                        _keyBindings[element] = new Dictionary<Key, Action>();
                        element.KeyDown += Element_KeyDown;
                    }

                    // إضافة الاختصار
                    _keyBindings[element][key] = action;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "KeyboardHelper.AddKeyBinding");
            }
        }

        /// <summary>
        /// إضافة اختصار لوحة المفاتيح مع مفاتيح التعديل
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="keyGesture">إيماءة المفتاح</param>
        /// <param name="action">الإجراء</param>
        public static void AddKeyGestureBinding(UIElement element, KeyGesture keyGesture, Action action)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("KeyboardHelper.AddKeyGestureBinding", () =>
                {
                    // التحقق من العنصر
                    if (element == null || keyGesture == null || action == null)
                        return;

                    // إضافة العنصر إلى القاموس إذا لم يكن موجوداً
                    if (!_keyGestureBindings.ContainsKey(element))
                    {
                        _keyGestureBindings[element] = new Dictionary<KeyGesture, Action>();
                        element.KeyDown += Element_KeyGestureDown;
                    }

                    // إضافة الاختصار
                    _keyGestureBindings[element][keyGesture] = action;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "KeyboardHelper.AddKeyGestureBinding");
            }
        }

        /// <summary>
        /// إزالة اختصار لوحة المفاتيح
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="key">المفتاح</param>
        public static void RemoveKeyBinding(UIElement element, Key key)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("KeyboardHelper.RemoveKeyBinding", () =>
                {
                    // التحقق من العنصر
                    if (element == null)
                        return;

                    // التحقق من وجود العنصر في القاموس
                    if (_keyBindings.ContainsKey(element))
                    {
                        // إزالة الاختصار
                        _keyBindings[element].Remove(key);

                        // إزالة العنصر من القاموس إذا لم يعد لديه اختصارات
                        if (_keyBindings[element].Count == 0)
                        {
                            _keyBindings.Remove(element);
                            element.KeyDown -= Element_KeyDown;
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "KeyboardHelper.RemoveKeyBinding");
            }
        }

        /// <summary>
        /// إزالة اختصار لوحة المفاتيح مع مفاتيح التعديل
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="keyGesture">إيماءة المفتاح</param>
        public static void RemoveKeyGestureBinding(UIElement element, KeyGesture keyGesture)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("KeyboardHelper.RemoveKeyGestureBinding", () =>
                {
                    // التحقق من العنصر
                    if (element == null || keyGesture == null)
                        return;

                    // التحقق من وجود العنصر في القاموس
                    if (_keyGestureBindings.ContainsKey(element))
                    {
                        // إزالة الاختصار
                        _keyGestureBindings[element].Remove(keyGesture);

                        // إزالة العنصر من القاموس إذا لم يعد لديه اختصارات
                        if (_keyGestureBindings[element].Count == 0)
                        {
                            _keyGestureBindings.Remove(element);
                            element.KeyDown -= Element_KeyGestureDown;
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "KeyboardHelper.RemoveKeyGestureBinding");
            }
        }

        /// <summary>
        /// إزالة جميع اختصارات لوحة المفاتيح
        /// </summary>
        /// <param name="element">العنصر</param>
        public static void RemoveAllKeyBindings(UIElement element)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("KeyboardHelper.RemoveAllKeyBindings", () =>
                {
                    // التحقق من العنصر
                    if (element == null)
                        return;

                    // إزالة اختصارات المفاتيح
                    if (_keyBindings.ContainsKey(element))
                    {
                        _keyBindings.Remove(element);
                        element.KeyDown -= Element_KeyDown;
                    }

                    // إزالة اختصارات إيماءات المفاتيح
                    if (_keyGestureBindings.ContainsKey(element))
                    {
                        _keyGestureBindings.Remove(element);
                        element.KeyDown -= Element_KeyGestureDown;
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "KeyboardHelper.RemoveAllKeyBindings");
            }
        }

        /// <summary>
        /// معالج حدث ضغط المفتاح
        /// </summary>
        private static void Element_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // التحقق من العنصر
                var element = sender as UIElement;
                if (element == null)
                    return;

                // التحقق من وجود العنصر في القاموس
                if (_keyBindings.ContainsKey(element))
                {
                    // التحقق من وجود المفتاح في القاموس
                    if (_keyBindings[element].ContainsKey(e.Key))
                    {
                        // تنفيذ الإجراء
                        _keyBindings[element][e.Key]();
                        e.Handled = true;
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "KeyboardHelper.Element_KeyDown");
            }
        }

        /// <summary>
        /// معالج حدث ضغط إيماءة المفتاح
        /// </summary>
        private static void Element_KeyGestureDown(object sender, KeyEventArgs e)
        {
            try
            {
                // التحقق من العنصر
                var element = sender as UIElement;
                if (element == null)
                    return;

                // التحقق من وجود العنصر في القاموس
                if (_keyGestureBindings.ContainsKey(element))
                {
                    // البحث عن إيماءة المفتاح المطابقة
                    foreach (var keyGesture in _keyGestureBindings[element].Keys)
                    {
                        if (keyGesture.Key == e.Key &&
                            keyGesture.Modifiers == Keyboard.Modifiers)
                        {
                            // تنفيذ الإجراء
                            _keyGestureBindings[element][keyGesture]();
                            e.Handled = true;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "KeyboardHelper.Element_KeyGestureDown");
            }
        }

        /// <summary>
        /// إضافة اختصار لوحة المفاتيح للتنقل بين عناصر التحكم
        /// </summary>
        /// <param name="controls">عناصر التحكم</param>
        public static void AddTabNavigationKeyBindings(IEnumerable<Control> controls)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("KeyboardHelper.AddTabNavigationKeyBindings", () =>
                {
                    // التحقق من عناصر التحكم
                    if (controls == null)
                        return;

                    // تحويل عناصر التحكم إلى قائمة
                    var controlsList = controls.ToList();
                    if (controlsList.Count == 0)
                        return;

                    // إضافة اختصارات لوحة المفاتيح لكل عنصر تحكم
                    for (int i = 0; i < controlsList.Count; i++)
                    {
                        var control = controlsList[i];
                        var nextControl = controlsList[(i + 1) % controlsList.Count];
                        var previousControl = controlsList[(i + controlsList.Count - 1) % controlsList.Count];

                        // إضافة اختصار Tab
                        AddKeyBinding(control, Key.Tab, () => nextControl.Focus());

                        // إضافة اختصار Shift+Tab
                        AddKeyGestureBinding(control, new KeyGesture(Key.Tab, ModifierKeys.Shift), () => previousControl.Focus());
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "KeyboardHelper.AddTabNavigationKeyBindings");
            }
        }

        /// <summary>
        /// إضافة اختصار لوحة المفاتيح للتنقل بين عناصر التحكم باستخدام مفاتيح الأسهم
        /// </summary>
        /// <param name="controls">عناصر التحكم</param>
        /// <param name="rows">عدد الصفوف</param>
        /// <param name="columns">عدد الأعمدة</param>
        public static void AddArrowNavigationKeyBindings(IEnumerable<Control> controls, int rows, int columns)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("KeyboardHelper.AddArrowNavigationKeyBindings", () =>
                {
                    // التحقق من عناصر التحكم
                    if (controls == null || rows <= 0 || columns <= 0)
                        return;

                    // تحويل عناصر التحكم إلى مصفوفة
                    var controlsList = controls.ToList();
                    if (controlsList.Count == 0)
                        return;

                    // إضافة اختصارات لوحة المفاتيح لكل عنصر تحكم
                    for (int i = 0; i < controlsList.Count; i++)
                    {
                        var control = controlsList[i];
                        var row = i / columns;
                        var column = i % columns;

                        // إضافة اختصار السهم لأعلى
                        var upRow = (row + rows - 1) % rows;
                        var upIndex = upRow * columns + column;
                        if (upIndex < controlsList.Count)
                        {
                            AddKeyBinding(control, Key.Up, () => controlsList[upIndex].Focus());
                        }

                        // إضافة اختصار السهم لأسفل
                        var downRow = (row + 1) % rows;
                        var downIndex = downRow * columns + column;
                        if (downIndex < controlsList.Count)
                        {
                            AddKeyBinding(control, Key.Down, () => controlsList[downIndex].Focus());
                        }

                        // إضافة اختصار السهم لليسار
                        var leftColumn = (column + columns - 1) % columns;
                        var leftIndex = row * columns + leftColumn;
                        if (leftIndex < controlsList.Count)
                        {
                            AddKeyBinding(control, Key.Left, () => controlsList[leftIndex].Focus());
                        }

                        // إضافة اختصار السهم لليمين
                        var rightColumn = (column + 1) % columns;
                        var rightIndex = row * columns + rightColumn;
                        if (rightIndex < controlsList.Count)
                        {
                            AddKeyBinding(control, Key.Right, () => controlsList[rightIndex].Focus());
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "KeyboardHelper.AddArrowNavigationKeyBindings");
            }
        }
    }
}
