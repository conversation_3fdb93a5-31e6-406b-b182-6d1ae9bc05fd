using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// نموذج سجل التدقيق
    /// </summary>
    public class AuditLog
    {
        /// <summary>
        /// معرف السجل
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// الإجراء المنفذ
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// نوع الكيان
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string EntityType { get; set; } = string.Empty;

        /// <summary>
        /// معرف الكيان
        /// </summary>
        public int? EntityId { get; set; }

        /// <summary>
        /// تفاصيل الإجراء
        /// </summary>
        [MaxLength(1000)]
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// البيانات القديمة (JSON)
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? OldValues { get; set; }

        /// <summary>
        /// البيانات الجديدة (JSON)
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? NewValues { get; set; }

        /// <summary>
        /// وقت تنفيذ الإجراء
        /// </summary>
        [Required]
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// معرف المستخدم الذي نفذ الإجراء
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// المستخدم الذي نفذ الإجراء
        /// </summary>
        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        /// <summary>
        /// عنوان IP للمستخدم
        /// </summary>
        [MaxLength(45)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// معلومات المتصفح
        /// </summary>
        [MaxLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// وصف العملية
        /// </summary>
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        [MaxLength(100)]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// مستوى الأهمية
        /// </summary>
        public int Level { get; set; } = 1;

        /// <summary>
        /// اسم الجدول المتأثر
        /// </summary>
        [MaxLength(100)]
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// معرف الجلسة
        /// </summary>
        [MaxLength(100)]
        public string SessionId { get; set; } = string.Empty;
    }
}
