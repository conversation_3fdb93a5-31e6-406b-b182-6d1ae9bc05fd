<UserControl x:Class="SmartAccountPro.Views.EnhancedReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SmartAccountPro.Views"
             xmlns:vm="clr-namespace:SmartAccountPro.ViewModels"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <UserControl.DataContext>
        <vm:EnhancedReportsViewModel />
    </UserControl.DataContext>

    <Grid Background="{DynamicResource BackgroundColor}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <Border Grid.Row="0"
                Background="{DynamicResource PrimaryColor}"
                CornerRadius="10"
                Padding="20,15"
                Margin="20,20,20,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="2" BlurRadius="10" Opacity="0.2" Color="#000000"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="&#xE9D9;"
                               FontFamily="Segoe MDL2 Assets"
                               FontSize="24"
                               Foreground="White"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>
                    <TextBlock Text="التقارير المالية"
                               FontSize="24"
                               FontWeight="Bold"
                               Foreground="White">
                        <TextBlock.Effect>
                            <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.3" Color="#000000"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </StackPanel>

                <StackPanel Grid.Column="1"
                            Orientation="Horizontal">
                    <Button Command="{Binding ExportToExcelCommand}"
                            Style="{StaticResource GreenButton}"
                            Margin="0,0,10,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE73E;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير إلى Excel"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button Command="{Binding ExportToCsvCommand}"
                            Style="{StaticResource OrangeButton}"
                            Margin="0,0,10,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE8E5;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير إلى CSV"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button Command="{Binding ExportToPdfCommand}"
                            Style="{StaticResource PurpleButton}"
                            Margin="0,0,10,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE7A7;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير إلى PDF"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button Command="{Binding PrintReportCommand}"
                            Style="{StaticResource ModernButton}"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE749;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="طباعة"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- أدوات التصفية -->
        <Border Grid.Row="1"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="10"
                Padding="20,15"
                Margin="20,0,20,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- نوع التقرير -->
                <StackPanel Grid.Column="0"
                            Margin="0,0,20,0">
                    <TextBlock Text="نوع التقرير"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,5"/>
                    <Border BorderBrush="#DDDDDD"
                            BorderThickness="1"
                            CornerRadius="4">
                        <ComboBox Width="150"
                                  ItemsSource="{Binding ReportTypes}"
                                  SelectedItem="{Binding SelectedReportType}"
                                  DisplayMemberPath="Name"
                                  BorderThickness="0"
                                  Background="Transparent"
                                  Padding="8,5"/>
                    </Border>
                </StackPanel>

                <!-- من تاريخ -->
                <StackPanel Grid.Column="1"
                            Margin="0,0,20,0">
                    <TextBlock Text="من تاريخ"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,5"/>
                    <Border BorderBrush="#DDDDDD"
                            BorderThickness="1"
                            CornerRadius="4">
                        <DatePicker Width="120"
                                    SelectedDate="{Binding StartDate}"
                                    BorderThickness="0"
                                    Background="Transparent"
                                    Padding="8,5"/>
                    </Border>
                </StackPanel>

                <!-- إلى تاريخ -->
                <StackPanel Grid.Column="2"
                            Margin="0,0,20,0">
                    <TextBlock Text="إلى تاريخ"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,5"/>
                    <Border BorderBrush="#DDDDDD"
                            BorderThickness="1"
                            CornerRadius="4">
                        <DatePicker Width="120"
                                    SelectedDate="{Binding EndDate}"
                                    BorderThickness="0"
                                    Background="Transparent"
                                    Padding="8,5"/>
                    </Border>
                </StackPanel>

                <!-- الحساب -->
                <StackPanel Grid.Column="3"
                            Margin="0,0,20,0">
                    <TextBlock Text="الحساب"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,5"/>
                    <Border BorderBrush="#DDDDDD"
                            BorderThickness="1"
                            CornerRadius="4">
                        <ComboBox Width="150"
                                  ItemsSource="{Binding Accounts}"
                                  SelectedItem="{Binding SelectedAccount}"
                                  DisplayMemberPath="Name"
                                  BorderThickness="0"
                                  Background="Transparent"
                                  Padding="8,5"/>
                    </Border>
                </StackPanel>

                <!-- حالة الترحيل -->
                <StackPanel Grid.Column="4"
                            Margin="0,0,20,0">
                    <TextBlock Text="حالة الترحيل"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,5"/>
                    <Border BorderBrush="#DDDDDD"
                            BorderThickness="1"
                            CornerRadius="4">
                        <ComboBox Width="120"
                                  ItemsSource="{Binding PostingStatuses}"
                                  SelectedItem="{Binding SelectedPostingStatus}"
                                  DisplayMemberPath="Name"
                                  BorderThickness="0"
                                  Background="Transparent"
                                  Padding="8,5"/>
                    </Border>
                </StackPanel>

                <!-- زر التصفية -->
                <Button Grid.Column="6"
                        Command="{Binding ApplyFilterCommand}"
                        Style="{StaticResource ModernButton}"
                        VerticalAlignment="Bottom"
                        Padding="12,8">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE71C;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="تطبيق التصفية"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- محتوى التقرير -->
        <Border Grid.Row="2"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="10"
                Padding="15"
                Margin="20,0,20,20">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
            </Border.Effect>
            <TabControl Background="Transparent"
                        BorderThickness="0">
                <TabControl.Resources>
                    <Style TargetType="TabItem">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="TabItem">
                                    <Border Name="Border"
                                            BorderThickness="0,0,0,2"
                                            BorderBrush="Transparent"
                                            Margin="0,0,15,0"
                                            Padding="10,5,10,5">
                                        <ContentPresenter x:Name="ContentSite"
                                                          VerticalAlignment="Center"
                                                          HorizontalAlignment="Center"
                                                          ContentSource="Header"
                                                          Margin="0"/>
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="SelectionStates">
                                                <VisualState x:Name="Selected">
                                                    <Storyboard>
                                                        <ColorAnimation Storyboard.TargetName="Border"
                                                                        Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                                        To="{DynamicResource PrimaryColor}"
                                                                        Duration="0:0:0.1"/>
                                                    </Storyboard>
                                                </VisualState>
                                                <VisualState x:Name="Unselected">
                                                    <Storyboard>
                                                        <ColorAnimation Storyboard.TargetName="Border"
                                                                        Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                                        To="Transparent"
                                                                        Duration="0:0:0.1"/>
                                                    </Storyboard>
                                                </VisualState>
                                            </VisualStateGroup>
                                        </VisualStateManager.VisualStateGroups>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="Foreground" Value="#555555"/>
                    </Style>
                </TabControl.Resources>

                <TabItem Header="جدول البيانات">
                    <Grid Margin="0,15,0,0">
                        <DataGrid ItemsSource="{Binding ReportData}"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="Horizontal"
                                  HorizontalGridLinesBrush="#E0E0E0"
                                  Background="White"
                                  RowBackground="White"
                                  AlternatingRowBackground="#F8F9FA"
                                  CanUserSortColumns="True"
                                  CanUserResizeColumns="True"
                                  CanUserReorderColumns="True"
                                  HeadersVisibility="Column"
                                  BorderThickness="1"
                                  BorderBrush="#E0E0E0">
                            <DataGrid.Resources>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#F1F3F4"/>
                                    <Setter Property="Padding" Value="10,8"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                </Style>
                                <Style TargetType="DataGridRow">
                                    <Setter Property="Height" Value="40"/>
                                </Style>
                            </DataGrid.Resources>
                            <DataGrid.Columns>
                                <!-- سيتم إضافة الأعمدة ديناميكياً في الكود -->
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- رسالة عند عدم وجود بيانات -->
                        <Border Background="#F8F9FA"
                                CornerRadius="8"
                                Padding="20,15"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Visibility="{Binding HasNoData, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <TextBlock Text="&#xE9CE;"
                                           FontFamily="Segoe MDL2 Assets"
                                           FontSize="48"
                                           Foreground="#BBBBBB"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,10"/>
                                <TextBlock Text="لا توجد بيانات للعرض"
                                           FontSize="18"
                                           Foreground="#777777"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </TabItem>

                <TabItem Header="الرسم البياني">
                    <Grid Margin="0,15,0,0">
                        <!-- تم تعطيل الرسم البياني مؤقتاً بسبب مشكلة توافق المكتبة -->
                        <Border Background="#F8F9FA"
                                CornerRadius="8"
                                Padding="20,15"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Visibility="{Binding HasData, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <TextBlock Text="&#xE912;"
                                           FontFamily="Segoe MDL2 Assets"
                                           FontSize="48"
                                           Foreground="#BBBBBB"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,10"/>
                                <TextBlock Text="الرسم البياني غير متاح حالياً"
                                           FontSize="18"
                                           Foreground="#777777"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- رسالة عند عدم وجود بيانات -->
                        <Border Background="#F8F9FA"
                                CornerRadius="8"
                                Padding="20,15"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Visibility="{Binding HasNoData, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <TextBlock Text="&#xE9CE;"
                                           FontFamily="Segoe MDL2 Assets"
                                           FontSize="48"
                                           Foreground="#BBBBBB"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,10"/>
                                <TextBlock Text="لا توجد بيانات للعرض"
                                           FontSize="18"
                                           Foreground="#777777"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </TabItem>
            </TabControl>
        </Border>
    </Grid>
</UserControl>
