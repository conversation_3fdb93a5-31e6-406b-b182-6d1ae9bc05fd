using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لتحسين أداء التطبيق
    /// </summary>
    public static class PerformanceHelper
    {
        private static readonly Dictionary<string, Stopwatch> _timers = new Dictionary<string, Stopwatch>();
        private static readonly Dictionary<string, long> _averageTimes = new Dictionary<string, long>();
        private static readonly Dictionary<string, int> _callCounts = new Dictionary<string, int>();
        private static readonly ConcurrentDictionary<string, PerformanceMetric> _metrics = new ConcurrentDictionary<string, PerformanceMetric>();
        private static readonly object _lockObject = new object();

        /// <summary>
        /// بدء قياس الوقت لعملية معينة
        /// </summary>
        /// <param name="operationName">اسم العملية</param>
        public static void StartTimer(string operationName)
        {
            if (_timers.ContainsKey(operationName))
            {
                _timers[operationName].Restart();
            }
            else
            {
                _timers[operationName] = Stopwatch.StartNew();
            }
        }

        /// <summary>
        /// إيقاف قياس الوقت لعملية معينة
        /// </summary>
        /// <param name="operationName">اسم العملية</param>
        /// <param name="logResult">تسجيل النتيجة</param>
        /// <returns>وقت التنفيذ بالمللي ثانية</returns>
        public static long StopTimer(string operationName, bool logResult = true)
        {
            if (!_timers.ContainsKey(operationName))
            {
                return 0;
            }

            _timers[operationName].Stop();
            var elapsedMs = _timers[operationName].ElapsedMilliseconds;

            // تحديث متوسط الوقت
            if (!_averageTimes.ContainsKey(operationName))
            {
                _averageTimes[operationName] = elapsedMs;
                _callCounts[operationName] = 1;
            }
            else
            {
                _callCounts[operationName]++;
                _averageTimes[operationName] = (_averageTimes[operationName] * (_callCounts[operationName] - 1) + elapsedMs) / _callCounts[operationName];
            }

            if (logResult)
            {
                Debug.WriteLine($"[PERFORMANCE] {operationName}: {elapsedMs}ms (متوسط: {_averageTimes[operationName]}ms)");
            }

            return elapsedMs;
        }

        /// <summary>
        /// تنفيذ عملية مع قياس الوقت
        /// </summary>
        /// <param name="operationName">اسم العملية</param>
        /// <param name="action">العملية المراد تنفيذها</param>
        /// <returns>وقت التنفيذ بالمللي ثانية</returns>
        public static long MeasureExecutionTime(string operationName, Action action)
        {
            StartTimer(operationName);
            try
            {
                action();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] خطأ أثناء تنفيذ العملية {operationName}: {ex.Message}");
                ExceptionHandler.HandleException(ex, $"PerformanceHelper.MeasureExecutionTime.{operationName}");
                throw;
            }
            finally
            {
                StopTimer(operationName);

                // تسجيل وقت التنفيذ في النظام الجديد
                RecordExecutionTime(operationName, _timers[operationName].ElapsedMilliseconds);
            }

            return _timers[operationName].ElapsedMilliseconds;
        }

        /// <summary>
        /// تنفيذ عملية مع قياس الوقت وإرجاع نتيجة
        /// </summary>
        /// <typeparam name="T">نوع النتيجة</typeparam>
        /// <param name="operationName">اسم العملية</param>
        /// <param name="func">الدالة المراد تنفيذها</param>
        /// <returns>النتيجة</returns>
        public static T MeasureExecutionTime<T>(string operationName, Func<T> func)
        {
            StartTimer(operationName);
            try
            {
                return func();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] خطأ أثناء تنفيذ العملية {operationName}: {ex.Message}");
                ExceptionHandler.HandleException(ex, $"PerformanceHelper.MeasureExecutionTime<T>.{operationName}");
                throw;
            }
            finally
            {
                StopTimer(operationName);

                // تسجيل وقت التنفيذ في النظام الجديد
                RecordExecutionTime(operationName, _timers[operationName].ElapsedMilliseconds);
            }
        }

        /// <summary>
        /// تنفيذ عملية غير متزامنة مع قياس الوقت
        /// </summary>
        /// <param name="operationName">اسم العملية</param>
        /// <param name="asyncAction">العملية غير المتزامنة المراد تنفيذها</param>
        /// <returns>مهمة تمثل وقت التنفيذ بالمللي ثانية</returns>
        public static async Task<long> MeasureExecutionTimeAsync(string operationName, Func<Task> asyncAction)
        {
            StartTimer(operationName);
            try
            {
                await asyncAction();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] خطأ أثناء تنفيذ العملية غير المتزامنة {operationName}: {ex.Message}");
                ExceptionHandler.HandleException(ex, $"PerformanceHelper.MeasureExecutionTimeAsync.{operationName}");
                throw;
            }
            finally
            {
                StopTimer(operationName);

                // تسجيل وقت التنفيذ في النظام الجديد
                RecordExecutionTime(operationName, _timers[operationName].ElapsedMilliseconds);
            }

            return _timers[operationName].ElapsedMilliseconds;
        }

        /// <summary>
        /// تنفيذ عملية غير متزامنة مع قياس الوقت وإرجاع نتيجة
        /// </summary>
        /// <typeparam name="T">نوع النتيجة</typeparam>
        /// <param name="operationName">اسم العملية</param>
        /// <param name="asyncFunc">الدالة غير المتزامنة المراد تنفيذها</param>
        /// <returns>مهمة تمثل النتيجة</returns>
        public static async Task<T> MeasureExecutionTimeAsync<T>(string operationName, Func<Task<T>> asyncFunc)
        {
            StartTimer(operationName);
            try
            {
                return await asyncFunc();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] خطأ أثناء تنفيذ العملية غير المتزامنة {operationName}: {ex.Message}");
                ExceptionHandler.HandleException(ex, $"PerformanceHelper.MeasureExecutionTimeAsync<T>.{operationName}");
                throw;
            }
            finally
            {
                StopTimer(operationName);

                // تسجيل وقت التنفيذ في النظام الجديد
                RecordExecutionTime(operationName, _timers[operationName].ElapsedMilliseconds);
            }
        }

        /// <summary>
        /// تنفيذ عملية في خلفية واجهة المستخدم
        /// </summary>
        /// <param name="action">العملية المراد تنفيذها</param>
        /// <param name="priority">أولوية التنفيذ</param>
        public static void RunInBackground(Action action, DispatcherPriority priority = DispatcherPriority.Background)
        {
            Application.Current.Dispatcher.BeginInvoke(action, priority);
        }

        /// <summary>
        /// تنفيذ عملية في خلفية واجهة المستخدم مع قياس الوقت
        /// </summary>
        /// <param name="operationName">اسم العملية</param>
        /// <param name="action">العملية المراد تنفيذها</param>
        /// <param name="priority">أولوية التنفيذ</param>
        public static void RunInBackgroundWithTiming(string operationName, Action action, DispatcherPriority priority = DispatcherPriority.Background)
        {
            Application.Current.Dispatcher.BeginInvoke(() =>
            {
                MeasureExecutionTime(operationName, action);
            }, priority);
        }

        /// <summary>
        /// الحصول على تقرير أداء لجميع العمليات
        /// </summary>
        /// <returns>تقرير الأداء</returns>
        public static string GetPerformanceReport()
        {
            try
            {
                var sb = new StringBuilder();
                sb.AppendLine("=== تقرير الأداء ===");

                // إضافة معلومات من النظام القديم
                sb.AppendLine("العمليات المقاسة (النظام القديم):");
                foreach (var operation in _averageTimes.Keys)
                {
                    sb.AppendLine($"{operation}: متوسط {_averageTimes[operation]}ms ({_callCounts[operation]} مرة)");
                }
                sb.AppendLine();

                // إضافة معلومات من النظام الجديد
                sb.AppendLine($"عدد العمليات المقاسة (النظام الجديد): {_metrics.Count}");
                sb.AppendLine();

                // الحصول على مقاييس الأداء
                var metrics = GetMetrics();

                // إضافة العمليات الأبطأ
                sb.AppendLine("العمليات الأبطأ (متوسط وقت التنفيذ):");
                foreach (var metric in metrics.Take(10))
                {
                    sb.AppendLine($"{metric.OperationName}: {metric.AverageExecutionTime:F2} ms (تم التنفيذ {metric.ExecutionCount} مرة)");
                }
                sb.AppendLine();

                // إضافة العمليات الأكثر تنفيذاً
                sb.AppendLine("العمليات الأكثر تنفيذاً:");
                foreach (var metric in metrics.OrderByDescending(m => m.ExecutionCount).Take(10))
                {
                    sb.AppendLine($"{metric.OperationName}: {metric.ExecutionCount} مرة (متوسط وقت التنفيذ: {metric.AverageExecutionTime:F2} ms)");
                }
                sb.AppendLine();

                // إضافة العمليات ذات أقصى وقت تنفيذ
                sb.AppendLine("العمليات ذات أقصى وقت تنفيذ:");
                foreach (var metric in metrics.OrderByDescending(m => m.MaxExecutionTime).Take(10))
                {
                    sb.AppendLine($"{metric.OperationName}: {metric.MaxExecutionTime} ms (متوسط وقت التنفيذ: {metric.AverageExecutionTime:F2} ms)");
                }

                return sb.ToString();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "PerformanceHelper.GetPerformanceReport");
                return "حدث خطأ أثناء إنشاء تقرير الأداء.";
            }
        }

        /// <summary>
        /// تحديد العمليات البطيئة
        /// </summary>
        /// <param name="thresholdMs">الحد الأدنى للوقت بالمللي ثانية</param>
        /// <returns>قائمة العمليات البطيئة</returns>
        public static Dictionary<string, long> GetSlowOperations(long thresholdMs = 100)
        {
            try
            {
                var slowOperations = new Dictionary<string, long>();

                // إضافة العمليات البطيئة من النظام القديم
                foreach (var operation in _averageTimes.Keys)
                {
                    if (_averageTimes[operation] > thresholdMs)
                    {
                        slowOperations.Add(operation, _averageTimes[operation]);
                    }
                }

                // إضافة العمليات البطيئة من النظام الجديد
                foreach (var metric in _metrics.Values)
                {
                    if (metric.AverageExecutionTime > thresholdMs && !slowOperations.ContainsKey(metric.OperationName))
                    {
                        slowOperations.Add(metric.OperationName, (long)metric.AverageExecutionTime);
                    }
                }

                return slowOperations;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "PerformanceHelper.GetSlowOperations");
                return new Dictionary<string, long>();
            }
        }

        /// <summary>
        /// مسح جميع بيانات الأداء
        /// </summary>
        public static void ClearPerformanceData()
        {
            try
            {
                _timers.Clear();
                _averageTimes.Clear();
                _callCounts.Clear();
                _metrics.Clear();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "PerformanceHelper.ClearPerformanceData");
            }
        }

        /// <summary>
        /// الحصول على مقاييس الأداء
        /// </summary>
        /// <returns>مقاييس الأداء</returns>
        public static List<PerformanceMetric> GetMetrics()
        {
            try
            {
                return _metrics.Values.OrderByDescending(m => m.AverageExecutionTime).ToList();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "PerformanceHelper.GetMetrics");
                return new List<PerformanceMetric>();
            }
        }

        /// <summary>
        /// تسجيل وقت تنفيذ العملية
        /// </summary>
        /// <param name="operationName">اسم العملية</param>
        /// <param name="elapsedMilliseconds">الوقت المستغرق بالمللي ثانية</param>
        private static void RecordExecutionTime(string operationName, long elapsedMilliseconds)
        {
            try
            {
                // الحصول على مقياس الأداء
                var metric = _metrics.GetOrAdd(operationName, _ => new PerformanceMetric(operationName));

                // تحديث مقياس الأداء
                metric.RecordExecution(elapsedMilliseconds);

                // تسجيل وقت التنفيذ
                Debug.WriteLine($"[PERFORMANCE] {operationName}: {elapsedMilliseconds} ms");

                // التحقق من تجاوز الحد الأقصى
                if (elapsedMilliseconds > 1000)
                {
                    Debug.WriteLine($"[PERFORMANCE] تحذير: {operationName} استغرق {elapsedMilliseconds} ms (أكثر من 1 ثانية)");
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "PerformanceHelper.RecordExecutionTime");
            }
        }
    }

    /// <summary>
    /// مقياس الأداء
    /// </summary>
    public class PerformanceMetric
    {
        private readonly object _lockObject = new object();

        /// <summary>
        /// اسم العملية
        /// </summary>
        public string OperationName { get; }

        /// <summary>
        /// عدد مرات التنفيذ
        /// </summary>
        public int ExecutionCount { get; private set; }

        /// <summary>
        /// إجمالي وقت التنفيذ
        /// </summary>
        public long TotalExecutionTime { get; private set; }

        /// <summary>
        /// متوسط وقت التنفيذ
        /// </summary>
        public double AverageExecutionTime => ExecutionCount > 0 ? (double)TotalExecutionTime / ExecutionCount : 0;

        /// <summary>
        /// أقصى وقت تنفيذ
        /// </summary>
        public long MaxExecutionTime { get; private set; }

        /// <summary>
        /// أدنى وقت تنفيذ
        /// </summary>
        public long MinExecutionTime { get; private set; }

        /// <summary>
        /// آخر وقت تنفيذ
        /// </summary>
        public long LastExecutionTime { get; private set; }

        /// <summary>
        /// إنشاء مقياس أداء جديد
        /// </summary>
        /// <param name="operationName">اسم العملية</param>
        public PerformanceMetric(string operationName)
        {
            OperationName = operationName;
            ExecutionCount = 0;
            TotalExecutionTime = 0;
            MaxExecutionTime = 0;
            MinExecutionTime = long.MaxValue;
            LastExecutionTime = 0;
        }

        /// <summary>
        /// تسجيل تنفيذ العملية
        /// </summary>
        /// <param name="elapsedMilliseconds">الوقت المستغرق بالمللي ثانية</param>
        public void RecordExecution(long elapsedMilliseconds)
        {
            lock (_lockObject)
            {
                ExecutionCount++;
                TotalExecutionTime += elapsedMilliseconds;
                MaxExecutionTime = Math.Max(MaxExecutionTime, elapsedMilliseconds);
                MinExecutionTime = Math.Min(MinExecutionTime, elapsedMilliseconds);
                LastExecutionTime = elapsedMilliseconds;
            }
        }
    }
}
