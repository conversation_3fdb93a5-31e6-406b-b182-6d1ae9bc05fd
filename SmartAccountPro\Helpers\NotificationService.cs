using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// خدمة إرسال الإشعارات للمستخدمين
    /// </summary>
    public class NotificationService
    {
        private static NotificationService? _instance;
        private readonly EmailSettings _emailSettings;
        private readonly SmsSettings _smsSettings;
        private readonly List<NotificationTemplate> _templates;

        /// <summary>
        /// الحصول على النسخة الوحيدة من خدمة الإشعارات
        /// </summary>
        public static NotificationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new NotificationService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// إنشاء نسخة جديدة من خدمة الإشعارات
        /// </summary>
        private NotificationService()
        {
            try
            {
                // تحميل إعدادات البريد الإلكتروني
                _emailSettings = LoadEmailSettings();

                // تحميل إعدادات الرسائل النصية
                _smsSettings = LoadSmsSettings();

                // تحميل قوالب الإشعارات
                _templates = LoadNotificationTemplates();

                Debug.WriteLine("[NOTIFICATION] تم تهيئة خدمة الإشعارات.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationService.Constructor");
                _emailSettings = new EmailSettings();
                _smsSettings = new SmsSettings();
                _templates = new List<NotificationTemplate>();
            }
        }

        /// <summary>
        /// إرسال إشعار بالبريد الإلكتروني
        /// </summary>
        /// <param name="to">المستقبل</param>
        /// <param name="subject">الموضوع</param>
        /// <param name="body">المحتوى</param>
        /// <param name="isHtml">هل المحتوى HTML</param>
        /// <returns>نجاح الإرسال</returns>
        public async Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = false)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("NotificationService.SendEmail", async () =>
                {
                    // التحقق من إعدادات البريد الإلكتروني
                    if (!_emailSettings.IsConfigured)
                    {
                        Debug.WriteLine("[NOTIFICATION] إعدادات البريد الإلكتروني غير مكتملة.");
                        return false;
                    }

                    // إنشاء رسالة البريد الإلكتروني
                    using (var message = new MailMessage())
                    {
                        message.From = new MailAddress(_emailSettings.FromEmail, _emailSettings.FromName);
                        message.To.Add(to);
                        message.Subject = subject;
                        message.Body = body;
                        message.IsBodyHtml = isHtml;
                        message.BodyEncoding = Encoding.UTF8;
                        message.SubjectEncoding = Encoding.UTF8;

                        // إنشاء عميل SMTP
                        using (var client = new SmtpClient(_emailSettings.SmtpServer, _emailSettings.SmtpPort))
                        {
                            client.EnableSsl = _emailSettings.EnableSsl;
                            client.UseDefaultCredentials = false;
                            client.Credentials = new NetworkCredential(_emailSettings.Username, _emailSettings.Password);

                            // إرسال الرسالة
                            await client.SendMailAsync(message);
                        }
                    }

                    Debug.WriteLine($"[NOTIFICATION] تم إرسال بريد إلكتروني إلى {to} بموضوع '{subject}'.");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationService.SendEmail");
                return false;
            }
        }

        /// <summary>
        /// إرسال إشعار بالرسائل النصية
        /// </summary>
        /// <param name="to">رقم الهاتف</param>
        /// <param name="message">الرسالة</param>
        /// <returns>نجاح الإرسال</returns>
        public async Task<bool> SendSmsAsync(string to, string message)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("NotificationService.SendSms", async () =>
                {
                    // التحقق من إعدادات الرسائل النصية
                    if (!_smsSettings.IsConfigured)
                    {
                        Debug.WriteLine("[NOTIFICATION] إعدادات الرسائل النصية غير مكتملة.");
                        return false;
                    }

                    // إرسال الرسالة عبر API
                    using (var client = new HttpClient())
                    {
                        // إعداد الطلب
                        var requestData = new
                        {
                            to = to,
                            message = message,
                            apiKey = _smsSettings.ApiKey
                        };

                        var json = Newtonsoft.Json.JsonConvert.SerializeObject(requestData);
                        var content = new StringContent(json, Encoding.UTF8, "application/json");

                        // إرسال الطلب
                        var response = await client.PostAsync(_smsSettings.ApiUrl, content);
                        
                        if (response.IsSuccessStatusCode)
                        {
                            Debug.WriteLine($"[NOTIFICATION] تم إرسال رسالة نصية إلى {to}.");
                            return true;
                        }
                        else
                        {
                            Debug.WriteLine($"[NOTIFICATION] فشل إرسال رسالة نصية إلى {to}. كود الاستجابة: {response.StatusCode}");
                            return false;
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationService.SendSms");
                return false;
            }
        }

        /// <summary>
        /// إرسال إشعار باستخدام قالب
        /// </summary>
        /// <param name="templateName">اسم القالب</param>
        /// <param name="to">المستقبل</param>
        /// <param name="parameters">معاملات القالب</param>
        /// <param name="notificationType">نوع الإشعار</param>
        /// <returns>نجاح الإرسال</returns>
        public async Task<bool> SendNotificationAsync(string templateName, string to, Dictionary<string, string> parameters, NotificationChannel notificationType)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("NotificationService.SendNotification", async () =>
                {
                    // البحث عن القالب
                    var template = _templates.Find(t => t.Name == templateName);
                    if (template == null)
                    {
                        Debug.WriteLine($"[NOTIFICATION] القالب '{templateName}' غير موجود.");
                        return false;
                    }

                    // تطبيق المعاملات على القالب
                    string subject = ApplyParameters(template.Subject, parameters);
                    string body = ApplyParameters(template.Body, parameters);

                    // إرسال الإشعار حسب النوع
                    switch (notificationType)
                    {
                        case NotificationChannel.Email:
                            return await SendEmailAsync(to, subject, body, template.IsHtml);
                        case NotificationChannel.Sms:
                            return await SendSmsAsync(to, body);
                        case NotificationChannel.Both:
                            bool emailResult = await SendEmailAsync(to, subject, body, template.IsHtml);
                            bool smsResult = await SendSmsAsync(to, body);
                            return emailResult || smsResult;
                        default:
                            return false;
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationService.SendNotification");
                return false;
            }
        }

        /// <summary>
        /// إرسال تقرير دوري
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="recipients">قائمة المستقبلين</param>
        /// <param name="reportData">بيانات التقرير</param>
        /// <returns>نجاح الإرسال</returns>
        public async Task<bool> SendPeriodicReportAsync(string reportType, List<string> recipients, object reportData)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("NotificationService.SendPeriodicReport", async () =>
                {
                    // إنشاء محتوى التقرير
                    string subject = $"تقرير {reportType} - {DateTime.Now:yyyy-MM-dd}";
                    string body = GenerateReportBody(reportType, reportData);

                    // إرسال التقرير لجميع المستقبلين
                    bool allSuccess = true;
                    foreach (string recipient in recipients)
                    {
                        bool result = await SendEmailAsync(recipient, subject, body, true);
                        if (!result)
                        {
                            allSuccess = false;
                        }
                    }

                    Debug.WriteLine($"[NOTIFICATION] تم إرسال تقرير {reportType} إلى {recipients.Count} مستقبل.");
                    return allSuccess;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationService.SendPeriodicReport");
                return false;
            }
        }

        /// <summary>
        /// إرسال تنبيه أمني
        /// </summary>
        /// <param name="alertType">نوع التنبيه</param>
        /// <param name="details">تفاصيل التنبيه</param>
        /// <param name="adminEmails">بريد المديرين</param>
        /// <returns>نجاح الإرسال</returns>
        public async Task<bool> SendSecurityAlertAsync(string alertType, string details, List<string> adminEmails)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("NotificationService.SendSecurityAlert", async () =>
                {
                    string subject = $"تنبيه أمني - {alertType}";
                    string body = $@"
                        <h2>تنبيه أمني</h2>
                        <p><strong>نوع التنبيه:</strong> {alertType}</p>
                        <p><strong>الوقت:</strong> {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>
                        <p><strong>التفاصيل:</strong></p>
                        <p>{details}</p>
                        <hr>
                        <p><em>هذا تنبيه تلقائي من نظام SmartAccount Pro</em></p>
                    ";

                    // إرسال التنبيه لجميع المديرين
                    bool allSuccess = true;
                    foreach (string adminEmail in adminEmails)
                    {
                        bool result = await SendEmailAsync(adminEmail, subject, body, true);
                        if (!result)
                        {
                            allSuccess = false;
                        }
                    }

                    Debug.WriteLine($"[NOTIFICATION] تم إرسال تنبيه أمني '{alertType}' إلى {adminEmails.Count} مدير.");
                    return allSuccess;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationService.SendSecurityAlert");
                return false;
            }
        }

        /// <summary>
        /// تحديث إعدادات البريد الإلكتروني
        /// </summary>
        /// <param name="settings">الإعدادات الجديدة</param>
        /// <returns>نجاح التحديث</returns>
        public bool UpdateEmailSettings(EmailSettings settings)
        {
            try
            {
                return PerformanceHelper.MeasureExecutionTime("NotificationService.UpdateEmailSettings", () =>
                {
                    // تحديث الإعدادات
                    _emailSettings.SmtpServer = settings.SmtpServer;
                    _emailSettings.SmtpPort = settings.SmtpPort;
                    _emailSettings.EnableSsl = settings.EnableSsl;
                    _emailSettings.Username = settings.Username;
                    _emailSettings.Password = settings.Password;
                    _emailSettings.FromEmail = settings.FromEmail;
                    _emailSettings.FromName = settings.FromName;

                    // حفظ الإعدادات
                    SaveEmailSettings(_emailSettings);

                    Debug.WriteLine("[NOTIFICATION] تم تحديث إعدادات البريد الإلكتروني.");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationService.UpdateEmailSettings");
                return false;
            }
        }

        /// <summary>
        /// اختبار إعدادات البريد الإلكتروني
        /// </summary>
        /// <param name="testEmail">بريد الاختبار</param>
        /// <returns>نجاح الاختبار</returns>
        public async Task<bool> TestEmailSettingsAsync(string testEmail)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("NotificationService.TestEmailSettings", async () =>
                {
                    string subject = "اختبار إعدادات البريد الإلكتروني - SmartAccount Pro";
                    string body = $@"
                        <h2>اختبار إعدادات البريد الإلكتروني</h2>
                        <p>تم إرسال هذه الرسالة لاختبار إعدادات البريد الإلكتروني في نظام SmartAccount Pro.</p>
                        <p><strong>الوقت:</strong> {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>
                        <p>إذا تلقيت هذه الرسالة، فإن الإعدادات تعمل بشكل صحيح.</p>
                    ";

                    return await SendEmailAsync(testEmail, subject, body, true);
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationService.TestEmailSettings");
                return false;
            }
        }

        /// <summary>
        /// تحميل إعدادات البريد الإلكتروني
        /// </summary>
        private EmailSettings LoadEmailSettings()
        {
            try
            {
                return new EmailSettings
                {
                    SmtpServer = Properties.Settings.Default.EmailSmtpServer,
                    SmtpPort = Properties.Settings.Default.EmailSmtpPort,
                    EnableSsl = Properties.Settings.Default.EmailEnableSsl,
                    Username = Properties.Settings.Default.EmailUsername,
                    Password = EncryptionHelper.Decrypt(Properties.Settings.Default.EmailPassword),
                    FromEmail = Properties.Settings.Default.EmailFromAddress,
                    FromName = Properties.Settings.Default.EmailFromName
                };
            }
            catch
            {
                return new EmailSettings();
            }
        }

        /// <summary>
        /// حفظ إعدادات البريد الإلكتروني
        /// </summary>
        private void SaveEmailSettings(EmailSettings settings)
        {
            try
            {
                Properties.Settings.Default.EmailSmtpServer = settings.SmtpServer;
                Properties.Settings.Default.EmailSmtpPort = settings.SmtpPort;
                Properties.Settings.Default.EmailEnableSsl = settings.EnableSsl;
                Properties.Settings.Default.EmailUsername = settings.Username;
                Properties.Settings.Default.EmailPassword = EncryptionHelper.Encrypt(settings.Password);
                Properties.Settings.Default.EmailFromAddress = settings.FromEmail;
                Properties.Settings.Default.EmailFromName = settings.FromName;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[NOTIFICATION] فشل حفظ إعدادات البريد الإلكتروني: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل إعدادات الرسائل النصية
        /// </summary>
        private SmsSettings LoadSmsSettings()
        {
            try
            {
                return new SmsSettings
                {
                    ApiUrl = Properties.Settings.Default.SmsApiUrl,
                    ApiKey = EncryptionHelper.Decrypt(Properties.Settings.Default.SmsApiKey),
                    SenderName = Properties.Settings.Default.SmsSenderName
                };
            }
            catch
            {
                return new SmsSettings();
            }
        }

        /// <summary>
        /// تحميل قوالب الإشعارات
        /// </summary>
        private List<NotificationTemplate> LoadNotificationTemplates()
        {
            return new List<NotificationTemplate>
            {
                new NotificationTemplate
                {
                    Name = "WelcomeUser",
                    Subject = "مرحباً بك في SmartAccount Pro",
                    Body = "مرحباً {UserName}، تم إنشاء حسابك بنجاح في نظام SmartAccount Pro.",
                    IsHtml = false
                },
                new NotificationTemplate
                {
                    Name = "PasswordReset",
                    Subject = "إعادة تعيين كلمة المرور",
                    Body = "مرحباً {UserName}، تم إعادة تعيين كلمة المرور الخاصة بك. كلمة المرور الجديدة: {NewPassword}",
                    IsHtml = false
                },
                new NotificationTemplate
                {
                    Name = "BackupCompleted",
                    Subject = "تم إكمال النسخة الاحتياطية",
                    Body = "تم إنشاء النسخة الاحتياطية بنجاح في {BackupTime}. حجم النسخة: {BackupSize}",
                    IsHtml = false
                }
            };
        }

        /// <summary>
        /// تطبيق المعاملات على النص
        /// </summary>
        private string ApplyParameters(string text, Dictionary<string, string> parameters)
        {
            foreach (var parameter in parameters)
            {
                text = text.Replace($"{{{parameter.Key}}}", parameter.Value);
            }
            return text;
        }

        /// <summary>
        /// إنشاء محتوى التقرير
        /// </summary>
        private string GenerateReportBody(string reportType, object reportData)
        {
            return $@"
                <h2>تقرير {reportType}</h2>
                <p><strong>تاريخ التقرير:</strong> {DateTime.Now:yyyy-MM-dd}</p>
                <p><strong>البيانات:</strong></p>
                <pre>{Newtonsoft.Json.JsonConvert.SerializeObject(reportData, Newtonsoft.Json.Formatting.Indented)}</pre>
                <hr>
                <p><em>تم إنشاء هذا التقرير تلقائياً من نظام SmartAccount Pro</em></p>
            ";
        }
    }

    /// <summary>
    /// إعدادات البريد الإلكتروني
    /// </summary>
    public class EmailSettings
    {
        public string SmtpServer { get; set; } = string.Empty;
        public int SmtpPort { get; set; } = 587;
        public bool EnableSsl { get; set; } = true;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string FromEmail { get; set; } = string.Empty;
        public string FromName { get; set; } = string.Empty;

        public bool IsConfigured => !string.IsNullOrEmpty(SmtpServer) && 
                                   !string.IsNullOrEmpty(Username) && 
                                   !string.IsNullOrEmpty(Password) && 
                                   !string.IsNullOrEmpty(FromEmail);
    }

    /// <summary>
    /// إعدادات الرسائل النصية
    /// </summary>
    public class SmsSettings
    {
        public string ApiUrl { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string SenderName { get; set; } = string.Empty;

        public bool IsConfigured => !string.IsNullOrEmpty(ApiUrl) && 
                                   !string.IsNullOrEmpty(ApiKey);
    }

    /// <summary>
    /// قالب الإشعار
    /// </summary>
    public class NotificationTemplate
    {
        public string Name { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public bool IsHtml { get; set; } = false;
    }

    /// <summary>
    /// قناة الإشعار
    /// </summary>
    public enum NotificationChannel
    {
        Email,
        Sms,
        Both
    }
}
