<UserControl x:Class="SmartAccountPro.Views.InvoicesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SmartAccountPro.Views"
             xmlns:vm="clr-namespace:SmartAccountPro.ViewModels"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             FlowDirection="RightToLeft">

    <UserControl.DataContext>
        <vm:InvoicesViewModel />
    </UserControl.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0"
                Background="{DynamicResource PrimaryColor}"
                CornerRadius="10"
                Padding="15"
                Margin="20,20,20,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="2" BlurRadius="10" Opacity="0.2" Color="#000000"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="&#xE71D;"
                           FontFamily="Segoe MDL2 Assets"
                           FontSize="24"
                           Foreground="White"
                           VerticalAlignment="Center"
                           Margin="0,0,10,0"/>
                <TextBlock Text="الفواتير"
                           FontSize="24"
                           FontWeight="Bold"
                           Foreground="White">
                    <TextBlock.Effect>
                        <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.3" Color="#000000"/>
                    </TextBlock.Effect>
                </TextBlock>
            </StackPanel>
        </Border>

        <!-- شريط الأدوات -->
        <Border Grid.Row="1"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="10"
                Padding="15"
                Margin="20,0,20,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="0"
                            Orientation="Horizontal"
                            HorizontalAlignment="Left">
                    <Button Command="{Binding AddInvoiceCommand}"
                            Style="{StaticResource GreenButton}"
                            Margin="0,0,10,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE710;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة فاتورة"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button Command="{Binding EditInvoiceCommand}"
                            Style="{StaticResource ModernButton}"
                            Margin="0,0,10,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE70F;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="تعديل"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button Command="{Binding DeleteInvoiceCommand}"
                            Style="{StaticResource RedButton}"
                            Margin="0,0,10,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE74D;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="حذف"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button Command="{Binding PrintInvoiceCommand}"
                            Style="{StaticResource ModernButton}"
                            Margin="0,0,10,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE749;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="طباعة"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button Command="{Binding ExportToPdfCommand}"
                            Style="{StaticResource ModernButton}"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE7A7;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير PDF"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <!-- البحث -->
                <Border Grid.Column="2"
                        Background="#F5F5F5"
                        CornerRadius="8"
                        Padding="5">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0"
                                BorderBrush="#DDDDDD"
                                BorderThickness="1"
                                CornerRadius="4"
                                Margin="0,0,10,0">
                            <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                     Width="200"
                                     BorderThickness="0"
                                     Background="Transparent"
                                     Padding="8,5"/>
                        </Border>
                        <Button Grid.Column="1"
                                Command="{Binding SearchCommand}"
                                Style="{StaticResource ModernButton}"
                                Padding="12,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="&#xE721;"
                                           FontFamily="Segoe MDL2 Assets"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                                <TextBlock Text="بحث"
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Border>
            </Grid>
        </Border>

        <!-- جدول الفواتير -->
        <Border Grid.Row="2"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="10"
                Padding="15"
                Margin="20,0,20,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="&#xE82D;"
                               FontFamily="Segoe MDL2 Assets"
                               Foreground="{DynamicResource PrimaryColor}"
                               FontSize="18"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>
                    <TextBlock Text="قائمة الفواتير"
                               FontWeight="Bold"
                               FontSize="16"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <DataGrid Grid.Row="1"
                          ItemsSource="{Binding Invoices}"
                          SelectedItem="{Binding SelectedInvoice}"
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          BorderThickness="1"
                          BorderBrush="#E0E0E0"
                          Background="White"
                          RowBackground="White"
                          AlternatingRowBackground="#F8F9FA"
                          HeadersVisibility="Column"
                          GridLinesVisibility="Horizontal"
                          HorizontalGridLinesBrush="#E0E0E0"
                          CanUserSortColumns="True"
                          CanUserResizeColumns="True"
                          SelectionMode="Single"
                          SelectionUnit="FullRow">
                    <DataGrid.Resources>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#F1F3F4"/>
                            <Setter Property="Padding" Value="10,8"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                        </Style>
                        <Style TargetType="DataGridRow">
                            <Setter Property="Height" Value="40"/>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8,0"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat={}{0:yyyy-MM-dd}}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8,0"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="*">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8,0"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Total, StringFormat={}{0:N2}}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8,0"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTemplateColumn Header="الحالة" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="{Binding Status, Converter={StaticResource StatusToColorConverter}}"
                                            CornerRadius="4"
                                            Padding="8,3"
                                            Margin="5"
                                            HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding StatusName}"
                                                   Foreground="White"
                                                   FontWeight="SemiBold"
                                                   FontSize="12"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- معلومات إضافية -->
        <Border Grid.Row="3"
                Background="#F8F9FA"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="10"
                Padding="15,10"
                Margin="20,0,20,20">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Border Background="{DynamicResource PrimaryColor}"
                            CornerRadius="5"
                            Padding="8,5"
                            Margin="0,0,10,0">
                        <TextBlock Text="&#xE8C0;"
                                   FontFamily="Segoe MDL2 Assets"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                    </Border>
                    <TextBlock Text="{Binding Invoices.Count, StringFormat={}عدد الفواتير: {0}}"
                               VerticalAlignment="Center"
                               FontWeight="SemiBold"
                               Foreground="#555555"/>
                </StackPanel>

                <Border Grid.Column="1"
                        Background="#4CAF50"
                        CornerRadius="5"
                        Padding="10,5">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE8EC;"
                                   FontFamily="Segoe MDL2 Assets"
                                   Foreground="White"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="الإجمالي: "
                                   VerticalAlignment="Center"
                                   Foreground="White"/>
                        <TextBlock Text="{Binding Invoices, Converter={StaticResource InvoicesTotalConverter}}"
                                   VerticalAlignment="Center"
                                   FontWeight="Bold"
                                   Foreground="White"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- نافذة الفاتورة -->
        <Border Grid.Row="0"
                Grid.RowSpan="4"
                Background="#80000000"
                Visibility="{Binding IsInvoiceDialogOpen, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Border Width="700"
                    Height="500"
                    Background="{DynamicResource CardBackgroundColor}"
                    CornerRadius="5"
                    BorderBrush="{DynamicResource BorderColor}"
                    BorderThickness="1"
                    Padding="20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان النافذة -->
                    <TextBlock Grid.Row="0"
                               Text="{Binding InvoiceDialogTitle}"
                               FontSize="20"
                               FontWeight="Bold"
                               Margin="0,0,0,20"
                               Foreground="{DynamicResource TextColor}"/>

                    <!-- محتوى النافذة -->
                    <ScrollViewer Grid.Row="1"
                                  VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <!-- بيانات الفاتورة -->
                            <Grid Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- رقم الفاتورة -->
                                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,10">
                                    <TextBlock Text="رقم الفاتورة"
                                               Margin="0,0,0,5"
                                               Foreground="{DynamicResource SecondaryTextColor}"/>
                                    <TextBox Text="{Binding CurrentInvoice.InvoiceNumber}"
                                             Style="{StaticResource DefaultTextBox}"
                                             IsReadOnly="True"/>
                                </StackPanel>

                                <!-- التاريخ -->
                                <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,10">
                                    <TextBlock Text="التاريخ"
                                               Margin="0,0,0,5"
                                               Foreground="{DynamicResource SecondaryTextColor}"/>
                                    <DatePicker SelectedDate="{Binding CurrentInvoice.Date}"
                                                BorderBrush="{DynamicResource BorderColor}"
                                                BorderThickness="1"
                                                Padding="5"/>
                                </StackPanel>

                                <!-- العميل -->
                                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,10">
                                    <TextBlock Text="العميل"
                                               Margin="0,0,0,5"
                                               Foreground="{DynamicResource SecondaryTextColor}"/>
                                    <ComboBox ItemsSource="{Binding Customers}"
                                              SelectedItem="{Binding SelectedCustomer}"
                                              DisplayMemberPath="Name"
                                              BorderBrush="{DynamicResource BorderColor}"
                                              BorderThickness="1"
                                              Padding="5"/>
                                </StackPanel>

                                <!-- الحالة -->
                                <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,10">
                                    <TextBlock Text="الحالة"
                                               Margin="0,0,0,5"
                                               Foreground="{DynamicResource SecondaryTextColor}"/>
                                    <ComboBox ItemsSource="{Binding InvoiceStatuses}"
                                              SelectedItem="{Binding SelectedInvoiceStatus}"
                                              DisplayMemberPath="Name"
                                              BorderBrush="{DynamicResource BorderColor}"
                                              BorderThickness="1"
                                              Padding="5"/>
                                </StackPanel>
                            </Grid>

                            <!-- عناصر الفاتورة -->
                            <TextBlock Text="عناصر الفاتورة"
                                       FontWeight="SemiBold"
                                       Margin="0,0,0,10"
                                       Foreground="{DynamicResource TextColor}"/>

                            <DataGrid ItemsSource="{Binding CurrentInvoice.Items}"
                                      AutoGenerateColumns="False"
                                      IsReadOnly="True"
                                      BorderThickness="1"
                                      BorderBrush="{DynamicResource BorderColor}"
                                      Background="{DynamicResource CardBackgroundColor}"
                                      RowBackground="{DynamicResource CardBackgroundColor}"
                                      AlternatingRowBackground="{DynamicResource BackgroundColor}"
                                      HeadersVisibility="Column"
                                      GridLinesVisibility="Horizontal"
                                      CanUserSortColumns="False"
                                      CanUserResizeColumns="True"
                                      SelectionMode="Single"
                                      SelectionUnit="FullRow"
                                      Height="200"
                                      Margin="0,0,0,10">
                                <DataGrid.Resources>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="{DynamicResource PrimaryColor}"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="Padding" Value="10,5"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                    </Style>
                                </DataGrid.Resources>
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="المنتج" Binding="{Binding ProductName}" Width="*"/>
                                    <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity, StringFormat={}{0:N2}}" Width="80"/>
                                    <DataGridTextColumn Header="السعر" Binding="{Binding Price, StringFormat={}{0:N2}}" Width="100"/>
                                    <DataGridTextColumn Header="الخصم" Binding="{Binding Discount, StringFormat={}{0:N2}}" Width="80"/>
                                    <DataGridTextColumn Header="الضريبة" Binding="{Binding Tax, StringFormat={}{0:N2}}" Width="80"/>
                                    <DataGridTextColumn Header="الإجمالي" Binding="{Binding Total, StringFormat={}{0:N2}}" Width="100"/>
                                    <DataGridTemplateColumn Width="50">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Button Content="×"
                                                        Command="{Binding DataContext.RemoveItemCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                        CommandParameter="{Binding}"
                                                        Style="{StaticResource RedButton}"
                                                        Padding="5,0"
                                                        FontSize="16"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>

                            <!-- إضافة عنصر -->
                            <Grid Margin="0,0,0,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="100"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <ComboBox Grid.Column="0"
                                          ItemsSource="{Binding Products}"
                                          SelectedItem="{Binding SelectedProduct}"
                                          DisplayMemberPath="Name"
                                          BorderBrush="{DynamicResource BorderColor}"
                                          BorderThickness="1"
                                          Padding="5"
                                          Margin="0,0,5,0"/>

                                <TextBox Grid.Column="1"
                                         Text="{Binding Quantity, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource DefaultTextBox}"
                                         Margin="0,0,5,0"/>

                                <TextBox Grid.Column="2"
                                         Text="{Binding Price, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource DefaultTextBox}"
                                         Margin="0,0,5,0"/>

                                <TextBox Grid.Column="3"
                                         Text="{Binding Discount, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource DefaultTextBox}"
                                         Margin="0,0,5,0"/>

                                <TextBox Grid.Column="4"
                                         Text="{Binding Tax, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource DefaultTextBox}"
                                         Margin="0,0,5,0"/>

                                <Button Grid.Column="5"
                                        Content="إضافة"
                                        Command="{Binding AddItemCommand}"
                                        Style="{StaticResource GreenButton}"/>
                            </Grid>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- أزرار الإجراءات -->
                    <StackPanel Grid.Row="2"
                                Orientation="Horizontal"
                                HorizontalAlignment="Center"
                                Margin="0,20,0,0">
                        <Button Content="حفظ"
                                Command="{Binding SaveInvoiceCommand}"
                                Style="{StaticResource GreenButton}"
                                Width="100"
                                Margin="0,0,10,0"/>
                        <Button Content="إلغاء"
                                Command="{Binding CancelInvoiceCommand}"
                                Style="{StaticResource RedButton}"
                                Width="100"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Border>
    </Grid>
</UserControl>
