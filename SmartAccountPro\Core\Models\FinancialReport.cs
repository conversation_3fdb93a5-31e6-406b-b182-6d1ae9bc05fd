using System;
using System.Collections.Generic;
using System.Linq;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// نوع التقرير المالي
    /// </summary>
    public enum FinancialReportType
    {
        /// <summary>
        /// قائمة الدخل
        /// </summary>
        IncomeStatement,

        /// <summary>
        /// الميزانية العمومية
        /// </summary>
        BalanceSheet,

        /// <summary>
        /// التدفقات النقدية
        /// </summary>
        CashFlow,

        /// <summary>
        /// تقرير مخصص
        /// </summary>
        Custom
    }

    /// <summary>
    /// فترة التقرير
    /// </summary>
    public enum ReportPeriod
    {
        /// <summary>
        /// شهري
        /// </summary>
        Monthly,

        /// <summary>
        /// ربع سنوي
        /// </summary>
        Quarterly,

        /// <summary>
        /// نصف سنوي
        /// </summary>
        SemiAnnual,

        /// <summary>
        /// سنوي
        /// </summary>
        Annual,

        /// <summary>
        /// مخصص
        /// </summary>
        Custom
    }

    /// <summary>
    /// التقرير المالي
    /// </summary>
    public class FinancialReport
    {
        /// <summary>
        /// معرف التقرير
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// عنوان التقرير
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// نوع التقرير
        /// </summary>
        public FinancialReportType Type { get; set; }

        /// <summary>
        /// فترة التقرير
        /// </summary>
        public ReportPeriod Period { get; set; }

        /// <summary>
        /// تاريخ بداية التقرير
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// تاريخ نهاية التقرير
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ التقرير
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// بنود التقرير
        /// </summary>
        public List<FinancialReportItem> Items { get; set; } = new List<FinancialReportItem>();

        /// <summary>
        /// إجمالي الإيرادات
        /// </summary>
        public decimal TotalRevenue => Items.Where(i => i.Category == "Revenue").Sum(i => i.Amount);

        /// <summary>
        /// إجمالي المصروفات
        /// </summary>
        public decimal TotalExpenses => Items.Where(i => i.Category == "Expense").Sum(i => i.Amount);

        /// <summary>
        /// صافي الربح
        /// </summary>
        public decimal NetProfit => TotalRevenue - TotalExpenses;

        /// <summary>
        /// إجمالي الأصول
        /// </summary>
        public decimal TotalAssets => Items.Where(i => i.Category == "Asset").Sum(i => i.Amount);

        /// <summary>
        /// إجمالي الخصوم
        /// </summary>
        public decimal TotalLiabilities => Items.Where(i => i.Category == "Liability").Sum(i => i.Amount);

        /// <summary>
        /// إجمالي حقوق الملكية
        /// </summary>
        public decimal TotalEquity => Items.Where(i => i.Category == "Equity").Sum(i => i.Amount);

        /// <summary>
        /// التوازن (الأصول = الخصوم + حقوق الملكية)
        /// </summary>
        public bool IsBalanced => Math.Abs(TotalAssets - (TotalLiabilities + TotalEquity)) < 0.01m;

        /// <summary>
        /// حالة التقرير
        /// </summary>
        public string Status { get; set; } = "Draft";

        /// <summary>
        /// هل تم نشر التقرير
        /// </summary>
        public bool IsPublished { get; set; }

        /// <summary>
        /// تاريخ النشر
        /// </summary>
        public DateTime? PublishedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي نشر التقرير
        /// </summary>
        public int? PublishedBy { get; set; }
    }

    /// <summary>
    /// بند التقرير المالي
    /// </summary>
    public class FinancialReportItem
    {
        /// <summary>
        /// معرف البند
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// معرف التقرير
        /// </summary>
        public int ReportId { get; set; }

        /// <summary>
        /// اسم البند
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// وصف البند
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// فئة البند (الإيرادات، المصروفات، الأصول، الخصوم، حقوق الملكية)
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// المبلغ
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// معرف الحساب المرتبط
        /// </summary>
        public int? AccountId { get; set; }

        /// <summary>
        /// هل هو عنوان
        /// </summary>
        public bool IsHeader { get; set; }

        /// <summary>
        /// هل هو إجمالي
        /// </summary>
        public bool IsTotal { get; set; }

        /// <summary>
        /// مستوى العمق
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// معرف العنصر الأب
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// العناصر الفرعية
        /// </summary>
        public List<FinancialReportItem> Children { get; set; } = new List<FinancialReportItem>();
    }
}
