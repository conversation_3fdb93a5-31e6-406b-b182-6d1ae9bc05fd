using SmartAccountPro.Core.Models;
using SmartAccountPro.Data.Services;
using SmartAccountPro.Helpers;
using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// نموذج عرض التقارير المالية
    /// </summary>
    public class FinancialReportsViewModel : ViewModelBase
    {
        private readonly FinancialReportService _reportService;
        private ObservableCollection<FinancialReport> _reports;
        private FinancialReport _selectedReport;
        private bool _isLoading;
        private string _searchText = string.Empty;
        private FinancialReportType _selectedReportType;
        private ReportPeriod _selectedPeriod;
        private DateTime _startDate;
        private DateTime _endDate;

        /// <summary>
        /// الأمر: إنشاء تقرير جديد
        /// </summary>
        public RelayCommand CreateReportCommand { get; }

        /// <summary>
        /// الأمر: تحرير التقرير
        /// </summary>
        public RelayCommand EditReportCommand { get; }

        /// <summary>
        /// الأمر: حذف التقرير
        /// </summary>
        public RelayCommand DeleteReportCommand { get; }

        /// <summary>
        /// الأمر: تحديث
        /// </summary>
        public RelayCommand RefreshCommand { get; }

        /// <summary>
        /// الأمر: طباعة
        /// </summary>
        public RelayCommand PrintReportCommand { get; }

        /// <summary>
        /// الأمر: تصدير
        /// </summary>
        public RelayCommand ExportReportCommand { get; }

        /// <summary>
        /// الأمر: نشر التقرير
        /// </summary>
        public RelayCommand PublishReportCommand { get; }

        /// <summary>
        /// الأمر: البحث
        /// </summary>
        public RelayCommand SearchCommand { get; }

        /// <summary>
        /// قائمة التقارير المالية
        /// </summary>
        public ObservableCollection<FinancialReport> Reports
        {
            get => _reports;
            set
            {
                _reports = value;
                OnPropertyChanged(nameof(Reports));
            }
        }

        /// <summary>
        /// التقرير المحدد
        /// </summary>
        public FinancialReport SelectedReport
        {
            get => _selectedReport;
            set
            {
                _selectedReport = value;
                OnPropertyChanged(nameof(SelectedReport));
                OnPropertyChanged(nameof(IsReportSelected));
            }
        }

        /// <summary>
        /// هل تم تحديد تقرير
        /// </summary>
        public bool IsReportSelected => SelectedReport != null;

        /// <summary>
        /// هل يتم التحميل
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged(nameof(IsLoading));
            }
        }

        /// <summary>
        /// نص البحث
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged(nameof(SearchText));
            }
        }

        /// <summary>
        /// نوع التقرير المحدد
        /// </summary>
        public FinancialReportType SelectedReportType
        {
            get => _selectedReportType;
            set
            {
                _selectedReportType = value;
                OnPropertyChanged(nameof(SelectedReportType));
            }
        }

        /// <summary>
        /// الفترة المحددة
        /// </summary>
        public ReportPeriod SelectedPeriod
        {
            get => _selectedPeriod;
            set
            {
                _selectedPeriod = value;
                OnPropertyChanged(nameof(SelectedPeriod));
            }
        }

        /// <summary>
        /// تاريخ البداية
        /// </summary>
        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                _startDate = value;
                OnPropertyChanged(nameof(StartDate));
            }
        }

        /// <summary>
        /// تاريخ النهاية
        /// </summary>
        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                _endDate = value;
                OnPropertyChanged(nameof(EndDate));
            }
        }

        /// <summary>
        /// إنشاء نسخة جديدة من نموذج عرض التقارير المالية
        /// </summary>
        public FinancialReportsViewModel()
        {
            // تهيئة الخدمات
            _reportService = new FinancialReportService();

            // تهيئة المجموعات
            _reports = new ObservableCollection<FinancialReport>();
            _selectedReport = new FinancialReport();

            // تهيئة التواريخ
            _startDate = new DateTime(DateTime.Now.Year, 1, 1);
            _endDate = DateTime.Now;

            // تهيئة الأوامر
            CreateReportCommand = new RelayCommand(CreateReport);
            EditReportCommand = new RelayCommand(EditReport, CanEditReport);
            DeleteReportCommand = new RelayCommand(DeleteReport, CanDeleteReport);
            RefreshCommand = new RelayCommand(Refresh);
            PrintReportCommand = new RelayCommand(PrintReport, CanPrintReport);
            ExportReportCommand = new RelayCommand(ExportReport, CanExportReport);
            PublishReportCommand = new RelayCommand(PublishReport, CanPublishReport);
            SearchCommand = new RelayCommand(Search);

            // تحميل التقارير
            LoadReportsAsync();
        }

        /// <summary>
        /// تحميل التقارير
        /// </summary>
        private async void LoadReportsAsync()
        {
            try
            {
                IsLoading = true;

                // قياس وقت تنفيذ العملية
                await PerformanceHelper.MeasureExecutionTimeAsync("FinancialReportsViewModel.LoadReports", async () =>
                {
                    var reports = await _reportService.GetAllReportsAsync();
                    
                    // تحديث المجموعة
                    Reports.Clear();
                    foreach (var report in reports)
                    {
                        Reports.Add(report);
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialReportsViewModel.LoadReports");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// إنشاء تقرير جديد
        /// </summary>
        private void CreateReport(object parameter)
        {
            try
            {
                // هنا يمكن إضافة كود لإنشاء تقرير جديد
                MessageBox.Show("سيتم فتح نافذة إنشاء تقرير جديد", "إنشاء تقرير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialReportsViewModel.CreateReport");
            }
        }

        /// <summary>
        /// تحرير التقرير
        /// </summary>
        private void EditReport(object parameter)
        {
            try
            {
                if (SelectedReport != null)
                {
                    // هنا يمكن إضافة كود لتحرير التقرير
                    MessageBox.Show($"سيتم فتح نافذة تحرير التقرير: {SelectedReport.Title}", "تحرير تقرير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialReportsViewModel.EditReport");
            }
        }

        /// <summary>
        /// هل يمكن تحرير التقرير
        /// </summary>
        private bool CanEditReport(object parameter)
        {
            return SelectedReport != null;
        }

        /// <summary>
        /// حذف التقرير
        /// </summary>
        private async void DeleteReport(object parameter)
        {
            try
            {
                if (SelectedReport != null)
                {
                    // عرض رسالة تأكيد
                    MessageBoxResult result = MessageBox.Show(
                        $"هل أنت متأكد من حذف التقرير: {SelectedReport.Title}؟",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        IsLoading = true;

                        // حذف التقرير
                        bool success = await _reportService.DeleteReportAsync(SelectedReport.Id);

                        if (success)
                        {
                            // إزالة التقرير من القائمة
                            Reports.Remove(SelectedReport);
                            SelectedReport = null!;
                        }

                        IsLoading = false;
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialReportsViewModel.DeleteReport");
                IsLoading = false;
            }
        }

        /// <summary>
        /// هل يمكن حذف التقرير
        /// </summary>
        private bool CanDeleteReport(object parameter)
        {
            return SelectedReport != null;
        }

        /// <summary>
        /// تحديث
        /// </summary>
        private void Refresh(object parameter)
        {
            LoadReportsAsync();
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void PrintReport(object parameter)
        {
            try
            {
                if (SelectedReport != null)
                {
                    // هنا يمكن إضافة كود لطباعة التقرير
                    MessageBox.Show($"سيتم طباعة التقرير: {SelectedReport.Title}", "طباعة تقرير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialReportsViewModel.PrintReport");
            }
        }

        /// <summary>
        /// هل يمكن طباعة التقرير
        /// </summary>
        private bool CanPrintReport(object parameter)
        {
            return SelectedReport != null;
        }

        /// <summary>
        /// تصدير التقرير
        /// </summary>
        private void ExportReport(object parameter)
        {
            try
            {
                if (SelectedReport != null)
                {
                    // هنا يمكن إضافة كود لتصدير التقرير
                    MessageBox.Show($"سيتم تصدير التقرير: {SelectedReport.Title}", "تصدير تقرير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialReportsViewModel.ExportReport");
            }
        }

        /// <summary>
        /// هل يمكن تصدير التقرير
        /// </summary>
        private bool CanExportReport(object parameter)
        {
            return SelectedReport != null;
        }

        /// <summary>
        /// نشر التقرير
        /// </summary>
        private async void PublishReport(object parameter)
        {
            try
            {
                if (SelectedReport != null && !SelectedReport.IsPublished)
                {
                    // عرض رسالة تأكيد
                    MessageBoxResult result = MessageBox.Show(
                        $"هل أنت متأكد من نشر التقرير: {SelectedReport.Title}؟",
                        "تأكيد النشر",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        IsLoading = true;

                        // تحديث حالة التقرير
                        SelectedReport.IsPublished = true;
                        SelectedReport.PublishedAt = DateTime.Now;
                        SelectedReport.PublishedBy = 1; // يمكن استبدالها بمعرف المستخدم الحالي
                        SelectedReport.Status = "Published";

                        // حفظ التغييرات
                        bool success = await _reportService.UpdateReportAsync(SelectedReport);

                        if (success)
                        {
                            // تحديث واجهة المستخدم
                            OnPropertyChanged(nameof(SelectedReport));
                        }

                        IsLoading = false;
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialReportsViewModel.PublishReport");
                IsLoading = false;
            }
        }

        /// <summary>
        /// هل يمكن نشر التقرير
        /// </summary>
        private bool CanPublishReport(object parameter)
        {
            return SelectedReport != null && !SelectedReport.IsPublished;
        }

        /// <summary>
        /// البحث
        /// </summary>
        private void Search(object parameter)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    // إذا كان نص البحث فارغًا، قم بتحميل جميع التقارير
                    LoadReportsAsync();
                    return;
                }

                // البحث في التقارير
                var filteredReports = Reports.Where(r =>
                    r.Title.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    r.Notes.Contains(SearchText, StringComparison.OrdinalIgnoreCase)).ToList();

                // تحديث القائمة
                Reports.Clear();
                foreach (var report in filteredReports)
                {
                    Reports.Add(report);
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialReportsViewModel.Search");
            }
        }
    }
}
