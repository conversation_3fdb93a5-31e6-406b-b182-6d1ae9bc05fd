using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// يمثل قيد محاسبي في دفتر اليومية
    /// </summary>
    public class JournalEntry
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(20)]
        public string EntryNumber { get; set; }

        /// <summary>
        /// تاريخ القيد
        /// </summary>
        public DateTime EntryDate { get; set; } = DateTime.Now;

        /// <summary>
        /// وصف القيد
        /// </summary>
        [MaxLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// مصدر القيد: يدوي، فاتورة، سند قبض، سند صرف، إلخ
        /// </summary>
        public EntrySource Source { get; set; }

        /// <summary>
        /// رقم المرجع (مثل رقم الفاتورة أو رقم السند)
        /// </summary>
        [MaxLength(50)]
        public string ReferenceNumber { get; set; }

        /// <summary>
        /// هل تم ترحيل القيد
        /// </summary>
        public bool IsPosted { get; set; }

        /// <summary>
        /// تاريخ ترحيل القيد
        /// </summary>
        public DateTime? PostedAt { get; set; }

        /// <summary>
        /// المستخدم الذي أنشأ القيد
        /// </summary>
        public int CreatedByUserId { get; set; }
        public virtual User CreatedByUser { get; set; }

        /// <summary>
        /// المستخدم الذي رحل القيد
        /// </summary>
        public int? PostedByUserId { get; set; }
        public virtual User PostedByUser { get; set; }

        /// <summary>
        /// بنود القيد
        /// </summary>
        public virtual ICollection<JournalEntryItem> Items { get; set; }

        /// <summary>
        /// إجمالي المدين
        /// </summary>
        public decimal TotalDebit { get; set; }

        /// <summary>
        /// إجمالي الدائن
        /// </summary>
        public decimal TotalCredit { get; set; }

        /// <summary>
        /// تاريخ إنشاء القيد
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث للقيد
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        public JournalEntry()
        {
            Items = new HashSet<JournalEntryItem>();
        }
    }

    /// <summary>
    /// يمثل بند في قيد محاسبي
    /// </summary>
    public class JournalEntryItem
    {
        public int Id { get; set; }

        /// <summary>
        /// رقم القيد المحاسبي
        /// </summary>
        public int JournalEntryId { get; set; }
        public virtual JournalEntry JournalEntry { get; set; }

        /// <summary>
        /// رقم الحساب
        /// </summary>
        public int AccountId { get; set; }
        public virtual Account Account { get; set; }

        /// <summary>
        /// وصف البند
        /// </summary>
        [MaxLength(255)]
        public string Description { get; set; }

        /// <summary>
        /// المبلغ المدين
        /// </summary>
        public decimal DebitAmount { get; set; }

        /// <summary>
        /// المبلغ الدائن
        /// </summary>
        public decimal CreditAmount { get; set; }
    }

    /// <summary>
    /// مصدر القيد المحاسبي
    /// </summary>
    public enum EntrySource
    {
        /// <summary>
        /// قيد يدوي
        /// </summary>
        Manual = 1,

        /// <summary>
        /// فاتورة مبيعات
        /// </summary>
        SalesInvoice = 2,

        /// <summary>
        /// فاتورة مشتريات
        /// </summary>
        PurchaseInvoice = 3,

        /// <summary>
        /// سند قبض
        /// </summary>
        Receipt = 4,

        /// <summary>
        /// سند صرف
        /// </summary>
        Payment = 5,

        /// <summary>
        /// قيد افتتاحي
        /// </summary>
        OpeningBalance = 6,

        /// <summary>
        /// قيد تسوية
        /// </summary>
        Adjustment = 7
    }
}
