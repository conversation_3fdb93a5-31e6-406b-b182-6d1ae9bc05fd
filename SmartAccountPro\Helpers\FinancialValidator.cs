using System;
using System.Globalization;
using System.Text.RegularExpressions;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للتحقق من صحة البيانات المالية
    /// </summary>
    public static class FinancialValidator
    {
        /// <summary>
        /// التحقق من صحة المبلغ المالي
        /// </summary>
        /// <param name="amount">المبلغ المالي</param>
        /// <returns>صحة المبلغ المالي</returns>
        public static bool IsValidAmount(string? amount)
        {
            if (string.IsNullOrWhiteSpace(amount))
                return false;

            try
            {
                // محاولة تحويل النص إلى قيمة عشرية
                if (decimal.TryParse(amount, NumberStyles.Currency, CultureInfo.CurrentCulture, out decimal result))
                {
                    // التحقق من أن المبلغ ليس سالبًا
                    return result >= 0;
                }
                return false;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialValidator.IsValidAmount");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة رقم الحساب المصرفي
        /// </summary>
        /// <param name="accountNumber">رقم الحساب المصرفي</param>
        /// <returns>صحة رقم الحساب المصرفي</returns>
        public static bool IsValidBankAccountNumber(string? accountNumber)
        {
            if (string.IsNullOrWhiteSpace(accountNumber))
                return false;

            try
            {
                // التعبير النمطي للتحقق من صحة رقم الحساب المصرفي
                string pattern = @"^[0-9]{10,20}$";
                return Regex.IsMatch(accountNumber, pattern);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialValidator.IsValidBankAccountNumber");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة رقم IBAN
        /// </summary>
        /// <param name="iban">رقم IBAN</param>
        /// <returns>صحة رقم IBAN</returns>
        public static bool IsValidIBAN(string? iban)
        {
            if (string.IsNullOrWhiteSpace(iban))
                return false;

            try
            {
                // إزالة المسافات
                iban = iban.Replace(" ", "");

                // التعبير النمطي للتحقق من صحة رقم IBAN
                string pattern = @"^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$";
                return Regex.IsMatch(iban, pattern, RegexOptions.IgnoreCase);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialValidator.IsValidIBAN");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة رقم بطاقة الائتمان
        /// </summary>
        /// <param name="creditCardNumber">رقم بطاقة الائتمان</param>
        /// <returns>صحة رقم بطاقة الائتمان</returns>
        public static bool IsValidCreditCardNumber(string? creditCardNumber)
        {
            if (string.IsNullOrWhiteSpace(creditCardNumber))
                return false;

            try
            {
                // إزالة المسافات والشرطات
                creditCardNumber = creditCardNumber.Replace(" ", "").Replace("-", "");

                // التعبير النمطي للتحقق من صحة رقم بطاقة الائتمان
                string pattern = @"^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|6(?:011|5[0-9][0-9])[0-9]{12}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\d{3})\d{11})$";
                return Regex.IsMatch(creditCardNumber, pattern);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialValidator.IsValidCreditCardNumber");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة رقم الضريبة
        /// </summary>
        /// <param name="taxNumber">رقم الضريبة</param>
        /// <returns>صحة رقم الضريبة</returns>
        public static bool IsValidTaxNumber(string? taxNumber)
        {
            if (string.IsNullOrWhiteSpace(taxNumber))
                return false;

            try
            {
                // التعبير النمطي للتحقق من صحة رقم الضريبة
                string pattern = @"^[0-9]{9,15}$";
                return Regex.IsMatch(taxNumber, pattern);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialValidator.IsValidTaxNumber");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة رقم الفاتورة
        /// </summary>
        /// <param name="invoiceNumber">رقم الفاتورة</param>
        /// <returns>صحة رقم الفاتورة</returns>
        public static bool IsValidInvoiceNumber(string? invoiceNumber)
        {
            if (string.IsNullOrWhiteSpace(invoiceNumber))
                return false;

            try
            {
                // التعبير النمطي للتحقق من صحة رقم الفاتورة
                string pattern = @"^[A-Za-z0-9\-\/]{3,20}$";
                return Regex.IsMatch(invoiceNumber, pattern);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialValidator.IsValidInvoiceNumber");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة رمز العملة
        /// </summary>
        /// <param name="currencyCode">رمز العملة</param>
        /// <returns>صحة رمز العملة</returns>
        public static bool IsValidCurrencyCode(string? currencyCode)
        {
            if (string.IsNullOrWhiteSpace(currencyCode))
                return false;

            try
            {
                // التعبير النمطي للتحقق من صحة رمز العملة
                string pattern = @"^[A-Z]{3}$";
                return Regex.IsMatch(currencyCode, pattern);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialValidator.IsValidCurrencyCode");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة نسبة مئوية
        /// </summary>
        /// <param name="percentage">النسبة المئوية</param>
        /// <returns>صحة النسبة المئوية</returns>
        public static bool IsValidPercentage(string? percentage)
        {
            if (string.IsNullOrWhiteSpace(percentage))
                return false;

            try
            {
                // إزالة علامة النسبة المئوية إن وجدت
                if (percentage.EndsWith("%"))
                {
                    percentage = percentage.Substring(0, percentage.Length - 1);
                }

                // محاولة تحويل النص إلى قيمة عشرية
                if (decimal.TryParse(percentage, NumberStyles.Number, CultureInfo.CurrentCulture, out decimal result))
                {
                    // التحقق من أن النسبة المئوية بين 0 و 100
                    return result >= 0 && result <= 100;
                }
                return false;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialValidator.IsValidPercentage");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة معدل الضريبة
        /// </summary>
        /// <param name="taxRate">معدل الضريبة</param>
        /// <returns>صحة معدل الضريبة</returns>
        public static bool IsValidTaxRate(string? taxRate)
        {
            return IsValidPercentage(taxRate);
        }

        /// <summary>
        /// التحقق من صحة معدل الخصم
        /// </summary>
        /// <param name="discountRate">معدل الخصم</param>
        /// <returns>صحة معدل الخصم</returns>
        public static bool IsValidDiscountRate(string? discountRate)
        {
            return IsValidPercentage(discountRate);
        }

        /// <summary>
        /// التحقق من صحة سعر الصرف
        /// </summary>
        /// <param name="exchangeRate">سعر الصرف</param>
        /// <returns>صحة سعر الصرف</returns>
        public static bool IsValidExchangeRate(string? exchangeRate)
        {
            if (string.IsNullOrWhiteSpace(exchangeRate))
                return false;

            try
            {
                // محاولة تحويل النص إلى قيمة عشرية
                if (decimal.TryParse(exchangeRate, NumberStyles.Number, CultureInfo.CurrentCulture, out decimal result))
                {
                    // التحقق من أن سعر الصرف موجب
                    return result > 0;
                }
                return false;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "FinancialValidator.IsValidExchangeRate");
                return false;
            }
        }
    }
}
