using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using SmartAccountPro.ViewModels;
using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;

namespace SmartAccountPro.Views
{
    /// <summary>
    /// نافذة منبثقة للإشعارات
    /// </summary>
    public partial class NotificationPopup : Window
    {
        private readonly DispatcherTimer _timer;
        private readonly Notification _notification;

        public NotificationPopup(Notification notification)
        {
            InitializeComponent();

            _notification = notification;
            DataContext = new NotificationItemViewModel(notification);

            // تحديد موقع النافذة في أسفل يمين الشاشة
            var workingArea = SystemParameters.WorkArea;
            Left = workingArea.Right - Width - 20;
            Top = workingArea.Bottom - Height - 20;

            // إعداد مؤقت لإغلاق النافذة تلقائياً بعد 5 ثواني
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5)
            };
            _timer.Tick += Timer_Tick;
            _timer.Start();

            // تشغيل تأثير الظهور
            var fadeInStoryboard = FindResource("FadeInStoryboard") as Storyboard;
            fadeInStoryboard?.Begin(this);
        }

        /// <summary>
        /// معالج حدث انتهاء المؤقت
        /// </summary>
        private void Timer_Tick(object sender, EventArgs e)
        {
            _timer.Stop();
            ClosePopup();
        }

        /// <summary>
        /// معالج حدث النقر على زر الإغلاق
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            _timer.Stop();
            ClosePopup();
        }

        /// <summary>
        /// إغلاق النافذة المنبثقة
        /// </summary>
        private void ClosePopup()
        {
            var fadeOutStoryboard = FindResource("FadeOutStoryboard") as Storyboard;
            if (fadeOutStoryboard != null)
            {
                fadeOutStoryboard.Completed += (s, e) => Close();
                fadeOutStoryboard.Begin(this);
            }
            else
            {
                Close();
            }
        }

        /// <summary>
        /// معالج حدث النقر على النافذة
        /// </summary>
        protected override void OnMouseLeftButtonUp(MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonUp(e);

            try
            {
                // تحديد الإشعار كمقروء
                NotificationManager.Instance.MarkAsReadAsync(_notification.Id);

                // إغلاق النافذة
                _timer.Stop();
                ClosePopup();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NotificationPopup.OnMouseLeftButtonUp");
            }
        }
    }
}
