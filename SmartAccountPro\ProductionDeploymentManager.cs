using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using System.Text;
using System.Linq;

namespace SmartAccountPro
{
    /// <summary>
    /// مدير تحضير النشر في الإنتاج الشامل
    /// </summary>
    public class ProductionDeploymentManager
    {
        private readonly string _projectPath;
        private readonly string _outputPath;
        private readonly string _deploymentPath;
        private readonly List<DeploymentStep> _deploymentSteps;

        public ProductionDeploymentManager()
        {
            _projectPath = Directory.GetCurrentDirectory();
            _outputPath = Path.Combine(_projectPath, "publish");
            _deploymentPath = Path.Combine(_projectPath, "deployment");
            _deploymentSteps = new List<DeploymentStep>();
        }

        /// <summary>
        /// تشغيل عملية تحضير النشر الشاملة
        /// </summary>
        public static async Task Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("🚀 SmartAccount Pro - تحضير النشر في الإنتاج الشامل");
            Console.WriteLine("═══════════════════════════════════════════════════════════════");

            var deploymentManager = new ProductionDeploymentManager();
            await deploymentManager.RunProductionDeploymentPreparationAsync();

            Console.WriteLine("\n🎉 انتهى تحضير النشر في الإنتاج!");
            Console.WriteLine("اضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }

        /// <summary>
        /// تشغيل عملية تحضير النشر الشاملة
        /// </summary>
        public async Task RunProductionDeploymentPreparationAsync()
        {
            try
            {
                // تهيئة بيئة النشر
                Console.WriteLine("🔧 المرحلة 1: تهيئة بيئة النشر");
                bool initResult = await InitializeDeploymentEnvironmentAsync();
                LogDeploymentStep("تهيئة بيئة النشر", initResult);

                if (!initResult) return;

                // إنشاء نسخ الإنتاج المحسنة
                Console.WriteLine("\n📦 المرحلة 2: إنشاء نسخ الإنتاج المحسنة");
                bool buildResult = await CreateOptimizedReleaseBuildsAsync();
                LogDeploymentStep("إنشاء نسخ الإنتاج", buildResult);

                // تطوير حزم التثبيت
                Console.WriteLine("\n💿 المرحلة 3: تطوير حزم التثبيت الاحترافية");
                bool installerResult = await CreateProfessionalInstallersAsync();
                LogDeploymentStep("تطوير حزم التثبيت", installerResult);

                // إنشاء التوثيق الشامل
                Console.WriteLine("\n📚 المرحلة 4: إنشاء التوثيق الشامل");
                bool documentationResult = await CreateComprehensiveDocumentationAsync();
                LogDeploymentStep("إنشاء التوثيق", documentationResult);

                // اختبار النشر في بيئات نظيفة
                Console.WriteLine("\n🧪 المرحلة 5: اختبار النشر في بيئات نظيفة");
                bool testingResult = await TestDeploymentInCleanEnvironmentsAsync();
                LogDeploymentStep("اختبار النشر", testingResult);

                // تحضير أدوات الإنتاج
                Console.WriteLine("\n🛠️ المرحلة 6: تحضير أدوات الإنتاج والمراقبة");
                bool toolsResult = await PrepareProductionToolsAsync();
                LogDeploymentStep("تحضير أدوات الإنتاج", toolsResult);

                // التحضير النهائي للإطلاق
                Console.WriteLine("\n🎯 المرحلة 7: التحضير النهائي للإطلاق");
                bool finalResult = await FinalLaunchPreparationAsync();
                LogDeploymentStep("التحضير النهائي", finalResult);

                // إنشاء تقرير النشر الشامل
                await GenerateDeploymentReportAsync();

                // عرض النتائج النهائية
                await DisplayFinalResultsAsync();

            }
            catch (Exception ex)
            {
                Console.WriteLine($"💥 خطأ عام في تحضير النشر: {ex.Message}");
            }
        }

        /// <summary>
        /// تهيئة بيئة النشر
        /// </summary>
        private async Task<bool> InitializeDeploymentEnvironmentAsync()
        {
            try
            {
                // إنشاء مجلدات النشر
                Console.WriteLine("   📁 إنشاء مجلدات النشر...");
                CreateDeploymentDirectories();

                // تنظيف المجلدات السابقة
                Console.WriteLine("   🧹 تنظيف المجلدات السابقة...");
                CleanPreviousBuilds();

                // التحقق من متطلبات النشر
                Console.WriteLine("   ✅ التحقق من متطلبات النشر...");
                bool requirementsCheck = await CheckDeploymentRequirementsAsync();

                // تهيئة ملفات التكوين
                Console.WriteLine("   ⚙️ تهيئة ملفات التكوين...");
                await InitializeConfigurationFilesAsync();

                Console.WriteLine("   ✅ تم تهيئة بيئة النشر بنجاح");
                return requirementsCheck;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في تهيئة بيئة النشر: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء مجلدات النشر
        /// </summary>
        private void CreateDeploymentDirectories()
        {
            var directories = new[]
            {
                _outputPath,
                _deploymentPath,
                Path.Combine(_deploymentPath, "installers"),
                Path.Combine(_deploymentPath, "documentation"),
                Path.Combine(_deploymentPath, "scripts"),
                Path.Combine(_deploymentPath, "tools"),
                Path.Combine(_deploymentPath, "tests"),
                Path.Combine(_deploymentPath, "releases")
            };

            foreach (var dir in directories)
            {
                if (!Directory.Exists(dir))
                {
                    Directory.CreateDirectory(dir);
                    Console.WriteLine($"      📁 تم إنشاء مجلد: {Path.GetFileName(dir)}");
                }
            }
        }

        /// <summary>
        /// تنظيف المجلدات السابقة
        /// </summary>
        private void CleanPreviousBuilds()
        {
            try
            {
                if (Directory.Exists(_outputPath))
                {
                    Directory.Delete(_outputPath, true);
                    Console.WriteLine("      🧹 تم تنظيف مجلد النشر السابق");
                }

                var oldReleases = Path.Combine(_deploymentPath, "releases");
                if (Directory.Exists(oldReleases))
                {
                    var oldFiles = Directory.GetFiles(oldReleases, "*", SearchOption.AllDirectories);
                    foreach (var file in oldFiles.Where(f => File.GetCreationTime(f) < DateTime.Now.AddDays(-7)))
                    {
                        File.Delete(file);
                    }
                    Console.WriteLine("      🧹 تم تنظيف الإصدارات القديمة");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ⚠️ تحذير في التنظيف: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من متطلبات النشر
        /// </summary>
        private async Task<bool> CheckDeploymentRequirementsAsync()
        {
            var requirements = new List<(string Name, bool Status)>();

            // التحقق من .NET SDK
            var dotnetCheck = await CheckDotNetSDKAsync();
            requirements.Add((".NET 6.0 SDK", dotnetCheck));

            // التحقق من MSBuild
            var msbuildCheck = CheckMSBuildAvailability();
            requirements.Add(("MSBuild", msbuildCheck));

            // التحقق من مساحة القرص
            var diskSpaceCheck = CheckDiskSpace();
            requirements.Add(("مساحة القرص (>1GB)", diskSpaceCheck));

            // التحقق من صلاحيات الكتابة
            var permissionsCheck = CheckWritePermissions();
            requirements.Add(("صلاحيات الكتابة", permissionsCheck));

            // عرض النتائج
            foreach (var (name, status) in requirements)
            {
                var statusIcon = status ? "✅" : "❌";
                Console.WriteLine($"      {statusIcon} {name}: {(status ? "متوفر" : "غير متوفر")}");
            }

            return requirements.All(r => r.Status);
        }

        /// <summary>
        /// التحقق من .NET SDK
        /// </summary>
        private async Task<bool> CheckDotNetSDKAsync()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "dotnet",
                        Arguments = "--version",
                        RedirectStandardOutput = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                await process.WaitForExitAsync();

                return process.ExitCode == 0 && !string.IsNullOrEmpty(output);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من MSBuild
        /// </summary>
        private bool CheckMSBuildAvailability()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "dotnet",
                        Arguments = "build --help",
                        RedirectStandardOutput = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                process.WaitForExit(5000);
                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من مساحة القرص
        /// </summary>
        private bool CheckDiskSpace()
        {
            try
            {
                var drive = new DriveInfo(Path.GetPathRoot(_projectPath)!);
                var availableSpaceGB = drive.AvailableFreeSpace / (1024.0 * 1024.0 * 1024.0);
                return availableSpaceGB > 1.0; // يتطلب أكثر من 1 GB
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صلاحيات الكتابة
        /// </summary>
        private bool CheckWritePermissions()
        {
            try
            {
                var testFile = Path.Combine(_projectPath, "test_write_permission.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تهيئة ملفات التكوين
        /// </summary>
        private async Task InitializeConfigurationFilesAsync()
        {
            // إنشاء ملف تكوين النشر
            var deploymentConfig = new
            {
                Version = "1.0.0",
                BuildDate = DateTime.Now,
                Configuration = "Release",
                TargetFramework = "net6.0-windows",
                OutputPath = _outputPath,
                Features = new[]
                {
                    "Accounting",
                    "Invoicing", 
                    "Reporting",
                    "UserManagement",
                    "BackupRestore"
                }
            };

            var configPath = Path.Combine(_deploymentPath, "deployment-config.json");
            await File.WriteAllTextAsync(configPath, 
                System.Text.Json.JsonSerializer.Serialize(deploymentConfig, new System.Text.Json.JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                }));

            Console.WriteLine("      ⚙️ تم إنشاء ملف تكوين النشر");
        }

        /// <summary>
        /// طباعة خطوة النشر
        /// </summary>
        private void LogDeploymentStep(string stepName, bool success)
        {
            var step = new DeploymentStep
            {
                Name = stepName,
                Success = success,
                Timestamp = DateTime.Now
            };

            _deploymentSteps.Add(step);

            string status = success ? "✅ نجح" : "❌ فشل";
            Console.WriteLine($"   🎯 {stepName}: {status}");
        }

        /// <summary>
        /// إنشاء نسخ الإنتاج المحسنة
        /// </summary>
        private async Task<bool> CreateOptimizedReleaseBuildsAsync()
        {
            try
            {
                // إنشاء نسخة Release محسنة
                Console.WriteLine("   🔨 إنشاء نسخة Release محسنة...");
                bool releaseResult = await BuildReleaseVersionAsync();

                // إنشاء نسخة Self-Contained
                Console.WriteLine("   📦 إنشاء نسخة Self-Contained...");
                bool selfContainedResult = await BuildSelfContainedVersionAsync();

                // إنشاء نسخة Framework-Dependent
                Console.WriteLine("   🔗 إنشاء نسخة Framework-Dependent...");
                bool frameworkDependentResult = await BuildFrameworkDependentVersionAsync();

                // تحسين الملفات المنشورة
                Console.WriteLine("   ⚡ تحسين الملفات المنشورة...");
                bool optimizationResult = await OptimizePublishedFilesAsync();

                // إنشاء checksums للتحقق من التكامل
                Console.WriteLine("   🔐 إنشاء checksums للتحقق من التكامل...");
                await CreateIntegrityChecksumsAsync();

                return releaseResult && selfContainedResult && frameworkDependentResult && optimizationResult;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في إنشاء نسخ الإنتاج: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// بناء نسخة Release
        /// </summary>
        private async Task<bool> BuildReleaseVersionAsync()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "dotnet",
                        Arguments = $"publish SmartAccountPro.csproj -c Release -o \"{_outputPath}\\Release\" --self-contained false",
                        WorkingDirectory = _projectPath,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                var error = await process.StandardError.ReadToEndAsync();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    Console.WriteLine("      ✅ تم بناء نسخة Release بنجاح");
                    return true;
                }
                else
                {
                    Console.WriteLine($"      ❌ فشل في بناء نسخة Release: {error}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ❌ خطأ في بناء نسخة Release: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// بناء نسخة Self-Contained
        /// </summary>
        private async Task<bool> BuildSelfContainedVersionAsync()
        {
            try
            {
                var runtimes = new[] { "win-x64", "win-x86" };
                bool allSuccessful = true;

                foreach (var runtime in runtimes)
                {
                    var outputDir = Path.Combine(_outputPath, $"SelfContained-{runtime}");

                    var process = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = "dotnet",
                            Arguments = $"publish SmartAccountPro.csproj -c Release -r {runtime} --self-contained true -o \"{outputDir}\"",
                            WorkingDirectory = _projectPath,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true,
                            UseShellExecute = false,
                            CreateNoWindow = true
                        }
                    };

                    process.Start();
                    await process.WaitForExitAsync();

                    if (process.ExitCode == 0)
                    {
                        Console.WriteLine($"      ✅ تم بناء نسخة Self-Contained {runtime}");
                    }
                    else
                    {
                        Console.WriteLine($"      ❌ فشل في بناء نسخة Self-Contained {runtime}");
                        allSuccessful = false;
                    }
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ❌ خطأ في بناء نسخة Self-Contained: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// بناء نسخة Framework-Dependent
        /// </summary>
        private async Task<bool> BuildFrameworkDependentVersionAsync()
        {
            try
            {
                var outputDir = Path.Combine(_outputPath, "FrameworkDependent");

                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "dotnet",
                        Arguments = $"publish SmartAccountPro.csproj -c Release --self-contained false -o \"{outputDir}\"",
                        WorkingDirectory = _projectPath,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    Console.WriteLine("      ✅ تم بناء نسخة Framework-Dependent");
                    return true;
                }
                else
                {
                    Console.WriteLine("      ❌ فشل في بناء نسخة Framework-Dependent");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ❌ خطأ في بناء نسخة Framework-Dependent: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحسين الملفات المنشورة
        /// </summary>
        private async Task<bool> OptimizePublishedFilesAsync()
        {
            try
            {
                // حذف الملفات غير الضرورية
                await RemoveUnnecessaryFilesAsync();

                // ضغط الملفات الكبيرة
                await CompressLargeFilesAsync();

                // تحسين ملفات التكوين
                await OptimizeConfigurationFilesAsync();

                Console.WriteLine("      ✅ تم تحسين الملفات المنشورة");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ❌ خطأ في تحسين الملفات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف الملفات غير الضرورية
        /// </summary>
        private async Task RemoveUnnecessaryFilesAsync()
        {
            var unnecessaryExtensions = new[] { ".pdb", ".xml", ".dev.json" };
            var outputDirectories = Directory.GetDirectories(_outputPath, "*", SearchOption.TopDirectoryOnly);

            foreach (var dir in outputDirectories)
            {
                foreach (var ext in unnecessaryExtensions)
                {
                    var files = Directory.GetFiles(dir, $"*{ext}", SearchOption.AllDirectories);
                    foreach (var file in files)
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // تجاهل الأخطاء في حذف الملفات
                        }
                    }
                }
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// ضغط الملفات الكبيرة
        /// </summary>
        private async Task CompressLargeFilesAsync()
        {
            // يمكن إضافة ضغط للملفات الكبيرة هنا إذا لزم الأمر
            await Task.CompletedTask;
        }

        /// <summary>
        /// تحسين ملفات التكوين
        /// </summary>
        private async Task OptimizeConfigurationFilesAsync()
        {
            var outputDirectories = Directory.GetDirectories(_outputPath, "*", SearchOption.TopDirectoryOnly);

            foreach (var dir in outputDirectories)
            {
                var configFiles = Directory.GetFiles(dir, "appsettings*.json", SearchOption.AllDirectories);
                foreach (var configFile in configFiles)
                {
                    try
                    {
                        var content = await File.ReadAllTextAsync(configFile);
                        // يمكن إضافة تحسينات على ملفات التكوين هنا
                        // مثل إزالة التعليقات أو ضغط JSON
                    }
                    catch
                    {
                        // تجاهل الأخطاء في تحسين ملفات التكوين
                    }
                }
            }
        }

        /// <summary>
        /// إنشاء checksums للتحقق من التكامل
        /// </summary>
        private async Task CreateIntegrityChecksumsAsync()
        {
            var outputDirectories = Directory.GetDirectories(_outputPath, "*", SearchOption.TopDirectoryOnly);

            foreach (var dir in outputDirectories)
            {
                var checksumFile = Path.Combine(dir, "checksums.txt");
                var checksums = new StringBuilder();

                var files = Directory.GetFiles(dir, "*.*", SearchOption.AllDirectories)
                    .Where(f => !f.EndsWith("checksums.txt"));

                foreach (var file in files)
                {
                    try
                    {
                        using var md5 = System.Security.Cryptography.MD5.Create();
                        using var stream = File.OpenRead(file);
                        var hash = md5.ComputeHash(stream);
                        var hashString = Convert.ToHexString(hash);

                        var relativePath = Path.GetRelativePath(dir, file);
                        checksums.AppendLine($"{hashString}  {relativePath}");
                    }
                    catch
                    {
                        // تجاهل الأخطاء في حساب checksum
                    }
                }

                await File.WriteAllTextAsync(checksumFile, checksums.ToString());
            }
        }
    }

    /// <summary>
    /// خطوة النشر
    /// </summary>
    public class DeploymentStep
    {
        public string Name { get; set; } = string.Empty;
        public bool Success { get; set; }
        public DateTime Timestamp { get; set; }
        public string? Notes { get; set; }
    }
}
