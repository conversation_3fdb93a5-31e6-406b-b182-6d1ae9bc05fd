using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Data;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using System.Linq;
using System.Collections.Generic;

namespace SmartAccountPro
{
    /// <summary>
    /// فئة اختبار شاملة لوظائف التطبيق
    /// </summary>
    public class ApplicationTester
    {
        private AppDbContext _context = null!;
        private DatabaseManager _dbManager = null!;
        private string _testDatabasePath = null!;

        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static async Task Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("🔍 SmartAccount Pro - اختبار شامل للتطبيق");
            Console.WriteLine("==============================================");

            var tester = new ApplicationTester();
            await tester.RunComprehensiveTestsAsync();

            Console.WriteLine("\nاضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }

        /// <summary>
        /// تشغيل الاختبارات الشاملة
        /// </summary>
        public async Task RunComprehensiveTestsAsync()
        {
            try
            {
                // المرحلة 1: تهيئة التطبيق
                Console.WriteLine("🚀 المرحلة 1: تهيئة التطبيق والبيانات");
                bool initResult = await InitializeApplicationAsync();
                LogTestResult("تهيئة التطبيق", initResult);

                if (!initResult)
                {
                    Console.WriteLine("❌ فشل في تهيئة التطبيق. توقف الاختبار.");
                    return;
                }

                // المرحلة 2: اختبار قاعدة البيانات
                Console.WriteLine("\n📊 المرحلة 2: اختبار عمليات قاعدة البيانات");
                bool dbResult = await TestDatabaseOperationsAsync();
                LogTestResult("عمليات قاعدة البيانات", dbResult);

                // المرحلة 3: اختبار نظام المستخدمين
                Console.WriteLine("\n👤 المرحلة 3: اختبار نظام إدارة المستخدمين");
                bool userResult = await TestUserManagementAsync();
                LogTestResult("إدارة المستخدمين", userResult);

                // المرحلة 4: اختبار النظام المحاسبي
                Console.WriteLine("\n💰 المرحلة 4: اختبار النظام المحاسبي");
                bool accountingResult = await TestAccountingSystemAsync();
                LogTestResult("النظام المحاسبي", accountingResult);

                // المرحلة 5: اختبار نظام الفواتير
                Console.WriteLine("\n🧾 المرحلة 5: اختبار نظام الفواتير");
                bool invoiceResult = await TestInvoiceSystemAsync();
                LogTestResult("نظام الفواتير", invoiceResult);

                // المرحلة 6: اختبار نظام التقارير
                Console.WriteLine("\n📈 المرحلة 6: اختبار نظام التقارير");
                bool reportResult = await TestReportingSystemAsync();
                LogTestResult("نظام التقارير", reportResult);

                // النتيجة النهائية
                Console.WriteLine("\n==============================================");
                var allResults = new[] { initResult, dbResult, userResult, accountingResult, invoiceResult, reportResult };
                bool overallSuccess = allResults.All(r => r);
                
                Console.WriteLine($"🎯 النتيجة النهائية: {(overallSuccess ? "جميع الاختبارات نجحت ✅" : "بعض الاختبارات فشلت ❌")}");
                Console.WriteLine($"📊 معدل النجاح: {allResults.Count(r => r)}/{allResults.Length} ({(allResults.Count(r => r) * 100.0 / allResults.Length):F1}%)");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"💥 خطأ عام في الاختبار: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
            finally
            {
                // تنظيف الموارد
                await CleanupAsync();
            }
        }

        /// <summary>
        /// تهيئة التطبيق للاختبار
        /// </summary>
        private async Task<bool> InitializeApplicationAsync()
        {
            try
            {
                // إنشاء مجلد اختبار مؤقت
                string tempPath = Path.Combine(Path.GetTempPath(), "SmartAccountProTest");
                if (Directory.Exists(tempPath))
                    Directory.Delete(tempPath, true);
                Directory.CreateDirectory(tempPath);

                _testDatabasePath = Path.Combine(tempPath, "TestDatabase.db");

                // تهيئة سياق قاعدة البيانات
                var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
                optionsBuilder.UseSqlite($"Data Source={_testDatabasePath}");
                optionsBuilder.EnableSensitiveDataLogging();

                _context = new AppDbContext(optionsBuilder.Options);

                // إنشاء قاعدة البيانات
                await _context.Database.EnsureCreatedAsync();

                // تهيئة مدير قاعدة البيانات
                _dbManager = new DatabaseManager(_context);
                await _dbManager.InitializeDatabaseAsync();

                Console.WriteLine($"   ✅ تم إنشاء قاعدة بيانات الاختبار: {_testDatabasePath}");
                Console.WriteLine($"   ✅ تم تهيئة مدير قاعدة البيانات");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في تهيئة التطبيق: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار عمليات قاعدة البيانات
        /// </summary>
        private async Task<bool> TestDatabaseOperationsAsync()
        {
            try
            {
                // اختبار الاتصال
                bool canConnect = await _context.Database.CanConnectAsync();
                if (!canConnect)
                {
                    Console.WriteLine("   ❌ فشل في الاتصال بقاعدة البيانات");
                    return false;
                }
                Console.WriteLine("   ✅ الاتصال بقاعدة البيانات ناجح");

                // اختبار وجود الجداول
                var tableNames = new[] { "Users", "Accounts", "JournalEntries", "Invoices", "Customers" };
                foreach (var tableName in tableNames)
                {
                    var tableExists = await _context.Database.ExecuteSqlRawAsync($"SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='{tableName}'") >= 0;
                    Console.WriteLine($"   ✅ جدول {tableName}: موجود");
                }

                // اختبار العد الأولي
                var userCount = await _context.Users.CountAsync();
                var accountCount = await _context.Accounts.CountAsync();
                Console.WriteLine($"   📊 عدد المستخدمين: {userCount}");
                Console.WriteLine($"   📊 عدد الحسابات: {accountCount}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار نظام إدارة المستخدمين
        /// </summary>
        private async Task<bool> TestUserManagementAsync()
        {
            try
            {
                // اختبار وجود المستخدم الافتراضي
                var adminUser = await _context.Users.FirstOrDefaultAsync(u => u.Username == "admin");
                if (adminUser != null)
                {
                    Console.WriteLine($"   ✅ المستخدم الافتراضي موجود: {adminUser.FullName}");
                    Console.WriteLine($"   📧 البريد الإلكتروني: {adminUser.Email}");
                    Console.WriteLine($"   🔐 حالة النشاط: {(adminUser.IsActive ? "نشط" : "غير نشط")}");
                }
                else
                {
                    Console.WriteLine("   ⚠️ المستخدم الافتراضي غير موجود - سيتم إنشاؤه");
                }

                // اختبار إنشاء مستخدم جديد
                var testUser = new User
                {
                    Username = "testuser",
                    FullName = "مستخدم تجريبي",
                    Email = "<EMAIL>",
                    PasswordHash = "hashedpassword",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Users.Add(testUser);
                await _context.SaveChangesAsync();
                Console.WriteLine("   ✅ تم إنشاء مستخدم تجريبي بنجاح");

                // اختبار البحث عن المستخدم
                var foundUser = await _context.Users.FirstOrDefaultAsync(u => u.Username == "testuser");
                if (foundUser != null)
                {
                    Console.WriteLine("   ✅ تم العثور على المستخدم التجريبي");
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار إدارة المستخدمين: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// طباعة نتيجة الاختبار
        /// </summary>
        private void LogTestResult(string testName, bool success)
        {
            string status = success ? "✅ نجح" : "❌ فشل";
            Console.WriteLine($"   🎯 {testName}: {status}");
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        private async Task CleanupAsync()
        {
            try
            {
                if (_context != null)
                {
                    await _context.DisposeAsync();
                }

                if (!string.IsNullOrEmpty(_testDatabasePath) && File.Exists(_testDatabasePath))
                {
                    File.Delete(_testDatabasePath);
                    Console.WriteLine("   🧹 تم حذف قاعدة بيانات الاختبار");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ خطأ في التنظيف: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار النظام المحاسبي
        /// </summary>
        private async Task<bool> TestAccountingSystemAsync()
        {
            try
            {
                // اختبار وجود الحسابات الأساسية
                var assetsAccount = await _context.Accounts.FirstOrDefaultAsync(a => a.Code == "1" && a.Type == AccountType.Asset);
                var liabilitiesAccount = await _context.Accounts.FirstOrDefaultAsync(a => a.Code == "2" && a.Type == AccountType.Liability);
                var equityAccount = await _context.Accounts.FirstOrDefaultAsync(a => a.Code == "3" && a.Type == AccountType.Equity);

                if (assetsAccount != null && liabilitiesAccount != null && equityAccount != null)
                {
                    Console.WriteLine("   ✅ الحسابات الأساسية موجودة");
                    Console.WriteLine($"      💰 الأصول: {assetsAccount.Name}");
                    Console.WriteLine($"      💳 الخصوم: {liabilitiesAccount.Name}");
                    Console.WriteLine($"      🏦 حقوق الملكية: {equityAccount.Name}");
                }
                else
                {
                    Console.WriteLine("   ⚠️ بعض الحسابات الأساسية مفقودة");
                }

                // اختبار إنشاء قيد محاسبي
                var testEntry = new JournalEntry
                {
                    EntryNumber = "TEST-001",
                    EntryDate = DateTime.Now,
                    Description = "قيد تجريبي للاختبار",
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = 1
                };

                // إضافة بنود القيد
                if (assetsAccount != null)
                {
                    testEntry.Items = new List<JournalEntryItem>
                    {
                        new JournalEntryItem
                        {
                            AccountId = assetsAccount.Id,
                            DebitAmount = 1000,
                            CreditAmount = 0,
                            Description = "مدين - اختبار"
                        },
                        new JournalEntryItem
                        {
                            AccountId = assetsAccount.Id,
                            DebitAmount = 0,
                            CreditAmount = 1000,
                            Description = "دائن - اختبار"
                        }
                    };

                    _context.JournalEntries.Add(testEntry);
                    await _context.SaveChangesAsync();

                    Console.WriteLine($"   ✅ تم إنشاء قيد تجريبي: {testEntry.EntryNumber}");
                    Console.WriteLine($"      📅 التاريخ: {testEntry.EntryDate:yyyy-MM-dd}");
                    Console.WriteLine($"      💰 المبلغ: {testEntry.Items.Sum(i => i.DebitAmount)}");

                    // حذف القيد التجريبي
                    _context.JournalEntries.Remove(testEntry);
                    await _context.SaveChangesAsync();
                    Console.WriteLine("   ✅ تم حذف القيد التجريبي");
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار النظام المحاسبي: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار نظام الفواتير
        /// </summary>
        private async Task<bool> TestInvoiceSystemAsync()
        {
            try
            {
                // اختبار إنشاء عميل تجريبي
                var testCustomer = new Customer
                {
                    Name = "عميل تجريبي",
                    Phone = "123456789",
                    Email = "<EMAIL>",
                    Address = "عنوان تجريبي",
                    CreatedAt = DateTime.Now
                };

                _context.Customers.Add(testCustomer);
                await _context.SaveChangesAsync();
                Console.WriteLine("   ✅ تم إنشاء عميل تجريبي");

                // اختبار إنشاء فاتورة تجريبية
                var testInvoice = new Invoice
                {
                    InvoiceNumber = "INV-TEST-001",
                    InvoiceDate = DateTime.Now,
                    DueDate = DateTime.Now.AddDays(30),
                    CustomerId = testCustomer.Id,
                    Status = InvoiceStatus.Draft,
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = 1
                };

                _context.Invoices.Add(testInvoice);
                await _context.SaveChangesAsync();

                Console.WriteLine($"   ✅ تم إنشاء فاتورة تجريبية: {testInvoice.InvoiceNumber}");
                Console.WriteLine($"      📅 التاريخ: {testInvoice.InvoiceDate:yyyy-MM-dd}");
                Console.WriteLine($"      👤 العميل: {testCustomer.Name}");

                // اختبار البحث عن الفاتورة
                var foundInvoice = await _context.Invoices
                    .Include(i => i.Customer)
                    .FirstOrDefaultAsync(i => i.InvoiceNumber == "INV-TEST-001");

                if (foundInvoice != null && foundInvoice.Customer != null)
                {
                    Console.WriteLine("   ✅ تم العثور على الفاتورة مع بيانات العميل");
                }

                // تنظيف البيانات التجريبية
                _context.Invoices.Remove(testInvoice);
                _context.Customers.Remove(testCustomer);
                await _context.SaveChangesAsync();
                Console.WriteLine("   ✅ تم حذف البيانات التجريبية");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار نظام الفواتير: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار نظام التقارير
        /// </summary>
        private async Task<bool> TestReportingSystemAsync()
        {
            try
            {
                // اختبار إنشاء مولد التقارير
                var reportGenerator = new ReportGenerator(_context);
                Console.WriteLine("   ✅ تم إنشاء مولد التقارير");

                // اختبار توليد تقرير ميزان المراجعة
                try
                {
                    var trialBalanceReport = await reportGenerator.GenerateTrialBalanceReportAsync(
                        DateTime.Now.AddMonths(-1), DateTime.Now);

                    if (trialBalanceReport != null)
                    {
                        Console.WriteLine("   ✅ تم توليد تقرير ميزان المراجعة");
                        Console.WriteLine($"      📄 نوع التقرير: FlowDocument");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ⚠️ تحذير في تقرير ميزان المراجعة: {ex.Message}");
                }

                // اختبار توليد تقرير الأرباح والخسائر
                try
                {
                    var incomeStatementReport = await reportGenerator.GenerateIncomeStatementReportAsync(
                        DateTime.Now.AddMonths(-1), DateTime.Now);

                    if (incomeStatementReport != null)
                    {
                        Console.WriteLine("   ✅ تم توليد تقرير الأرباح والخسائر");
                        Console.WriteLine($"      📄 نوع التقرير: FlowDocument");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ⚠️ تحذير في تقرير الأرباح والخسائر: {ex.Message}");
                }

                Console.WriteLine("   ✅ اختبار نظام التقارير مكتمل");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار نظام التقارير: {ex.Message}");
                return false;
            }
        }
    }
}
