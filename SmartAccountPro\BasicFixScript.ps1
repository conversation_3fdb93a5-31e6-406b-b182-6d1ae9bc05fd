# SmartAccount Pro - Basic Fix Script
# Basic fixes without Arabic text issues

param(
    [string]$ProjectPath = "."
)

Write-Host "Basic Fix Script for SmartAccount Pro" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# 1. Create missing directories
Write-Host "`nCreating missing directories..." -ForegroundColor Yellow

$requiredDirectories = @(
    "$ProjectPath\Core",
    "$ProjectPath\Core\Models",
    "$ProjectPath\Core\Services", 
    "$ProjectPath\Core\Interfaces",
    "$ProjectPath\Data",
    "$ProjectPath\Data\Entities",
    "$ProjectPath\Data\Repositories"
)

foreach ($dir in $requiredDirectories) {
    if (-not (Test-Path $dir)) {
        try {
            New-Item -Path $dir -ItemType Directory -Force | Out-Null
            Write-Host "Created: $dir" -ForegroundColor Green
        }
        catch {
            Write-Host "Failed to create: $dir" -ForegroundColor Red
        }
    }
    else {
        Write-Host "Already exists: $dir" -ForegroundColor Cyan
    }
}

# 2. Create missing AppDbContext.cs
Write-Host "`nCreating missing AppDbContext.cs..." -ForegroundColor Yellow

$appDbContextPath = "$ProjectPath\Data\AppDbContext.cs"
if (-not (Test-Path $appDbContextPath)) {
    $appDbContextContent = @"
using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        // DbSets for main entities
        public DbSet<User> Users { get; set; } = null!;
        public DbSet<Account> Accounts { get; set; } = null!;
        public DbSet<JournalEntry> JournalEntries { get; set; } = null!;
        public DbSet<JournalEntryItem> JournalEntryItems { get; set; } = null!;
        public DbSet<Invoice> Invoices { get; set; } = null!;
        public DbSet<InvoiceItem> InvoiceItems { get; set; } = null!;
        public DbSet<Customer> Customers { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // Configure entity relationships and constraints here
        }
    }
}
"@
    try {
        $appDbContextContent | Out-File -FilePath $appDbContextPath -Encoding UTF8
        Write-Host "AppDbContext.cs created successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to create AppDbContext.cs: $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host "AppDbContext.cs already exists" -ForegroundColor Cyan
}

# 3. Create DatabaseManager.cs
Write-Host "`nCreating DatabaseManager.cs..." -ForegroundColor Yellow

$dbManagerPath = "$ProjectPath\Data\DatabaseManager.cs"
if (-not (Test-Path $dbManagerPath)) {
    $dbManagerContent = @"
using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Data
{
    public class DatabaseManager
    {
        private readonly AppDbContext _context;

        public DatabaseManager(AppDbContext context)
        {
            _context = context;
        }

        public async Task InitializeDatabaseAsync()
        {
            try
            {
                // Ensure database is created
                await _context.Database.EnsureCreatedAsync();
                
                // Add any initial data seeding here
                await SeedInitialDataAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to initialize database", ex);
            }
        }

        private async Task SeedInitialDataAsync()
        {
            // Check if we need to seed initial data
            if (!await _context.Users.AnyAsync())
            {
                // Add default admin user
                var adminUser = new User
                {
                    Username = "admin",
                    PasswordHash = "admin123", // This should be properly hashed
                    FullName = "System Administrator",
                    Email = "<EMAIL>",
                    Role = "Admin",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                _context.Users.Add(adminUser);
                await _context.SaveChangesAsync();
            }
        }
    }
}
"@
    try {
        $dbManagerContent | Out-File -FilePath $dbManagerPath -Encoding UTF8
        Write-Host "DatabaseManager.cs created successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to create DatabaseManager.cs: $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host "DatabaseManager.cs already exists" -ForegroundColor Cyan
}

# 4. Fix C# warnings in BackgroundTaskManager.cs
Write-Host "`nFixing C# warnings..." -ForegroundColor Yellow

$bgTaskManagerPath = "$ProjectPath\Helpers\BackgroundTaskManager.cs"
if (Test-Path $bgTaskManagerPath) {
    try {
        # Create backup
        $backupPath = "$bgTaskManagerPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        Copy-Item $bgTaskManagerPath $backupPath -Force
        
        $content = Get-Content $bgTaskManagerPath -Raw -Encoding UTF8
        
        # Remove unnecessary using statements
        $content = $content -replace 'using System\.Text;\r?\n', ''
        $content = $content -replace 'using System\.Windows;\r?\n', ''
        
        $content | Out-File -FilePath $bgTaskManagerPath -Encoding UTF8
        Write-Host "C# warnings fixed in BackgroundTaskManager.cs" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to fix C# warnings: $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host "BackgroundTaskManager.cs not found" -ForegroundColor Cyan
}

# 5. Create .editorconfig file
Write-Host "`nCreating .editorconfig file..." -ForegroundColor Yellow

$editorConfigPath = "$ProjectPath\.editorconfig"
if (-not (Test-Path $editorConfigPath)) {
    $editorConfigContent = @"
root = true

[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
indent_style = space
indent_size = 4

[*.cs]
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true

[*.ps1]
indent_size = 4

[*.json]
indent_size = 2

[*.md]
trim_trailing_whitespace = false
"@
    try {
        $editorConfigContent | Out-File -FilePath $editorConfigPath -Encoding UTF8
        Write-Host ".editorconfig created successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to create .editorconfig: $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host ".editorconfig already exists" -ForegroundColor Cyan
}

# 6. Fix PowerShell script switch parameters
Write-Host "`nFixing PowerShell script switch parameters..." -ForegroundColor Yellow

$psScriptPath = "$ProjectPath\deployment\scripts\DeploymentTestFramework.ps1"
if (Test-Path $psScriptPath) {
    try {
        # Create backup
        $backupPath = "$psScriptPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        Copy-Item $psScriptPath $backupPath -Force
        Write-Host "Backup created: $backupPath" -ForegroundColor Green
        
        # Read content
        $content = Get-Content $psScriptPath -Raw -Encoding UTF8
        
        # Fix switch parameters with default values
        $content = $content -replace '\[switch\]\$TestAllInstallers = \$true,', '[switch]$TestAllInstallers,'
        $content = $content -replace '\[switch\]\$TestPerformance = \$true,', '[switch]$TestPerformance,'
        $content = $content -replace '\[switch\]\$TestCompatibility = \$true,', '[switch]$TestCompatibility,'
        $content = $content -replace '\[switch\]\$GenerateReports = \$true,', '[switch]$GenerateReports,'
        
        # Save fixed content
        $content | Out-File -FilePath $psScriptPath -Encoding UTF8
        Write-Host "PowerShell script switch parameters fixed" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to fix PowerShell script: $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host "PowerShell script not found: $psScriptPath" -ForegroundColor Yellow
}

Write-Host "`nBasic fix script completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Run 'dotnet build' to test the project" -ForegroundColor Cyan
Write-Host "2. Review the fixed files" -ForegroundColor Cyan
Write-Host "3. Test the PowerShell script" -ForegroundColor Cyan
