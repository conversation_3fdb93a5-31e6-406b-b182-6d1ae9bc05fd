using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SmartAccountPro.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع الحسابات
    /// </summary>
    public class AccountRepository : Repository<Account>, IAccountRepository
    {
        public AccountRepository(AppDbContext context) : base(context)
        {
        }

        /// <summary>
        /// الحصول على حساب بواسطة الكود
        /// </summary>
        public async Task<Account> GetByCodeAsync(string code)
        {
            return await _dbSet.FirstOrDefaultAsync(a => a.Code == code);
        }

        /// <summary>
        /// الحصول على الحسابات الرئيسية
        /// </summary>
        public async Task<IEnumerable<Account>> GetRootAccountsAsync()
        {
            return await _dbSet.Where(a => a.ParentAccountId == null).ToListAsync();
        }

        /// <summary>
        /// الحصول على الحسابات الفرعية لحساب معين
        /// </summary>
        public async Task<IEnumerable<Account>> GetChildAccountsAsync(int parentAccountId)
        {
            return await _dbSet.Where(a => a.ParentAccountId == parentAccountId).ToListAsync();
        }

        /// <summary>
        /// الحصول على شجرة الحسابات
        /// </summary>
        public async Task<IEnumerable<Account>> GetAccountTreeAsync()
        {
            return await _dbSet
                .Include(a => a.ChildAccounts)
                .Where(a => a.ParentAccountId == null)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على ميزان المراجعة
        /// </summary>
        public async Task<IEnumerable<Account>> GetTrialBalanceAsync(DateTime fromDate, DateTime toDate)
        {
            // هنا يمكن تنفيذ استعلام معقد للحصول على ميزان المراجعة
            // هذا مثال بسيط فقط
            return await _dbSet.ToListAsync();
        }

        /// <summary>
        /// تحديث رصيد الحساب
        /// </summary>
        public async Task<bool> UpdateBalanceAsync(int accountId, decimal amount)
        {
            var account = await GetByIdAsync(accountId);
            if (account == null)
                return false;

            account.Balance += amount;
            account.UpdatedAt = DateTime.Now;

            return await UpdateAsync(account);
        }
    }
}
