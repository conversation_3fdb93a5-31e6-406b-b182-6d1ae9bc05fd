using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SmartAccountPro.Data.Repositories
{
    /// <summary>
    /// تنفيذ مستودع القيود المحاسبية
    /// </summary>
    public class JournalEntryRepository : Repository<JournalEntry>, IJournalEntryRepository
    {
        private readonly IAccountRepository _accountRepository;

        public JournalEntryRepository(AppDbContext context, IAccountRepository accountRepository) : base(context)
        {
            _accountRepository = accountRepository;
        }

        /// <summary>
        /// الحصول على قيد محاسبي بواسطة الرقم
        /// </summary>
        public async Task<JournalEntry> GetByNumberAsync(string entryNumber)
        {
            return await _dbSet
                .Include(j => j.Items)
                .ThenInclude(i => i.Account)
                .Include(j => j.CreatedByUser)
                .Include(j => j.Posted<PERSON>yUser)
                .FirstOrDefaultAsync(j => j.EntryNumber == entryNumber);
        }

        /// <summary>
        /// الحصول على القيود المحاسبية في فترة معينة
        /// </summary>
        public async Task<IEnumerable<JournalEntry>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(j => j.Items)
                .ThenInclude(i => i.Account)
                .Include(j => j.CreatedByUser)
                .Include(j => j.PostedByUser)
                .Where(j => j.EntryDate >= fromDate && j.EntryDate <= toDate)
                .OrderByDescending(j => j.EntryDate)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على دفتر اليومية
        /// </summary>
        public async Task<IEnumerable<JournalEntry>> GetJournalAsync(DateTime fromDate, DateTime toDate)
        {
            return await GetByDateRangeAsync(fromDate, toDate);
        }

        /// <summary>
        /// الحصول على دفتر الأستاذ لحساب معين
        /// </summary>
        public async Task<IEnumerable<JournalEntryItem>> GetLedgerAsync(int accountId, DateTime fromDate, DateTime toDate)
        {
            return await _context.JournalEntryItems
                .Include(i => i.JournalEntry)
                .Include(i => i.Account)
                .Where(i => i.AccountId == accountId && i.JournalEntry.EntryDate >= fromDate && i.JournalEntry.EntryDate <= toDate)
                .OrderBy(i => i.JournalEntry.EntryDate)
                .ToListAsync();
        }

        /// <summary>
        /// ترحيل قيد محاسبي
        /// </summary>
        public async Task<bool> PostJournalEntryAsync(int id, int userId)
        {
            var journalEntry = await _dbSet
                .Include(j => j.Items)
                .FirstOrDefaultAsync(j => j.Id == id);

            if (journalEntry == null || journalEntry.IsPosted)
                return false;

            // تحديث أرصدة الحسابات
            foreach (var item in journalEntry.Items)
            {
                decimal amount = item.DebitAmount - item.CreditAmount;
                await _accountRepository.UpdateBalanceAsync(item.AccountId, amount);
            }

            // تحديث حالة القيد
            journalEntry.IsPosted = true;
            journalEntry.PostedAt = DateTime.Now;
            journalEntry.PostedByUserId = userId;

            return await UpdateAsync(journalEntry);
        }

        /// <summary>
        /// إلغاء ترحيل قيد محاسبي
        /// </summary>
        public async Task<bool> UnpostJournalEntryAsync(int id)
        {
            var journalEntry = await _dbSet
                .Include(j => j.Items)
                .FirstOrDefaultAsync(j => j.Id == id);

            if (journalEntry == null || !journalEntry.IsPosted)
                return false;

            // تحديث أرصدة الحسابات (عكس التأثير)
            foreach (var item in journalEntry.Items)
            {
                decimal amount = -(item.DebitAmount - item.CreditAmount);
                await _accountRepository.UpdateBalanceAsync(item.AccountId, amount);
            }

            // تحديث حالة القيد
            journalEntry.IsPosted = false;
            journalEntry.PostedAt = null;
            journalEntry.PostedByUserId = null;

            return await UpdateAsync(journalEntry);
        }

        /// <summary>
        /// إنشاء رقم قيد جديد
        /// </summary>
        public async Task<string> GenerateEntryNumberAsync()
        {
            int year = DateTime.Now.Year;
            int count = await _dbSet.CountAsync(j => j.EntryDate.Year == year) + 1;
            return $"JE-{year}-{count.ToString("D5")}";
        }
    }
}
