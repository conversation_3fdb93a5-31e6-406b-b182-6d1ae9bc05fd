using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Data;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using System.Linq;
using System.Collections.Generic;

namespace SmartAccountPro
{
    /// <summary>
    /// اختبار وظيفي تفاعلي شامل
    /// </summary>
    public class InteractiveFunctionalTest
    {
        private AppDbContext _context = null!;
        private DatabaseManager _dbManager = null!;
        private string _testDatabasePath = null!;

        /// <summary>
        /// تشغيل الاختبار الوظيفي التفاعلي
        /// </summary>
        public static async Task Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("🎯 SmartAccount Pro - اختبار وظيفي تفاعلي شامل");
            Console.WriteLine("═══════════════════════════════════════════════════");

            var tester = new InteractiveFunctionalTest();
            await tester.RunInteractiveFunctionalTestAsync();

            Console.WriteLine("\n🎉 انتهى الاختبار الوظيفي!");
            Console.WriteLine("اضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }

        /// <summary>
        /// تشغيل الاختبار الوظيفي الشامل
        /// </summary>
        public async Task RunInteractiveFunctionalTestAsync()
        {
            try
            {
                // تهيئة النظام
                Console.WriteLine("🚀 المرحلة 1: تهيئة النظام والبيانات");
                bool initResult = await InitializeSystemAsync();
                LogTestResult("تهيئة النظام", initResult);

                if (!initResult) return;

                // اختبار نظام المصادقة
                Console.WriteLine("\n🔐 المرحلة 2: اختبار نظام المصادقة");
                bool authResult = await TestAuthenticationSystemAsync();
                LogTestResult("نظام المصادقة", authResult);

                // اختبار إدارة الحسابات
                Console.WriteLine("\n🏦 المرحلة 3: اختبار إدارة الحسابات المحاسبية");
                bool accountsResult = await TestAccountManagementAsync();
                LogTestResult("إدارة الحسابات", accountsResult);

                // اختبار نظام القيود
                Console.WriteLine("\n📝 المرحلة 4: اختبار نظام القيود اليومية");
                bool journalResult = await TestJournalEntrySystemAsync();
                LogTestResult("نظام القيود", journalResult);

                // اختبار نظام الفواتير
                Console.WriteLine("\n🧾 المرحلة 5: اختبار نظام الفواتير");
                bool invoiceResult = await TestInvoiceSystemAsync();
                LogTestResult("نظام الفواتير", invoiceResult);

                // اختبار نظام التقارير
                Console.WriteLine("\n📈 المرحلة 6: اختبار نظام التقارير");
                bool reportsResult = await TestReportingSystemAsync();
                LogTestResult("نظام التقارير", reportsResult);

                // اختبار سيناريوهات حقيقية
                Console.WriteLine("\n🎯 المرحلة 7: اختبار سيناريوهات الاستخدام الحقيقية");
                bool scenariosResult = await TestRealWorldScenariosAsync();
                LogTestResult("السيناريوهات الحقيقية", scenariosResult);

                // النتيجة النهائية
                var allResults = new[] { initResult, authResult, accountsResult, journalResult, invoiceResult, reportsResult, scenariosResult };
                bool overallSuccess = allResults.All(r => r);
                
                Console.WriteLine("\n═══════════════════════════════════════════════════");
                Console.WriteLine($"🎯 النتيجة النهائية: {(overallSuccess ? "جميع الاختبارات نجحت ✅" : "بعض الاختبارات فشلت ❌")}");
                Console.WriteLine($"📊 معدل النجاح: {allResults.Count(r => r)}/{allResults.Length} ({(allResults.Count(r => r) * 100.0 / allResults.Length):F1}%)");
                
                if (overallSuccess)
                {
                    Console.WriteLine("🏆 التطبيق جاهز للاستخدام الفعلي!");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"💥 خطأ عام في الاختبار: {ex.Message}");
            }
            finally
            {
                await CleanupAsync();
            }
        }

        /// <summary>
        /// تهيئة النظام للاختبار
        /// </summary>
        private async Task<bool> InitializeSystemAsync()
        {
            try
            {
                // إنشاء مجلد اختبار مؤقت
                string tempPath = Path.Combine(Path.GetTempPath(), "SmartAccountProFunctionalTest");
                if (Directory.Exists(tempPath))
                    Directory.Delete(tempPath, true);
                Directory.CreateDirectory(tempPath);

                _testDatabasePath = Path.Combine(tempPath, "FunctionalTestDatabase.db");

                // تهيئة سياق قاعدة البيانات
                var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
                optionsBuilder.UseSqlite($"Data Source={_testDatabasePath}");
                optionsBuilder.EnableSensitiveDataLogging();

                _context = new AppDbContext(optionsBuilder.Options);

                // إنشاء قاعدة البيانات
                await _context.Database.EnsureCreatedAsync();

                // تهيئة مدير قاعدة البيانات
                _dbManager = new DatabaseManager(_context);
                await _dbManager.InitializeDatabaseAsync();

                Console.WriteLine($"   ✅ تم إنشاء قاعدة بيانات الاختبار: {_testDatabasePath}");
                Console.WriteLine($"   ✅ تم تهيئة مدير قاعدة البيانات");
                Console.WriteLine($"   ✅ تم إنشاء الجداول والبيانات الأساسية");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في تهيئة النظام: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار نظام المصادقة
        /// </summary>
        private async Task<bool> TestAuthenticationSystemAsync()
        {
            try
            {
                // اختبار وجود المستخدم الافتراضي
                var adminUser = await _context.Users.FirstOrDefaultAsync(u => u.Username == "admin");
                if (adminUser != null)
                {
                    Console.WriteLine($"   ✅ المستخدم الافتراضي موجود: {adminUser.FullName}");
                    Console.WriteLine($"   📧 البريد الإلكتروني: {adminUser.Email}");
                    Console.WriteLine($"   🔐 حالة النشاط: {(adminUser.IsActive ? "نشط" : "غير نشط")}");
                }

                // اختبار إنشاء مستخدم جديد
                var testUser = new User
                {
                    Username = "testuser",
                    FullName = "مستخدم تجريبي للاختبار",
                    Email = "<EMAIL>",
                    PasswordHash = "hashedpassword123",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Users.Add(testUser);
                await _context.SaveChangesAsync();
                Console.WriteLine("   ✅ تم إنشاء مستخدم تجريبي بنجاح");

                // اختبار البحث عن المستخدم
                var foundUser = await _context.Users.FirstOrDefaultAsync(u => u.Username == "testuser");
                if (foundUser != null)
                {
                    Console.WriteLine("   ✅ تم العثور على المستخدم التجريبي");
                    Console.WriteLine($"   👤 الاسم الكامل: {foundUser.FullName}");
                }

                // اختبار تعديل المستخدم
                foundUser!.FullName = "مستخدم تجريبي محدث";
                await _context.SaveChangesAsync();
                Console.WriteLine("   ✅ تم تحديث بيانات المستخدم");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار نظام المصادقة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار إدارة الحسابات المحاسبية
        /// </summary>
        private async Task<bool> TestAccountManagementAsync()
        {
            try
            {
                // اختبار وجود الحسابات الأساسية
                var assetsAccount = await _context.Accounts.FirstOrDefaultAsync(a => a.Code == "1" && a.Type == AccountType.Asset);
                var liabilitiesAccount = await _context.Accounts.FirstOrDefaultAsync(a => a.Code == "2" && a.Type == AccountType.Liability);
                var equityAccount = await _context.Accounts.FirstOrDefaultAsync(a => a.Code == "3" && a.Type == AccountType.Equity);

                if (assetsAccount != null && liabilitiesAccount != null && equityAccount != null)
                {
                    Console.WriteLine("   ✅ الحسابات الأساسية موجودة:");
                    Console.WriteLine($"      💰 الأصول: {assetsAccount.Name}");
                    Console.WriteLine($"      💳 الخصوم: {liabilitiesAccount.Name}");
                    Console.WriteLine($"      🏦 حقوق الملكية: {equityAccount.Name}");
                }

                // اختبار إنشاء حساب فرعي جديد
                var newAccount = new Account
                {
                    Code = "1.1.1",
                    Name = "النقدية في الصندوق",
                    Type = AccountType.Asset,
                    ParentAccountId = assetsAccount?.Id,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Accounts.Add(newAccount);
                await _context.SaveChangesAsync();
                Console.WriteLine($"   ✅ تم إنشاء حساب فرعي: {newAccount.Name}");

                // اختبار البحث في الحسابات
                var searchResults = await _context.Accounts
                    .Where(a => a.Name.Contains("النقدية"))
                    .ToListAsync();
                
                Console.WriteLine($"   ✅ نتائج البحث: {searchResults.Count} حساب");

                // اختبار عدد الحسابات الإجمالي
                var totalAccounts = await _context.Accounts.CountAsync();
                Console.WriteLine($"   📊 إجمالي الحسابات: {totalAccounts}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار إدارة الحسابات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار نظام القيود اليومية
        /// </summary>
        private async Task<bool> TestJournalEntrySystemAsync()
        {
            try
            {
                // البحث عن حسابات للاختبار
                var cashAccount = await _context.Accounts.FirstOrDefaultAsync(a => a.Name.Contains("النقدية"));
                var capitalAccount = await _context.Accounts.FirstOrDefaultAsync(a => a.Type == AccountType.Equity);

                if (cashAccount == null || capitalAccount == null)
                {
                    Console.WriteLine("   ⚠️ لم يتم العثور على الحسابات المطلوبة للاختبار");
                    return false;
                }

                // إنشاء قيد تجريبي
                var testEntry = new JournalEntry
                {
                    EntryNumber = "JE-TEST-001",
                    EntryDate = DateTime.Now,
                    Description = "قيد افتتاحي تجريبي",
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = 1
                };

                // إضافة بنود القيد
                testEntry.Items = new List<JournalEntryItem>
                {
                    new JournalEntryItem
                    {
                        AccountId = cashAccount.Id,
                        DebitAmount = 50000,
                        CreditAmount = 0,
                        Description = "نقدية افتتاحية"
                    },
                    new JournalEntryItem
                    {
                        AccountId = capitalAccount.Id,
                        DebitAmount = 0,
                        CreditAmount = 50000,
                        Description = "رأس المال الافتتاحي"
                    }
                };

                _context.JournalEntries.Add(testEntry);
                await _context.SaveChangesAsync();

                Console.WriteLine($"   ✅ تم إنشاء قيد تجريبي: {testEntry.EntryNumber}");
                Console.WriteLine($"      📅 التاريخ: {testEntry.EntryDate:yyyy-MM-dd}");
                Console.WriteLine($"      💰 المبلغ: {testEntry.Items.Sum(i => i.DebitAmount):N0} ريال");
                Console.WriteLine($"      📝 الوصف: {testEntry.Description}");

                // التحقق من توازن القيد
                var totalDebit = testEntry.Items.Sum(i => i.DebitAmount);
                var totalCredit = testEntry.Items.Sum(i => i.CreditAmount);
                
                if (totalDebit == totalCredit)
                {
                    Console.WriteLine("   ✅ القيد متوازن (مدين = دائن)");
                }
                else
                {
                    Console.WriteLine("   ❌ القيد غير متوازن!");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار نظام القيود: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// طباعة نتيجة الاختبار
        /// </summary>
        private void LogTestResult(string testName, bool success)
        {
            string status = success ? "✅ نجح" : "❌ فشل";
            Console.WriteLine($"   🎯 {testName}: {status}");
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        private async Task CleanupAsync()
        {
            try
            {
                if (_context != null)
                {
                    await _context.DisposeAsync();
                }

                if (!string.IsNullOrEmpty(_testDatabasePath) && File.Exists(_testDatabasePath))
                {
                    File.Delete(_testDatabasePath);
                    Console.WriteLine("   🧹 تم حذف قاعدة بيانات الاختبار");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ خطأ في التنظيف: {ex.Message}");
            }
        }

        /// <summary>
        /// اختبار نظام الفواتير
        /// </summary>
        private async Task<bool> TestInvoiceSystemAsync()
        {
            try
            {
                // إنشاء عميل تجريبي
                var testCustomer = new Customer
                {
                    Name = "شركة الاختبار التجارية",
                    Phone = "0501234567",
                    Email = "<EMAIL>",
                    Address = "الرياض، المملكة العربية السعودية",
                    CreatedAt = DateTime.Now
                };

                _context.Customers.Add(testCustomer);
                await _context.SaveChangesAsync();
                Console.WriteLine($"   ✅ تم إنشاء عميل تجريبي: {testCustomer.Name}");

                // إنشاء فاتورة تجريبية
                var testInvoice = new Invoice
                {
                    InvoiceNumber = "INV-TEST-001",
                    InvoiceDate = DateTime.Now,
                    DueDate = DateTime.Now.AddDays(30),
                    CustomerId = testCustomer.Id,
                    Status = InvoiceStatus.Draft,
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = 1
                };

                _context.Invoices.Add(testInvoice);
                await _context.SaveChangesAsync();

                Console.WriteLine($"   ✅ تم إنشاء فاتورة تجريبية: {testInvoice.InvoiceNumber}");
                Console.WriteLine($"      📅 التاريخ: {testInvoice.InvoiceDate:yyyy-MM-dd}");
                Console.WriteLine($"      👤 العميل: {testCustomer.Name}");
                Console.WriteLine($"      📋 الحالة: {testInvoice.Status}");

                // اختبار البحث عن الفاتورة
                var foundInvoice = await _context.Invoices
                    .Include(i => i.Customer)
                    .FirstOrDefaultAsync(i => i.InvoiceNumber == "INV-TEST-001");

                if (foundInvoice != null && foundInvoice.Customer != null)
                {
                    Console.WriteLine("   ✅ تم العثور على الفاتورة مع بيانات العميل");
                }

                // اختبار عدد الفواتير
                var totalInvoices = await _context.Invoices.CountAsync();
                Console.WriteLine($"   📊 إجمالي الفواتير: {totalInvoices}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار نظام الفواتير: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار نظام التقارير
        /// </summary>
        private async Task<bool> TestReportingSystemAsync()
        {
            try
            {
                // اختبار إنشاء مولد التقارير
                var reportGenerator = new ReportGenerator(_context);
                Console.WriteLine("   ✅ تم إنشاء مولد التقارير");

                // اختبار توليد تقرير ميزان المراجعة
                try
                {
                    var trialBalanceReport = await reportGenerator.GenerateTrialBalanceReportAsync(
                        DateTime.Now.AddMonths(-1), DateTime.Now);

                    if (trialBalanceReport != null)
                    {
                        Console.WriteLine("   ✅ تم توليد تقرير ميزان المراجعة");
                        Console.WriteLine("      📄 نوع التقرير: FlowDocument");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ⚠️ تحذير في تقرير ميزان المراجعة: {ex.Message}");
                }

                // اختبار توليد تقرير الأرباح والخسائر
                try
                {
                    var incomeStatementReport = await reportGenerator.GenerateIncomeStatementReportAsync(
                        DateTime.Now.AddMonths(-1), DateTime.Now);

                    if (incomeStatementReport != null)
                    {
                        Console.WriteLine("   ✅ تم توليد تقرير الأرباح والخسائر");
                        Console.WriteLine("      📄 نوع التقرير: FlowDocument");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ⚠️ تحذير في تقرير الأرباح والخسائر: {ex.Message}");
                }

                // اختبار إحصائيات قاعدة البيانات
                var accountsCount = await _context.Accounts.CountAsync();
                var journalEntriesCount = await _context.JournalEntries.CountAsync();
                var invoicesCount = await _context.Invoices.CountAsync();
                var customersCount = await _context.Customers.CountAsync();

                Console.WriteLine("   📊 إحصائيات قاعدة البيانات:");
                Console.WriteLine($"      🏦 الحسابات: {accountsCount}");
                Console.WriteLine($"      📝 القيود: {journalEntriesCount}");
                Console.WriteLine($"      🧾 الفواتير: {invoicesCount}");
                Console.WriteLine($"      👥 العملاء: {customersCount}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار نظام التقارير: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار سيناريوهات الاستخدام الحقيقية
        /// </summary>
        private async Task<bool> TestRealWorldScenariosAsync()
        {
            try
            {
                Console.WriteLine("   🎯 سيناريو 1: شركة تجارية صغيرة");

                // إنشاء حسابات إضافية للشركة التجارية
                var salesAccount = new Account
                {
                    Code = "4.1",
                    Name = "مبيعات البضائع",
                    Type = AccountType.Revenue,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                var expenseAccount = new Account
                {
                    Code = "5.1",
                    Name = "مصروفات التشغيل",
                    Type = AccountType.Expense,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.Accounts.AddRange(salesAccount, expenseAccount);
                await _context.SaveChangesAsync();
                Console.WriteLine("      ✅ تم إنشاء حسابات الشركة التجارية");

                // إنشاء قيد مبيعات
                var salesEntry = new JournalEntry
                {
                    EntryNumber = "JE-SALES-001",
                    EntryDate = DateTime.Now,
                    Description = "مبيعات نقدية",
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = 1
                };

                var cashAccount = await _context.Accounts.FirstOrDefaultAsync(a => a.Name.Contains("النقدية"));
                if (cashAccount != null)
                {
                    salesEntry.Items = new List<JournalEntryItem>
                    {
                        new JournalEntryItem
                        {
                            AccountId = cashAccount.Id,
                            DebitAmount = 5000,
                            CreditAmount = 0,
                            Description = "نقدية من المبيعات"
                        },
                        new JournalEntryItem
                        {
                            AccountId = salesAccount.Id,
                            DebitAmount = 0,
                            CreditAmount = 5000,
                            Description = "مبيعات البضائع"
                        }
                    };

                    _context.JournalEntries.Add(salesEntry);
                    await _context.SaveChangesAsync();
                    Console.WriteLine("      ✅ تم تسجيل قيد المبيعات");
                }

                // إنشاء قيد مصروفات
                var expenseEntry = new JournalEntry
                {
                    EntryNumber = "JE-EXP-001",
                    EntryDate = DateTime.Now,
                    Description = "مصروفات إدارية",
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = 1
                };

                if (cashAccount != null)
                {
                    expenseEntry.Items = new List<JournalEntryItem>
                    {
                        new JournalEntryItem
                        {
                            AccountId = expenseAccount.Id,
                            DebitAmount = 1000,
                            CreditAmount = 0,
                            Description = "مصروفات إدارية"
                        },
                        new JournalEntryItem
                        {
                            AccountId = cashAccount.Id,
                            DebitAmount = 0,
                            CreditAmount = 1000,
                            Description = "دفع نقدي للمصروفات"
                        }
                    };

                    _context.JournalEntries.Add(expenseEntry);
                    await _context.SaveChangesAsync();
                    Console.WriteLine("      ✅ تم تسجيل قيد المصروفات");
                }

                // حساب صافي الربح
                var totalRevenue = await _context.JournalEntryItems
                    .Where(i => i.Account.Type == AccountType.Revenue)
                    .SumAsync(i => i.CreditAmount);

                var totalExpenses = await _context.JournalEntryItems
                    .Where(i => i.Account.Type == AccountType.Expense)
                    .SumAsync(i => i.DebitAmount);

                var netProfit = totalRevenue - totalExpenses;

                Console.WriteLine($"      📊 إجمالي الإيرادات: {totalRevenue:N0} ريال");
                Console.WriteLine($"      📊 إجمالي المصروفات: {totalExpenses:N0} ريال");
                Console.WriteLine($"      💰 صافي الربح: {netProfit:N0} ريال");

                Console.WriteLine("   🎯 سيناريو 2: اختبار التوازن المحاسبي");

                // التحقق من توازن جميع القيود
                var allEntries = await _context.JournalEntries.Include(je => je.Items).ToListAsync();
                bool allEntriesBalanced = true;

                foreach (var entry in allEntries)
                {
                    var totalDebit = entry.Items.Sum(i => i.DebitAmount);
                    var totalCredit = entry.Items.Sum(i => i.CreditAmount);

                    if (totalDebit != totalCredit)
                    {
                        allEntriesBalanced = false;
                        Console.WriteLine($"      ❌ قيد غير متوازن: {entry.EntryNumber}");
                    }
                }

                if (allEntriesBalanced)
                {
                    Console.WriteLine("      ✅ جميع القيود متوازنة");
                }

                Console.WriteLine("   🎯 سيناريو 3: اختبار الأداء");

                // قياس وقت الاستعلامات
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                var complexQuery = await _context.JournalEntryItems
                    .Include(i => i.Account)
                    .Include(i => i.JournalEntry)
                    .Where(i => i.DebitAmount > 0)
                    .OrderByDescending(i => i.DebitAmount)
                    .Take(10)
                    .ToListAsync();

                stopwatch.Stop();
                Console.WriteLine($"      ⏱️ وقت الاستعلام المعقد: {stopwatch.ElapsedMilliseconds} مللي ثانية");
                Console.WriteLine($"      📊 نتائج الاستعلام: {complexQuery.Count} عنصر");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ خطأ في اختبار السيناريوهات الحقيقية: {ex.Message}");
                return false;
            }
        }
    }
}
