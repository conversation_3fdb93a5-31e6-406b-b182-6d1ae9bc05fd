using SmartAccountPro.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SmartAccountPro.Core.Repositories
{
    /// <summary>
    /// واجهة مستودع القيود المحاسبية
    /// </summary>
    public interface IJournalEntryRepository : IRepository<JournalEntry>
    {
        /// <summary>
        /// الحصول على قيد محاسبي بواسطة الرقم
        /// </summary>
        Task<JournalEntry> GetByNumberAsync(string entryNumber);

        /// <summary>
        /// الحصول على القيود المحاسبية في فترة معينة
        /// </summary>
        Task<IEnumerable<JournalEntry>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// الحصول على دفتر اليومية
        /// </summary>
        Task<IEnumerable<JournalEntry>> GetJournalAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// الحصول على دفتر الأستاذ لحساب معين
        /// </summary>
        Task<IEnumerable<JournalEntryItem>> GetLedgerAsync(int accountId, DateTime fromDate, DateTime toDate);

        /// <summary>
        /// ترحيل قيد محاسبي
        /// </summary>
        Task<bool> PostJournalEntryAsync(int id, int userId);

        /// <summary>
        /// إلغاء ترحيل قيد محاسبي
        /// </summary>
        Task<bool> UnpostJournalEntryAsync(int id);

        /// <summary>
        /// إنشاء رقم قيد جديد
        /// </summary>
        Task<string> GenerateEntryNumberAsync();
    }
}
