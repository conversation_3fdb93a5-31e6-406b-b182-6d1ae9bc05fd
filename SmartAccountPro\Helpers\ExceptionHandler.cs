using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// معالج الاستثناءات في التطبيق
    /// </summary>
    public static class ExceptionHandler
    {
        private static readonly string LogFilePath = Path.Combine(
            App.AppDataPath,
            "Logs",
            $"error_log_{DateTime.Now:yyyy-MM-dd}.txt");

        /// <summary>
        /// تسجيل الاستثناء في ملف السجل
        /// </summary>
        public static void LogException(Exception ex, string source = "")
        {
            try
            {
                // التأكد من وجود مجلد السجلات
                var logDirectory = Path.GetDirectoryName(LogFilePath);
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory!);
                }

                // بناء رسالة السجل
                var logMessage = new StringBuilder();
                logMessage.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] Exception in {source}");
                logMessage.AppendLine($"Message: {ex.Message}");
                logMessage.AppendLine($"Source: {ex.Source}");
                logMessage.AppendLine($"StackTrace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    logMessage.AppendLine($"Inner Exception: {ex.InnerException.Message}");
                    logMessage.AppendLine($"Inner StackTrace: {ex.InnerException.StackTrace}");
                }

                logMessage.AppendLine(new string('-', 50));

                // كتابة السجل في الملف
                File.AppendAllText(LogFilePath, logMessage.ToString());

                // تسجيل الاستثناء في سجل التصحيح
                Debug.WriteLine($"[ERROR] {source}: {ex.Message}");
            }
            catch (Exception logEx)
            {
                // تسجيل أي استثناءات أثناء تسجيل الاستثناء
                Debug.WriteLine($"خطأ أثناء تسجيل الاستثناء: {logEx.Message}");
            }
        }

        /// <summary>
        /// معالجة الاستثناء وعرض رسالة للمستخدم
        /// </summary>
        public static void HandleException(Exception ex, string source = "", bool showMessage = true)
        {
            // تسجيل الاستثناء
            LogException(ex, source);

            // عرض رسالة للمستخدم
            if (showMessage)
            {
                // تحديد نوع الخطأ ورسالة مناسبة
                string errorMessage = GetFriendlyErrorMessage(ex);

                MessageBox.Show(
                    $"{errorMessage}\n\nتم تسجيل تفاصيل الخطأ.",
                    "خطأ في التطبيق",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الحصول على رسالة خطأ ودية للمستخدم
        /// </summary>
        public static string GetFriendlyErrorMessage(Exception ex)
        {
            try
            {
                // تحديد نوع الخطأ وإرجاع رسالة مناسبة
                if (ex.GetType().Name.Contains("SQLite") || ex.Message.Contains("SQLite") || ex.Source?.Contains("SQLite") == true)
                {
                    return "حدث خطأ أثناء الاتصال بقاعدة البيانات. يرجى التأكد من وجود قاعدة البيانات وصلاحيات الوصول.";
                }
                else if (ex is System.IO.IOException)
                {
                    return "حدث خطأ أثناء قراءة أو كتابة الملفات. يرجى التأكد من وجود صلاحيات كافية.";
                }
                else if (ex is System.Net.WebException)
                {
                    return "حدث خطأ أثناء الاتصال بالإنترنت. يرجى التحقق من اتصالك بالإنترنت.";
                }
                else if (ex is System.OutOfMemoryException)
                {
                    return "نفدت ذاكرة التطبيق. يرجى إغلاق بعض التطبيقات وإعادة المحاولة.";
                }
                else if (ex is System.UnauthorizedAccessException)
                {
                    return "ليس لديك صلاحيات كافية للقيام بهذه العملية.";
                }
                else if (ex is System.ArgumentException || ex is System.FormatException)
                {
                    return "تم إدخال بيانات غير صحيحة. يرجى التحقق من البيانات المدخلة.";
                }
                else if (ex is System.NullReferenceException)
                {
                    return "حدث خطأ في التطبيق: محاولة الوصول إلى كائن غير موجود.";
                }
                else if (ex is System.InvalidOperationException)
                {
                    return "حدث خطأ في التطبيق: عملية غير صالحة في السياق الحالي.";
                }
                else if (ex is System.Data.DataException)
                {
                    return "حدث خطأ في البيانات. يرجى التحقق من صحة البيانات المدخلة.";
                }
                else if (ex is System.TimeoutException)
                {
                    return "انتهت مهلة العملية. يرجى المحاولة مرة أخرى.";
                }
                else
                {
                    // رسالة عامة للأخطاء غير المعروفة
                    return $"حدث خطأ في التطبيق: {ex.Message}";
                }
            }
            catch
            {
                // في حالة حدوث خطأ أثناء معالجة الخطأ
                return "حدث خطأ غير متوقع في التطبيق.";
            }
        }

        /// <summary>
        /// معالجة الاستثناءات غير المعالجة في التطبيق
        /// </summary>
        public static void SetupGlobalExceptionHandling()
        {
            // معالجة الاستثناءات غير المعالجة في واجهة المستخدم
            Application.Current.DispatcherUnhandledException += (sender, e) =>
            {
                HandleException(e.Exception, "UI Thread");
                e.Handled = true;
            };

            // معالجة الاستثناءات غير المعالجة في المجال الحالي
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                if (e.ExceptionObject is Exception ex)
                {
                    HandleException(ex, "AppDomain");
                }
            };

            // معالجة الاستثناءات غير المعالجة في المهام
            TaskScheduler.UnobservedTaskException += (sender, e) =>
            {
                HandleException(e.Exception, "Task");
                e.SetObserved();
            };

            Debug.WriteLine("تم تهيئة معالجة الاستثناءات العامة");
        }

        /// <summary>
        /// فتح ملف سجل الأخطاء
        /// </summary>
        public static void OpenLogFile()
        {
            try
            {
                if (File.Exists(LogFilePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = LogFilePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("ملف سجل الأخطاء غير موجود.", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ أثناء فتح ملف سجل الأخطاء: {ex.Message}");
                MessageBox.Show($"تعذر فتح ملف سجل الأخطاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الحصول على مسار ملف سجل الأخطاء الحالي
        /// </summary>
        /// <returns>مسار ملف سجل الأخطاء</returns>
        public static string GetCurrentLogFilePath()
        {
            return LogFilePath;
        }

        /// <summary>
        /// الحصول على محتوى ملف سجل الأخطاء
        /// </summary>
        /// <returns>محتوى ملف سجل الأخطاء</returns>
        public static string GetLogFileContent()
        {
            try
            {
                if (File.Exists(LogFilePath))
                {
                    return File.ReadAllText(LogFilePath);
                }
                else
                {
                    return "ملف سجل الأخطاء غير موجود.";
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ أثناء قراءة ملف سجل الأخطاء: {ex.Message}");
                return $"تعذر قراءة ملف سجل الأخطاء: {ex.Message}";
            }
        }

        /// <summary>
        /// مسح محتوى ملف سجل الأخطاء
        /// </summary>
        /// <returns>نجاح العملية</returns>
        public static bool ClearLogFile()
        {
            try
            {
                if (File.Exists(LogFilePath))
                {
                    File.WriteAllText(LogFilePath, $"تم مسح السجل في {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n{new string('-', 50)}\n");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ أثناء مسح ملف سجل الأخطاء: {ex.Message}");
                return false;
            }
        }
    }
}
