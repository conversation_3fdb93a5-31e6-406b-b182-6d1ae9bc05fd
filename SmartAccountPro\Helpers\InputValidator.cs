using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للتحقق من صحة المدخلات
    /// </summary>
    public static class InputValidator
    {
        /// <summary>
        /// التحقق من صحة اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateUsername(string username)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(username))
                {
                    return new ValidationResult(false, "اسم المستخدم مطلوب.");
                }

                // التحقق من الطول
                if (username.Length < 3 || username.Length > 50)
                {
                    return new ValidationResult(false, "يجب أن يكون طول اسم المستخدم بين 3 و 50 حرفاً.");
                }

                // التحقق من الأحرف المسموح بها
                if (!Regex.IsMatch(username, @"^[a-zA-Z0-9_.-]+$"))
                {
                    return new ValidationResult(false, "يجب أن يحتوي اسم المستخدم على أحرف إنجليزية وأرقام ورموز _ . - فقط.");
                }

                return new ValidationResult(true, "اسم المستخدم صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "InputValidator.ValidateUsername");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من اسم المستخدم.");
            }
        }

        /// <summary>
        /// التحقق من صحة كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidatePassword(string password)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(password))
                {
                    return new ValidationResult(false, "كلمة المرور مطلوبة.");
                }

                // التحقق من الطول
                if (password.Length < 6 || password.Length > 100)
                {
                    return new ValidationResult(false, "يجب أن يكون طول كلمة المرور بين 6 و 100 حرف.");
                }

                // التحقق من قوة كلمة المرور
                int strength = 0;

                // التحقق من وجود أحرف صغيرة
                if (Regex.IsMatch(password, @"[a-z]"))
                {
                    strength++;
                }

                // التحقق من وجود أحرف كبيرة
                if (Regex.IsMatch(password, @"[A-Z]"))
                {
                    strength++;
                }

                // التحقق من وجود أرقام
                if (Regex.IsMatch(password, @"[0-9]"))
                {
                    strength++;
                }

                // التحقق من وجود رموز خاصة
                if (Regex.IsMatch(password, @"[^a-zA-Z0-9]"))
                {
                    strength++;
                }

                // التحقق من قوة كلمة المرور
                if (strength < 3)
                {
                    return new ValidationResult(false, "كلمة المرور ضعيفة. يجب أن تحتوي على أحرف صغيرة وكبيرة وأرقام ورموز خاصة.");
                }

                return new ValidationResult(true, "كلمة المرور صحيحة.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "InputValidator.ValidatePassword");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من كلمة المرور.");
            }
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateEmail(string email)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(email))
                {
                    return new ValidationResult(false, "البريد الإلكتروني مطلوب.");
                }

                // التحقق من صحة البريد الإلكتروني
                if (!Regex.IsMatch(email, @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"))
                {
                    return new ValidationResult(false, "البريد الإلكتروني غير صحيح.");
                }

                return new ValidationResult(true, "البريد الإلكتروني صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "InputValidator.ValidateEmail");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من البريد الإلكتروني.");
            }
        }

        /// <summary>
        /// التحقق من صحة رقم الهاتف
        /// </summary>
        /// <param name="phoneNumber">رقم الهاتف</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidatePhoneNumber(string phoneNumber)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(phoneNumber))
                {
                    return new ValidationResult(true, "رقم الهاتف اختياري.");
                }

                // التحقق من صحة رقم الهاتف
                if (!Regex.IsMatch(phoneNumber, @"^[0-9+\-\(\) ]{8,20}$"))
                {
                    return new ValidationResult(false, "رقم الهاتف غير صحيح. يجب أن يحتوي على أرقام ورموز + - ( ) فقط وأن يكون طوله بين 8 و 20 حرفاً.");
                }

                return new ValidationResult(true, "رقم الهاتف صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "InputValidator.ValidatePhoneNumber");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من رقم الهاتف.");
            }
        }

        /// <summary>
        /// التحقق من صحة المبلغ المالي
        /// </summary>
        /// <param name="amount">المبلغ المالي</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateAmount(string amount)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(amount))
                {
                    return new ValidationResult(false, "المبلغ المالي مطلوب.");
                }

                // التحقق من صحة المبلغ المالي
                if (!decimal.TryParse(amount, out decimal result))
                {
                    return new ValidationResult(false, "المبلغ المالي غير صحيح.");
                }

                // التحقق من قيمة المبلغ المالي
                if (result <= 0)
                {
                    return new ValidationResult(false, "يجب أن يكون المبلغ المالي أكبر من صفر.");
                }

                return new ValidationResult(true, "المبلغ المالي صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "InputValidator.ValidateAmount");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من المبلغ المالي.");
            }
        }

        /// <summary>
        /// التحقق من صحة التاريخ
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateDate(string date)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(date))
                {
                    return new ValidationResult(false, "التاريخ مطلوب.");
                }

                // التحقق من صحة التاريخ
                if (!DateTime.TryParse(date, out DateTime result))
                {
                    return new ValidationResult(false, "التاريخ غير صحيح.");
                }

                return new ValidationResult(true, "التاريخ صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "InputValidator.ValidateDate");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من التاريخ.");
            }
        }

        /// <summary>
        /// التحقق من صحة النص
        /// </summary>
        /// <param name="text">النص</param>
        /// <param name="minLength">الحد الأدنى للطول</param>
        /// <param name="maxLength">الحد الأقصى للطول</param>
        /// <param name="allowEmpty">السماح بالقيمة الفارغة</param>
        /// <returns>نتيجة التحقق</returns>
        public static ValidationResult ValidateText(string text, int minLength = 1, int maxLength = 255, bool allowEmpty = false)
        {
            try
            {
                // التحقق من عدم وجود قيمة فارغة
                if (string.IsNullOrWhiteSpace(text))
                {
                    if (allowEmpty)
                    {
                        return new ValidationResult(true, "النص اختياري.");
                    }
                    else
                    {
                        return new ValidationResult(false, "النص مطلوب.");
                    }
                }

                // التحقق من الطول
                if (text.Length < minLength || text.Length > maxLength)
                {
                    return new ValidationResult(false, $"يجب أن يكون طول النص بين {minLength} و {maxLength} حرفاً.");
                }

                return new ValidationResult(true, "النص صحيح.");
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "InputValidator.ValidateText");
                return new ValidationResult(false, "حدث خطأ أثناء التحقق من النص.");
            }
        }
    }

    /// <summary>
    /// نتيجة التحقق من صحة المدخلات
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// هل المدخلات صحيحة
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// رسالة التحقق
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// إنشاء نتيجة تحقق جديدة
        /// </summary>
        /// <param name="isValid">هل المدخلات صحيحة</param>
        /// <param name="message">رسالة التحقق</param>
        public ValidationResult(bool isValid, string message)
        {
            IsValid = isValid;
            Message = message;
        }
    }
}
