using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Data;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة الأمان والصلاحيات
    /// </summary>
    public class SecurityManager
    {
        private readonly AppDbContext _context;
        private static User? _currentUser;
        private static List<string> _userPermissions = new List<string>();
        private static readonly Dictionary<string, bool> _permissionCache = new Dictionary<string, bool>();

        /// <summary>
        /// إنشاء مدير الأمان
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public SecurityManager(AppDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>نتيجة تسجيل الدخول</returns>
        public async Task<LoginResult> LoginAsync(string username, string password)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("SecurityManager.Login", async () =>
                {
                    // التحقق من صحة المدخلات
                    if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                    {
                        return new LoginResult
                        {
                            Success = false,
                            Message = "يرجى إدخال اسم المستخدم وكلمة المرور."
                        };
                    }

                    // تشفير كلمة المرور
                    string hashedPassword = EncryptionHelper.ComputeSha256Hash(password);

                    // البحث عن المستخدم
                    var user = await _context.Users
                        .Include(u => u.UserRoles)
                            .ThenInclude(ur => ur.Role)
                                .ThenInclude(r => r.RolePermissions)
                                    .ThenInclude(rp => rp.Permission)
                        .FirstOrDefaultAsync(u => u.Username == username);

                    // التحقق من وجود المستخدم
                    if (user == null)
                    {
                        // تسجيل محاولة تسجيل دخول فاشلة
                        await AuditTrailManager.LogLogin(0, username, false);

                        return new LoginResult
                        {
                            Success = false,
                            Message = "اسم المستخدم أو كلمة المرور غير صحيحة."
                        };
                    }

                    // التحقق من حالة المستخدم
                    if (!user.IsActive)
                    {
                        // تسجيل محاولة تسجيل دخول فاشلة
                        await AuditTrailManager.LogLogin(user.Id, username, false);

                        return new LoginResult
                        {
                            Success = false,
                            Message = "الحساب غير نشط. يرجى التواصل مع المسؤول."
                        };
                    }

                    // التحقق من كلمة المرور
                    if (user.PasswordHash != hashedPassword)
                    {
                        // تسجيل محاولة تسجيل دخول فاشلة
                        await AuditTrailManager.LogLogin(user.Id, username, false);

                        return new LoginResult
                        {
                            Success = false,
                            Message = "اسم المستخدم أو كلمة المرور غير صحيحة."
                        };
                    }

                    // تحديث بيانات المستخدم
                    user.LastLoginAt = DateTime.Now;
                    user.LoginCount++;
                    await _context.SaveChangesAsync();

                    // تعيين المستخدم الحالي
                    _currentUser = user;

                    // تحميل صلاحيات المستخدم
                    await LoadUserPermissionsAsync(user);

                    // تسجيل تسجيل دخول ناجح
                    await AuditTrailManager.LogLogin(user.Id, username, true);

                    return new LoginResult
                    {
                        Success = true,
                        Message = "تم تسجيل الدخول بنجاح.",
                        User = user
                    };
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityManager.Login");
                return new LoginResult
                {
                    Success = false,
                    Message = "حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى."
                };
            }
        }

        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        /// <returns>نجاح العملية</returns>
        public async Task<bool> LogoutAsync()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("SecurityManager.Logout", async () =>
                {
                    // التحقق من وجود مستخدم حالي
                    if (_currentUser == null)
                    {
                        return false;
                    }

                    // تسجيل تسجيل خروج
                    await AuditTrailManager.LogLogout(_currentUser.Id, _currentUser.Username);

                    // إعادة تعيين المستخدم الحالي
                    _currentUser = null;
                    _userPermissions.Clear();
                    _permissionCache.Clear();

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityManager.Logout");
                return false;
            }
        }

        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="currentPassword">كلمة المرور الحالية</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>نجاح العملية</returns>
        public async Task<ChangePasswordResult> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("SecurityManager.ChangePassword", async () =>
                {
                    // التحقق من صحة المدخلات
                    if (string.IsNullOrWhiteSpace(currentPassword) || string.IsNullOrWhiteSpace(newPassword))
                    {
                        return new ChangePasswordResult
                        {
                            Success = false,
                            Message = "يرجى إدخال كلمة المرور الحالية والجديدة."
                        };
                    }

                    // التحقق من قوة كلمة المرور الجديدة
                    var passwordValidation = InputValidator.ValidatePassword(newPassword);
                    if (!passwordValidation.IsValid)
                    {
                        return new ChangePasswordResult
                        {
                            Success = false,
                            Message = passwordValidation.Message
                        };
                    }

                    // تشفير كلمات المرور
                    string hashedCurrentPassword = EncryptionHelper.ComputeSha256Hash(currentPassword);
                    string hashedNewPassword = EncryptionHelper.ComputeSha256Hash(newPassword);

                    // البحث عن المستخدم
                    var user = await _context.Users.FindAsync(userId);

                    // التحقق من وجود المستخدم
                    if (user == null)
                    {
                        return new ChangePasswordResult
                        {
                            Success = false,
                            Message = "المستخدم غير موجود."
                        };
                    }

                    // التحقق من كلمة المرور الحالية
                    if (user.PasswordHash != hashedCurrentPassword)
                    {
                        return new ChangePasswordResult
                        {
                            Success = false,
                            Message = "كلمة المرور الحالية غير صحيحة."
                        };
                    }

                    // تحديث كلمة المرور
                    user.PasswordHash = hashedNewPassword;
                    user.PasswordChangedAt = DateTime.Now;
                    await _context.SaveChangesAsync();

                    // تسجيل تغيير كلمة المرور
                    await AuditTrailManager.LogAction("تغيير كلمة المرور", "User", userId, "تم تغيير كلمة المرور بنجاح.", userId);

                    return new ChangePasswordResult
                    {
                        Success = true,
                        Message = "تم تغيير كلمة المرور بنجاح."
                    };
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityManager.ChangePassword");
                return new ChangePasswordResult
                {
                    Success = false,
                    Message = "حدث خطأ أثناء تغيير كلمة المرور. يرجى المحاولة مرة أخرى."
                };
            }
        }

        /// <summary>
        /// إعادة تعيين كلمة المرور
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <param name="adminUserId">معرف المستخدم المسؤول</param>
        /// <returns>نجاح العملية</returns>
        public async Task<ChangePasswordResult> ResetPasswordAsync(int userId, string newPassword, int adminUserId)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("SecurityManager.ResetPassword", async () =>
                {
                    // التحقق من صحة المدخلات
                    if (string.IsNullOrWhiteSpace(newPassword))
                    {
                        return new ChangePasswordResult
                        {
                            Success = false,
                            Message = "يرجى إدخال كلمة المرور الجديدة."
                        };
                    }

                    // التحقق من قوة كلمة المرور الجديدة
                    var passwordValidation = InputValidator.ValidatePassword(newPassword);
                    if (!passwordValidation.IsValid)
                    {
                        return new ChangePasswordResult
                        {
                            Success = false,
                            Message = passwordValidation.Message
                        };
                    }

                    // التحقق من صلاحية المستخدم المسؤول
                    if (!await HasPermissionAsync(adminUserId, "users.reset_password"))
                    {
                        return new ChangePasswordResult
                        {
                            Success = false,
                            Message = "ليس لديك صلاحية إعادة تعيين كلمة المرور."
                        };
                    }

                    // تشفير كلمة المرور الجديدة
                    string hashedNewPassword = EncryptionHelper.ComputeSha256Hash(newPassword);

                    // البحث عن المستخدم
                    var user = await _context.Users.FindAsync(userId);

                    // التحقق من وجود المستخدم
                    if (user == null)
                    {
                        return new ChangePasswordResult
                        {
                            Success = false,
                            Message = "المستخدم غير موجود."
                        };
                    }

                    // تحديث كلمة المرور
                    user.PasswordHash = hashedNewPassword;
                    user.PasswordChangedAt = DateTime.Now;
                    user.RequirePasswordChange = true;
                    await _context.SaveChangesAsync();

                    // تسجيل إعادة تعيين كلمة المرور
                    await AuditTrailManager.LogAction("إعادة تعيين كلمة المرور", "User", userId, $"تم إعادة تعيين كلمة المرور بواسطة المستخدم {adminUserId}.", adminUserId);

                    return new ChangePasswordResult
                    {
                        Success = true,
                        Message = "تم إعادة تعيين كلمة المرور بنجاح."
                    };
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityManager.ResetPassword");
                return new ChangePasswordResult
                {
                    Success = false,
                    Message = "حدث خطأ أثناء إعادة تعيين كلمة المرور. يرجى المحاولة مرة أخرى."
                };
            }
        }

        /// <summary>
        /// التحقق من وجود صلاحية
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="permissionCode">رمز الصلاحية</param>
        /// <returns>وجود الصلاحية</returns>
        public async Task<bool> HasPermissionAsync(int userId, string permissionCode)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("SecurityManager.HasPermission", async () =>
                {
                    // التحقق من المستخدم الحالي
                    if (_currentUser != null && _currentUser.Id == userId)
                    {
                        // التحقق من وجود الصلاحية في الذاكرة المؤقتة
                        string cacheKey = $"{userId}:{permissionCode}";
                        if (_permissionCache.ContainsKey(cacheKey))
                        {
                            return _permissionCache[cacheKey];
                        }

                        // التحقق من وجود الصلاحية في قائمة صلاحيات المستخدم
                        bool hasPermission = _userPermissions.Contains(permissionCode);
                        _permissionCache[cacheKey] = hasPermission;
                        return hasPermission;
                    }

                    // البحث عن المستخدم وصلاحياته
                    var user = await _context.Users
                        .Include(u => u.UserRoles)
                            .ThenInclude(ur => ur.Role)
                                .ThenInclude(r => r.RolePermissions)
                                    .ThenInclude(rp => rp.Permission)
                        .FirstOrDefaultAsync(u => u.Id == userId);

                    // التحقق من وجود المستخدم
                    if (user == null)
                    {
                        return false;
                    }

                    // التحقق من وجود الصلاحية
                    bool hasUserPermission = user.UserRoles
                        .SelectMany(ur => ur.Role.RolePermissions)
                        .Any(rp => rp.Permission.Code == permissionCode);

                    // تخزين الصلاحية في الذاكرة المؤقتة
                    string userCacheKey = $"{userId}:{permissionCode}";
                    _permissionCache[userCacheKey] = hasUserPermission;

                    return hasUserPermission;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityManager.HasPermission");
                return false;
            }
        }

        /// <summary>
        /// تحميل صلاحيات المستخدم
        /// </summary>
        /// <param name="user">المستخدم</param>
        private async Task LoadUserPermissionsAsync(User user)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                await PerformanceHelper.MeasureExecutionTimeAsync("SecurityManager.LoadUserPermissions", async () =>
                {
                    // مسح الصلاحيات السابقة
                    _userPermissions.Clear();
                    _permissionCache.Clear();

                    // تحميل صلاحيات المستخدم
                    var permissions = user.UserRoles
                        .SelectMany(ur => ur.Role.RolePermissions)
                        .Select(rp => rp.Permission.Code)
                        .Distinct()
                        .ToList();

                    // إضافة الصلاحيات إلى القائمة
                    _userPermissions.AddRange(permissions);

                    // تسجيل عدد الصلاحيات
                    Debug.WriteLine($"[SECURITY] تم تحميل {_userPermissions.Count} صلاحية للمستخدم {user.Username}.");

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "SecurityManager.LoadUserPermissions");
            }
        }

        /// <summary>
        /// الحصول على المستخدم الحالي
        /// </summary>
        /// <returns>المستخدم الحالي</returns>
        public User? GetCurrentUser()
        {
            return _currentUser;
        }

        /// <summary>
        /// الحصول على صلاحيات المستخدم الحالي
        /// </summary>
        /// <returns>صلاحيات المستخدم الحالي</returns>
        public List<string> GetCurrentUserPermissions()
        {
            return _userPermissions.ToList();
        }
    }

    /// <summary>
    /// نتيجة تسجيل الدخول
    /// </summary>
    public class LoginResult
    {
        /// <summary>
        /// نجاح العملية
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// الرسالة
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// المستخدم
        /// </summary>
        public User? User { get; set; }
    }

    /// <summary>
    /// نتيجة تغيير كلمة المرور
    /// </summary>
    public class ChangePasswordResult
    {
        /// <summary>
        /// نجاح العملية
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// الرسالة
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }
}
