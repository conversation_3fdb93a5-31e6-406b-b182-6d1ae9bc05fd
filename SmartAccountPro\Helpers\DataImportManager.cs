using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using OfficeOpenXml;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Data;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لاستيراد البيانات من مصادر خارجية
    /// </summary>
    public class DataImportManager
    {
        private readonly AppDbContext _context;
        private readonly List<string> _supportedFormats;

        /// <summary>
        /// إنشاء مدير استيراد البيانات
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public DataImportManager(AppDbContext context)
        {
            _context = context;
            _supportedFormats = new List<string> { ".xlsx", ".xls", ".csv", ".txt" };
            
            // تعيين ترخيص EPPlus
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        /// <summary>
        /// استيراد الحسابات من ملف
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نتيجة الاستيراد</returns>
        public async Task<ImportResult> ImportAccountsAsync(string filePath, int userId)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("DataImportManager.ImportAccounts", async () =>
                {
                    var result = new ImportResult
                    {
                        FileName = Path.GetFileName(filePath),
                        ImportType = "Accounts",
                        StartTime = DateTime.Now
                    };

                    // التحقق من وجود الملف
                    if (!File.Exists(filePath))
                    {
                        throw new FileNotFoundException($"الملف غير موجود: {filePath}");
                    }

                    // التحقق من تنسيق الملف
                    string extension = Path.GetExtension(filePath).ToLower();
                    if (!_supportedFormats.Contains(extension))
                    {
                        throw new NotSupportedException($"تنسيق الملف غير مدعوم: {extension}");
                    }

                    // قراءة البيانات من الملف
                    var accountsData = await ReadAccountsFromFileAsync(filePath);
                    result.TotalRecords = accountsData.Count;

                    // معالجة البيانات
                    foreach (var accountData in accountsData)
                    {
                        try
                        {
                            // التحقق من صحة البيانات
                            var validation = ValidateAccountData(accountData);
                            if (!validation.IsValid)
                            {
                                result.Errors.Add($"السطر {accountData.RowNumber}: {validation.Message}");
                                result.FailedRecords++;
                                continue;
                            }

                            // التحقق من عدم وجود الحساب
                            var existingAccount = await _context.Accounts
                                .FirstOrDefaultAsync(a => a.Code == accountData.Code);

                            if (existingAccount != null)
                            {
                                result.Warnings.Add($"السطر {accountData.RowNumber}: الحساب {accountData.Code} موجود بالفعل");
                                result.SkippedRecords++;
                                continue;
                            }

                            // إنشاء الحساب
                            var account = new Account
                            {
                                Code = accountData.Code,
                                Name = accountData.Name,
                                Description = accountData.Description,
                                AccountType = accountData.AccountType,
                                ParentAccountId = accountData.ParentAccountId,
                                IsActive = true,
                                CreatedAt = DateTime.Now,
                                CreatedBy = userId
                            };

                            _context.Accounts.Add(account);
                            result.SuccessfulRecords++;
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"السطر {accountData.RowNumber}: {ex.Message}");
                            result.FailedRecords++;
                        }
                    }

                    // حفظ التغييرات
                    if (result.SuccessfulRecords > 0)
                    {
                        await _context.SaveChangesAsync();
                    }

                    result.EndTime = DateTime.Now;
                    result.Duration = result.EndTime - result.StartTime;
                    result.Success = result.FailedRecords == 0;

                    // تسجيل عملية الاستيراد
                    await AuditTrailManager.LogAction("استيراد حسابات", "Account", 0, 
                        $"تم استيراد {result.SuccessfulRecords} حساب من الملف {result.FileName}", userId);

                    Debug.WriteLine($"[IMPORT] تم استيراد {result.SuccessfulRecords} حساب من {result.TotalRecords} سجل");
                    return result;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataImportManager.ImportAccounts");
                throw;
            }
        }

        /// <summary>
        /// استيراد القيود المحاسبية من ملف
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نتيجة الاستيراد</returns>
        public async Task<ImportResult> ImportJournalEntriesAsync(string filePath, int userId)
        {
            try
            {
                return await PerformanceHelper.MeasureExecutionTimeAsync("DataImportManager.ImportJournalEntries", async () =>
                {
                    var result = new ImportResult
                    {
                        FileName = Path.GetFileName(filePath),
                        ImportType = "JournalEntries",
                        StartTime = DateTime.Now
                    };

                    // التحقق من وجود الملف
                    if (!File.Exists(filePath))
                    {
                        throw new FileNotFoundException($"الملف غير موجود: {filePath}");
                    }

                    // قراءة البيانات من الملف
                    var entriesData = await ReadJournalEntriesFromFileAsync(filePath);
                    result.TotalRecords = entriesData.Count;

                    // تجميع القيود حسب الرقم المرجعي
                    var groupedEntries = entriesData.GroupBy(e => e.ReferenceNumber).ToList();

                    foreach (var entryGroup in groupedEntries)
                    {
                        try
                        {
                            // التحقق من توازن القيد
                            var totalDebit = entryGroup.Sum(e => e.DebitAmount);
                            var totalCredit = entryGroup.Sum(e => e.CreditAmount);

                            if (totalDebit != totalCredit)
                            {
                                result.Errors.Add($"القيد {entryGroup.Key}: القيد غير متوازن (مدين: {totalDebit}, دائن: {totalCredit})");
                                result.FailedRecords += entryGroup.Count();
                                continue;
                            }

                            // التحقق من عدم وجود القيد
                            var existingEntry = await _context.JournalEntries
                                .FirstOrDefaultAsync(j => j.ReferenceNumber == entryGroup.Key);

                            if (existingEntry != null)
                            {
                                result.Warnings.Add($"القيد {entryGroup.Key}: موجود بالفعل");
                                result.SkippedRecords += entryGroup.Count();
                                continue;
                            }

                            // إنشاء القيد
                            var journalEntry = new JournalEntry
                            {
                                ReferenceNumber = entryGroup.Key,
                                EntryDate = entryGroup.First().EntryDate,
                                Description = entryGroup.First().Description,
                                CreatedAt = DateTime.Now,
                                CreatedBy = userId
                            };

                            _context.JournalEntries.Add(journalEntry);
                            await _context.SaveChangesAsync(); // حفظ للحصول على معرف القيد

                            // إضافة تفاصيل القيد
                            foreach (var detail in entryGroup)
                            {
                                // البحث عن الحساب
                                var account = await _context.Accounts
                                    .FirstOrDefaultAsync(a => a.Code == detail.AccountCode);

                                if (account == null)
                                {
                                    result.Errors.Add($"القيد {entryGroup.Key}: الحساب {detail.AccountCode} غير موجود");
                                    continue;
                                }

                                var journalEntryDetail = new JournalEntryDetail
                                {
                                    JournalEntryId = journalEntry.Id,
                                    AccountId = account.Id,
                                    Description = detail.DetailDescription,
                                    DebitAmount = detail.DebitAmount,
                                    CreditAmount = detail.CreditAmount
                                };

                                _context.JournalEntryDetails.Add(journalEntryDetail);
                            }

                            result.SuccessfulRecords += entryGroup.Count();
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"القيد {entryGroup.Key}: {ex.Message}");
                            result.FailedRecords += entryGroup.Count();
                        }
                    }

                    // حفظ التغييرات
                    if (result.SuccessfulRecords > 0)
                    {
                        await _context.SaveChangesAsync();
                    }

                    result.EndTime = DateTime.Now;
                    result.Duration = result.EndTime - result.StartTime;
                    result.Success = result.FailedRecords == 0;

                    // تسجيل عملية الاستيراد
                    await AuditTrailManager.LogAction("استيراد قيود محاسبية", "JournalEntry", 0, 
                        $"تم استيراد {result.SuccessfulRecords} تفصيل قيد من الملف {result.FileName}", userId);

                    Debug.WriteLine($"[IMPORT] تم استيراد {result.SuccessfulRecords} تفصيل قيد من {result.TotalRecords} سجل");
                    return result;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataImportManager.ImportJournalEntries");
                throw;
            }
        }

        /// <summary>
        /// اختيار ملف للاستيراد
        /// </summary>
        /// <returns>مسار الملف المختار</returns>
        public string? SelectImportFile()
        {
            try
            {
                return PerformanceHelper.MeasureExecutionTime("DataImportManager.SelectImportFile", () =>
                {
                    var openFileDialog = new OpenFileDialog
                    {
                        Title = "اختر ملف للاستيراد",
                        Filter = "ملفات Excel (*.xlsx;*.xls)|*.xlsx;*.xls|" +
                                "ملفات CSV (*.csv)|*.csv|" +
                                "ملفات نصية (*.txt)|*.txt|" +
                                "جميع الملفات المدعومة|*.xlsx;*.xls;*.csv;*.txt",
                        CheckFileExists = true,
                        Multiselect = false
                    };

                    return openFileDialog.ShowDialog() == true ? openFileDialog.FileName : null;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataImportManager.SelectImportFile");
                return null;
            }
        }

        /// <summary>
        /// قراءة الحسابات من ملف
        /// </summary>
        private async Task<List<AccountImportData>> ReadAccountsFromFileAsync(string filePath)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            
            return extension switch
            {
                ".xlsx" or ".xls" => await ReadAccountsFromExcelAsync(filePath),
                ".csv" => await ReadAccountsFromCsvAsync(filePath),
                ".txt" => await ReadAccountsFromTextAsync(filePath),
                _ => throw new NotSupportedException($"تنسيق الملف غير مدعوم: {extension}")
            };
        }

        /// <summary>
        /// قراءة الحسابات من ملف Excel
        /// </summary>
        private async Task<List<AccountImportData>> ReadAccountsFromExcelAsync(string filePath)
        {
            var accounts = new List<AccountImportData>();

            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                var worksheet = package.Workbook.Worksheets[0];
                int rowCount = worksheet.Dimension?.Rows ?? 0;

                for (int row = 2; row <= rowCount; row++) // تخطي الصف الأول (العناوين)
                {
                    var account = new AccountImportData
                    {
                        RowNumber = row,
                        Code = worksheet.Cells[row, 1].Text?.Trim() ?? "",
                        Name = worksheet.Cells[row, 2].Text?.Trim() ?? "",
                        Description = worksheet.Cells[row, 3].Text?.Trim() ?? "",
                        AccountType = ParseAccountType(worksheet.Cells[row, 4].Text?.Trim() ?? ""),
                        ParentAccountCode = worksheet.Cells[row, 5].Text?.Trim()
                    };

                    // البحث عن الحساب الأب
                    if (!string.IsNullOrEmpty(account.ParentAccountCode))
                    {
                        var parentAccount = await _context.Accounts
                            .FirstOrDefaultAsync(a => a.Code == account.ParentAccountCode);
                        account.ParentAccountId = parentAccount?.Id;
                    }

                    accounts.Add(account);
                }
            }

            return accounts;
        }

        /// <summary>
        /// قراءة الحسابات من ملف CSV
        /// </summary>
        private async Task<List<AccountImportData>> ReadAccountsFromCsvAsync(string filePath)
        {
            var accounts = new List<AccountImportData>();
            var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);

            for (int i = 1; i < lines.Length; i++) // تخطي الصف الأول (العناوين)
            {
                var fields = lines[i].Split(',');
                if (fields.Length >= 4)
                {
                    var account = new AccountImportData
                    {
                        RowNumber = i + 1,
                        Code = fields[0].Trim().Trim('"'),
                        Name = fields[1].Trim().Trim('"'),
                        Description = fields[2].Trim().Trim('"'),
                        AccountType = ParseAccountType(fields[3].Trim().Trim('"')),
                        ParentAccountCode = fields.Length > 4 ? fields[4].Trim().Trim('"') : null
                    };

                    // البحث عن الحساب الأب
                    if (!string.IsNullOrEmpty(account.ParentAccountCode))
                    {
                        var parentAccount = await _context.Accounts
                            .FirstOrDefaultAsync(a => a.Code == account.ParentAccountCode);
                        account.ParentAccountId = parentAccount?.Id;
                    }

                    accounts.Add(account);
                }
            }

            return accounts;
        }

        /// <summary>
        /// قراءة الحسابات من ملف نصي
        /// </summary>
        private async Task<List<AccountImportData>> ReadAccountsFromTextAsync(string filePath)
        {
            // نفس منطق CSV ولكن مع فاصل Tab
            var accounts = new List<AccountImportData>();
            var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);

            for (int i = 1; i < lines.Length; i++)
            {
                var fields = lines[i].Split('\t');
                if (fields.Length >= 4)
                {
                    var account = new AccountImportData
                    {
                        RowNumber = i + 1,
                        Code = fields[0].Trim(),
                        Name = fields[1].Trim(),
                        Description = fields[2].Trim(),
                        AccountType = ParseAccountType(fields[3].Trim()),
                        ParentAccountCode = fields.Length > 4 ? fields[4].Trim() : null
                    };

                    // البحث عن الحساب الأب
                    if (!string.IsNullOrEmpty(account.ParentAccountCode))
                    {
                        var parentAccount = await _context.Accounts
                            .FirstOrDefaultAsync(a => a.Code == account.ParentAccountCode);
                        account.ParentAccountId = parentAccount?.Id;
                    }

                    accounts.Add(account);
                }
            }

            return accounts;
        }

        /// <summary>
        /// قراءة القيود المحاسبية من ملف
        /// </summary>
        private async Task<List<JournalEntryImportData>> ReadJournalEntriesFromFileAsync(string filePath)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            
            return extension switch
            {
                ".xlsx" or ".xls" => await ReadJournalEntriesFromExcelAsync(filePath),
                ".csv" => await ReadJournalEntriesFromCsvAsync(filePath),
                ".txt" => await ReadJournalEntriesFromTextAsync(filePath),
                _ => throw new NotSupportedException($"تنسيق الملف غير مدعوم: {extension}")
            };
        }

        /// <summary>
        /// قراءة القيود من ملف Excel
        /// </summary>
        private async Task<List<JournalEntryImportData>> ReadJournalEntriesFromExcelAsync(string filePath)
        {
            var entries = new List<JournalEntryImportData>();

            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                var worksheet = package.Workbook.Worksheets[0];
                int rowCount = worksheet.Dimension?.Rows ?? 0;

                for (int row = 2; row <= rowCount; row++)
                {
                    var entry = new JournalEntryImportData
                    {
                        RowNumber = row,
                        ReferenceNumber = worksheet.Cells[row, 1].Text?.Trim() ?? "",
                        EntryDate = DateTime.TryParse(worksheet.Cells[row, 2].Text, out var date) ? date : DateTime.Now,
                        Description = worksheet.Cells[row, 3].Text?.Trim() ?? "",
                        AccountCode = worksheet.Cells[row, 4].Text?.Trim() ?? "",
                        DetailDescription = worksheet.Cells[row, 5].Text?.Trim() ?? "",
                        DebitAmount = decimal.TryParse(worksheet.Cells[row, 6].Text, out var debit) ? debit : 0,
                        CreditAmount = decimal.TryParse(worksheet.Cells[row, 7].Text, out var credit) ? credit : 0
                    };

                    entries.Add(entry);
                }
            }

            return entries;
        }

        /// <summary>
        /// قراءة القيود من ملف CSV
        /// </summary>
        private async Task<List<JournalEntryImportData>> ReadJournalEntriesFromCsvAsync(string filePath)
        {
            var entries = new List<JournalEntryImportData>();
            var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);

            for (int i = 1; i < lines.Length; i++)
            {
                var fields = lines[i].Split(',');
                if (fields.Length >= 7)
                {
                    var entry = new JournalEntryImportData
                    {
                        RowNumber = i + 1,
                        ReferenceNumber = fields[0].Trim().Trim('"'),
                        EntryDate = DateTime.TryParse(fields[1].Trim().Trim('"'), out var date) ? date : DateTime.Now,
                        Description = fields[2].Trim().Trim('"'),
                        AccountCode = fields[3].Trim().Trim('"'),
                        DetailDescription = fields[4].Trim().Trim('"'),
                        DebitAmount = decimal.TryParse(fields[5].Trim().Trim('"'), out var debit) ? debit : 0,
                        CreditAmount = decimal.TryParse(fields[6].Trim().Trim('"'), out var credit) ? credit : 0
                    };

                    entries.Add(entry);
                }
            }

            return entries;
        }

        /// <summary>
        /// قراءة القيود من ملف نصي
        /// </summary>
        private async Task<List<JournalEntryImportData>> ReadJournalEntriesFromTextAsync(string filePath)
        {
            var entries = new List<JournalEntryImportData>();
            var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);

            for (int i = 1; i < lines.Length; i++)
            {
                var fields = lines[i].Split('\t');
                if (fields.Length >= 7)
                {
                    var entry = new JournalEntryImportData
                    {
                        RowNumber = i + 1,
                        ReferenceNumber = fields[0].Trim(),
                        EntryDate = DateTime.TryParse(fields[1].Trim(), out var date) ? date : DateTime.Now,
                        Description = fields[2].Trim(),
                        AccountCode = fields[3].Trim(),
                        DetailDescription = fields[4].Trim(),
                        DebitAmount = decimal.TryParse(fields[5].Trim(), out var debit) ? debit : 0,
                        CreditAmount = decimal.TryParse(fields[6].Trim(), out var credit) ? credit : 0
                    };

                    entries.Add(entry);
                }
            }

            return entries;
        }

        /// <summary>
        /// تحليل نوع الحساب
        /// </summary>
        private AccountType ParseAccountType(string accountTypeText)
        {
            return accountTypeText.ToLower() switch
            {
                "أصل" or "asset" => AccountType.Asset,
                "التزام" or "liability" => AccountType.Liability,
                "حقوق ملكية" or "equity" => AccountType.Equity,
                "إيراد" or "revenue" => AccountType.Revenue,
                "مصروف" or "expense" => AccountType.Expense,
                _ => AccountType.Asset
            };
        }

        /// <summary>
        /// التحقق من صحة بيانات الحساب
        /// </summary>
        private ValidationResult ValidateAccountData(AccountImportData accountData)
        {
            if (string.IsNullOrWhiteSpace(accountData.Code))
            {
                return new ValidationResult(false, "رمز الحساب مطلوب");
            }

            if (string.IsNullOrWhiteSpace(accountData.Name))
            {
                return new ValidationResult(false, "اسم الحساب مطلوب");
            }

            return new ValidationResult(true, "البيانات صحيحة");
        }
    }

    /// <summary>
    /// بيانات استيراد الحساب
    /// </summary>
    public class AccountImportData
    {
        public int RowNumber { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public AccountType AccountType { get; set; }
        public string? ParentAccountCode { get; set; }
        public int? ParentAccountId { get; set; }
    }

    /// <summary>
    /// بيانات استيراد القيد المحاسبي
    /// </summary>
    public class JournalEntryImportData
    {
        public int RowNumber { get; set; }
        public string ReferenceNumber { get; set; } = string.Empty;
        public DateTime EntryDate { get; set; }
        public string Description { get; set; } = string.Empty;
        public string AccountCode { get; set; } = string.Empty;
        public string DetailDescription { get; set; } = string.Empty;
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
    }

    /// <summary>
    /// نتيجة الاستيراد
    /// </summary>
    public class ImportResult
    {
        public string FileName { get; set; } = string.Empty;
        public string ImportType { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public int TotalRecords { get; set; }
        public int SuccessfulRecords { get; set; }
        public int FailedRecords { get; set; }
        public int SkippedRecords { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
    }
}
