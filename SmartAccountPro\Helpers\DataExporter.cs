using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Xml;
using System.Xml.Serialization;
using Newtonsoft.Json;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لتصدير البيانات
    /// </summary>
    public static class DataExporter
    {
        /// <summary>
        /// تصدير البيانات إلى ملف CSV
        /// </summary>
        /// <typeparam name="T">نوع البيانات</typeparam>
        /// <param name="data">البيانات</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="separator">فاصل الحقول</param>
        /// <returns>نجاح العملية</returns>
        public static async Task<bool> ExportToCsvAsync<T>(IEnumerable<T> data, string filePath, char separator = ',')
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("DataExporter.ExportToCsv", async () =>
                {
                    // التحقق من البيانات
                    if (data == null || !data.Any())
                    {
                        MessageBox.Show("لا توجد بيانات للتصدير.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return false;
                    }

                    // إنشاء مسار الملف إذا لم يكن موجوداً
                    var directory = Path.GetDirectoryName(filePath);
                    if (!Directory.Exists(directory) && !string.IsNullOrEmpty(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // الحصول على خصائص النوع
                    var properties = typeof(T).GetProperties();

                    // إنشاء ملف CSV
                    using (var writer = new StreamWriter(filePath, false, Encoding.UTF8))
                    {
                        // كتابة رؤوس الأعمدة
                        var header = string.Join(separator, properties.Select(p => p.Name));
                        await writer.WriteLineAsync(header);

                        // كتابة البيانات
                        foreach (var item in data)
                        {
                            var values = properties.Select(p => FormatValue(p.GetValue(item), separator));
                            var line = string.Join(separator, values);
                            await writer.WriteLineAsync(line);
                        }
                    }

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم تصدير البيانات",
                        $"تم تصدير البيانات إلى ملف CSV بنجاح: {Path.GetFileName(filePath)}",
                        NotificationType.Success);

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataExporter.ExportToCsv");
                return false;
            }
        }

        /// <summary>
        /// تصدير البيانات إلى ملف JSON
        /// </summary>
        /// <typeparam name="T">نوع البيانات</typeparam>
        /// <param name="data">البيانات</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>نجاح العملية</returns>
        public static async Task<bool> ExportToJsonAsync<T>(IEnumerable<T> data, string filePath)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("DataExporter.ExportToJson", async () =>
                {
                    // التحقق من البيانات
                    if (data == null || !data.Any())
                    {
                        MessageBox.Show("لا توجد بيانات للتصدير.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return false;
                    }

                    // إنشاء مسار الملف إذا لم يكن موجوداً
                    var directory = Path.GetDirectoryName(filePath);
                    if (!Directory.Exists(directory) && !string.IsNullOrEmpty(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // تحويل البيانات إلى JSON
                    var json = JsonConvert.SerializeObject(data, Newtonsoft.Json.Formatting.Indented);

                    // كتابة البيانات إلى ملف
                    await File.WriteAllTextAsync(filePath, json, Encoding.UTF8);

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم تصدير البيانات",
                        $"تم تصدير البيانات إلى ملف JSON بنجاح: {Path.GetFileName(filePath)}",
                        NotificationType.Success);

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataExporter.ExportToJson");
                return false;
            }
        }

        /// <summary>
        /// تصدير البيانات إلى ملف XML
        /// </summary>
        /// <typeparam name="T">نوع البيانات</typeparam>
        /// <param name="data">البيانات</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>نجاح العملية</returns>
        public static async Task<bool> ExportToXmlAsync<T>(IEnumerable<T> data, string filePath)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("DataExporter.ExportToXml", async () =>
                {
                    // التحقق من البيانات
                    if (data == null || !data.Any())
                    {
                        MessageBox.Show("لا توجد بيانات للتصدير.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return false;
                    }

                    // إنشاء مسار الملف إذا لم يكن موجوداً
                    var directory = Path.GetDirectoryName(filePath);
                    if (!Directory.Exists(directory) && !string.IsNullOrEmpty(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // إنشاء محول XML
                    var serializer = new XmlSerializer(typeof(List<T>));
                    var settings = new XmlWriterSettings
                    {
                        Indent = true,
                        Encoding = Encoding.UTF8
                    };

                    // كتابة البيانات إلى ملف
                    using (var writer = XmlWriter.Create(filePath, settings))
                    {
                        serializer.Serialize(writer, data.ToList());
                    }

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم تصدير البيانات",
                        $"تم تصدير البيانات إلى ملف XML بنجاح: {Path.GetFileName(filePath)}",
                        NotificationType.Success);

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataExporter.ExportToXml");
                return false;
            }
        }

        /// <summary>
        /// تنسيق قيمة للتصدير إلى CSV
        /// </summary>
        /// <param name="value">القيمة</param>
        /// <param name="separator">الفاصل</param>
        /// <returns>القيمة المنسقة</returns>
        private static string FormatValue(object? value, char separator)
        {
            if (value == null)
                return string.Empty;

            var stringValue = value.ToString() ?? string.Empty;

            // إذا كانت القيمة تحتوي على الفاصل أو أسطر جديدة، قم بتغليفها بعلامات اقتباس
            if (stringValue.Contains(separator) || stringValue.Contains("\"") || stringValue.Contains("\n"))
            {
                // استبدال علامات الاقتباس بعلامات اقتباس مزدوجة
                stringValue = stringValue.Replace("\"", "\"\"");
                // تغليف القيمة بعلامات اقتباس
                stringValue = $"\"{stringValue}\"";
            }

            return stringValue;
        }

        /// <summary>
        /// فتح ملف التصدير
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>نجاح العملية</returns>
        public static bool OpenExportFile(string filePath)
        {
            try
            {
                // التحقق من وجود الملف
                if (!File.Exists(filePath))
                {
                    MessageBox.Show("الملف غير موجود.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // فتح الملف باستخدام التطبيق الافتراضي
                Process.Start(new ProcessStartInfo
                {
                    FileName = filePath,
                    UseShellExecute = true
                });

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "DataExporter.OpenExportFile");
                return false;
            }
        }
    }
}
