<UserControl x:Class="SmartAccountPro.Views.JournalEntryView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SmartAccountPro.Views"
             xmlns:sys="clr-namespace:System;assembly=mscorlib"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             FlowDirection="RightToLeft">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0"
                Background="{DynamicResource PrimaryColor}"
                CornerRadius="10"
                Padding="15"
                Margin="0,0,0,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="2" BlurRadius="10" Opacity="0.2" Color="#000000"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="&#xE8A5;"
                           FontFamily="Segoe MDL2 Assets"
                           FontSize="24"
                           Foreground="White"
                           VerticalAlignment="Center"
                           Margin="0,0,10,0"/>
                <TextBlock Text="قيد محاسبي جديد"
                           FontSize="22"
                           FontWeight="Bold"
                           Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- معلومات القيد -->
        <Border Grid.Row="1"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="10"
                Padding="15"
                Margin="0,0,0,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم القيد:" Margin="0,8,10,8" VerticalAlignment="Center"/>
                <Border Grid.Row="0" Grid.Column="1"
                        BorderBrush="#DDDDDD"
                        BorderThickness="1"
                        CornerRadius="4"
                        Margin="0,5,10,5">
                    <TextBox BorderThickness="0"
                             Background="Transparent"
                             Padding="8,5"
                             IsReadOnly="True"
                             Text="JE-00001"/>
                </Border>

                <TextBlock Grid.Row="0" Grid.Column="2" Text="التاريخ:" Margin="0,8,10,8" VerticalAlignment="Center"/>
                <Border Grid.Row="0" Grid.Column="3"
                        BorderBrush="#DDDDDD"
                        BorderThickness="1"
                        CornerRadius="4"
                        Margin="0,5,10,5">
                    <DatePicker BorderThickness="0"
                                Background="Transparent"
                                Padding="5,3"
                                SelectedDate="{Binding Source={x:Static sys:DateTime.Now}}"/>
                </Border>

                <TextBlock Grid.Row="0" Grid.Column="4" Text="المصدر:" Margin="0,8,10,8" VerticalAlignment="Center"/>
                <Border Grid.Row="0" Grid.Column="5"
                        BorderBrush="#DDDDDD"
                        BorderThickness="1"
                        CornerRadius="4"
                        Margin="0,5,0,5">
                    <ComboBox BorderThickness="0"
                              Background="Transparent"
                              Padding="5,3"
                              SelectedIndex="0">
                        <ComboBoxItem Content="قيد يدوي"/>
                        <ComboBoxItem Content="فاتورة مبيعات"/>
                        <ComboBoxItem Content="فاتورة مشتريات"/>
                        <ComboBoxItem Content="سند قبض"/>
                        <ComboBoxItem Content="سند صرف"/>
                        <ComboBoxItem Content="قيد افتتاحي"/>
                        <ComboBoxItem Content="قيد تسوية"/>
                    </ComboBox>
                </Border>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="رقم المرجع:" Margin="0,8,10,8" VerticalAlignment="Center"/>
                <Border Grid.Row="1" Grid.Column="1"
                        BorderBrush="#DDDDDD"
                        BorderThickness="1"
                        CornerRadius="4"
                        Margin="0,5,10,5">
                    <TextBox BorderThickness="0"
                             Background="Transparent"
                             Padding="8,5"/>
                </Border>

                <TextBlock Grid.Row="1" Grid.Column="2" Text="البيان:" Margin="0,8,10,8" VerticalAlignment="Center"/>
                <Border Grid.Row="1" Grid.Column="3" Grid.ColumnSpan="3"
                        BorderBrush="#DDDDDD"
                        BorderThickness="1"
                        CornerRadius="4"
                        Margin="0,5,0,5">
                    <TextBox BorderThickness="0"
                             Background="Transparent"
                             Padding="8,5"/>
                </Border>
            </Grid>
        </Border>

        <!-- بنود القيد -->
        <Border Grid.Row="2"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="10"
                Padding="15"
                Margin="0,0,0,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <Button x:Name="AddItemButton"
                            Style="{StaticResource ModernButton}"
                            Margin="0,0,10,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE710;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة بند"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="DeleteItemButton"
                            Style="{StaticResource RedButton}"
                            Margin="0,0,10,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE74D;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="حذف بند"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <DataGrid Grid.Row="1"
                          x:Name="JournalItemsDataGrid"
                          AutoGenerateColumns="False"
                          BorderThickness="1"
                          BorderBrush="#E0E0E0"
                          Background="White"
                          RowBackground="White"
                          AlternatingRowBackground="#F8F9FA"
                          GridLinesVisibility="Horizontal"
                          HorizontalGridLinesBrush="#E0E0E0">
                    <DataGrid.Resources>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#F1F3F4"/>
                            <Setter Property="Padding" Value="10,8"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="#" Width="50" IsReadOnly="True" Binding="{Binding Index}"/>
                        <DataGridTemplateColumn Header="الحساب" Width="*">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding AccountName}" Padding="5,3"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                            <DataGridTemplateColumn.CellEditingTemplate>
                                <DataTemplate>
                                    <ComboBox ItemsSource="{Binding Accounts}"
                                              DisplayMemberPath="Name"
                                              SelectedValuePath="Id"
                                              Padding="5,3"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellEditingTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTextColumn Header="البيان" Width="*" Binding="{Binding Description}"/>
                        <DataGridTextColumn Header="مدين" Width="120" Binding="{Binding Debit, StringFormat=N2}"/>
                        <DataGridTextColumn Header="دائن" Width="120" Binding="{Binding Credit, StringFormat=N2}"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- الإجماليات وأزرار التحكم -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- الإجماليات -->
            <Border Grid.Column="0"
                    Background="#F8F9FA"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1"
                    CornerRadius="10"
                    Padding="15,10"
                    Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <Border Background="#1A73E8"
                            CornerRadius="5"
                            Padding="8,5"
                            Margin="0,0,10,0">
                        <TextBlock Text="إجمالي المدين:"
                                   FontWeight="Bold"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                    </Border>
                    <TextBlock x:Name="TotalDebitText"
                               Text="0.00"
                               Margin="5,0,15,0"
                               FontSize="16"
                               FontWeight="SemiBold"
                               VerticalAlignment="Center"/>

                    <Border Background="#4CAF50"
                            CornerRadius="5"
                            Padding="8,5"
                            Margin="0,0,10,0">
                        <TextBlock Text="إجمالي الدائن:"
                                   FontWeight="Bold"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                    </Border>
                    <TextBlock x:Name="TotalCreditText"
                               Text="0.00"
                               Margin="5,0,15,0"
                               FontSize="16"
                               FontWeight="SemiBold"
                               VerticalAlignment="Center"/>

                    <Border Background="#F44336"
                            CornerRadius="5"
                            Padding="8,5"
                            Margin="0,0,10,0">
                        <TextBlock Text="الفرق:"
                                   FontWeight="Bold"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                    </Border>
                    <TextBlock x:Name="DifferenceText"
                               Text="0.00"
                               Foreground="#F44336"
                               FontSize="16"
                               FontWeight="SemiBold"
                               Margin="5,0,0,0"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- أزرار التحكم -->
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button x:Name="SaveButton"
                        Style="{StaticResource ModernButton}"
                        Padding="15,8"
                        Margin="0,0,10,0">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE74E;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button x:Name="SaveAndPostButton"
                        Style="{StaticResource GreenButton}"
                        Padding="15,8"
                        Margin="0,0,10,0">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE72A;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ وترحيل"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button x:Name="CancelButton"
                        Style="{StaticResource RedButton}"
                        Padding="15,8">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE711;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
