using SmartAccountPro.Core.Models;
using SmartAccountPro.Data.Services;
using SmartAccountPro.Helpers;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.EntityFrameworkCore;

namespace SmartAccountPro.ViewModels
{
    /// <summary>
    /// نموذج عرض المهام
    /// </summary>
    public class TasksViewModel : ViewModelBase
    {
        private ObservableCollection<TaskItem> _tasks;
        private ObservableCollection<TaskItem> _filteredTasks;
        private TaskService _taskService;
        private bool _hasNoTasks;
        private bool _isTaskDialogOpen;
        private string _taskDialogTitle;
        private TaskItem _currentTask;
        private ObservableCollection<TaskStatusItem> _taskStatuses;
        private TaskStatusItem _selectedTaskStatus;
        private ObservableCollection<TaskPriorityItem> _taskPriorities;
        private TaskPriorityItem _selectedTaskPriority;
        private ObservableCollection<User> _users;
        private User _selectedUser;
        private ObservableCollection<DueDateFilterItem> _dueDateFilters;
        private DueDateFilterItem _selectedDueDateFilter;
        private ObservableCollection<Account> _accounts;
        private Account _selectedAccount;
        private TaskStatusItem _selectedTaskStatusForDialog;
        private TaskPriorityItem _selectedTaskPriorityForDialog;
        private User _selectedUserForDialog;
        private Account _selectedAccountForDialog;
        private ObservableCollection<RecurrencePatternItem> _recurrencePatterns;
        private RecurrencePatternItem _selectedRecurrencePatternForDialog;

        /// <summary>
        /// قائمة المهام
        /// </summary>
        public ObservableCollection<TaskItem> Tasks
        {
            get => _tasks;
            set => SetProperty(ref _tasks, value);
        }

        /// <summary>
        /// قائمة المهام المصفاة
        /// </summary>
        public ObservableCollection<TaskItem> FilteredTasks
        {
            get => _filteredTasks;
            set
            {
                if (SetProperty(ref _filteredTasks, value))
                {
                    HasNoTasks = _filteredTasks == null || _filteredTasks.Count == 0;
                }
            }
        }

        /// <summary>
        /// هل لا توجد مهام
        /// </summary>
        public bool HasNoTasks
        {
            get => _hasNoTasks;
            set => SetProperty(ref _hasNoTasks, value);
        }

        /// <summary>
        /// هل نافذة المهمة مفتوحة
        /// </summary>
        public bool IsTaskDialogOpen
        {
            get => _isTaskDialogOpen;
            set => SetProperty(ref _isTaskDialogOpen, value);
        }

        /// <summary>
        /// عنوان نافذة المهمة
        /// </summary>
        public string TaskDialogTitle
        {
            get => _taskDialogTitle;
            set => SetProperty(ref _taskDialogTitle, value);
        }

        /// <summary>
        /// المهمة الحالية
        /// </summary>
        public TaskItem CurrentTask
        {
            get => _currentTask;
            set => SetProperty(ref _currentTask, value);
        }

        /// <summary>
        /// قائمة حالات المهام
        /// </summary>
        public ObservableCollection<TaskStatusItem> TaskStatuses
        {
            get => _taskStatuses;
            set => SetProperty(ref _taskStatuses, value);
        }

        /// <summary>
        /// حالة المهمة المحددة
        /// </summary>
        public TaskStatusItem SelectedTaskStatus
        {
            get => _selectedTaskStatus;
            set => SetProperty(ref _selectedTaskStatus, value);
        }

        /// <summary>
        /// قائمة أولويات المهام
        /// </summary>
        public ObservableCollection<TaskPriorityItem> TaskPriorities
        {
            get => _taskPriorities;
            set => SetProperty(ref _taskPriorities, value);
        }

        /// <summary>
        /// أولوية المهمة المحددة
        /// </summary>
        public TaskPriorityItem SelectedTaskPriority
        {
            get => _selectedTaskPriority;
            set => SetProperty(ref _selectedTaskPriority, value);
        }

        /// <summary>
        /// قائمة المستخدمين
        /// </summary>
        public ObservableCollection<User> Users
        {
            get => _users;
            set => SetProperty(ref _users, value);
        }

        /// <summary>
        /// المستخدم المحدد
        /// </summary>
        public User SelectedUser
        {
            get => _selectedUser;
            set => SetProperty(ref _selectedUser, value);
        }

        /// <summary>
        /// قائمة تصفية تاريخ الاستحقاق
        /// </summary>
        public ObservableCollection<DueDateFilterItem> DueDateFilters
        {
            get => _dueDateFilters;
            set => SetProperty(ref _dueDateFilters, value);
        }

        /// <summary>
        /// تصفية تاريخ الاستحقاق المحددة
        /// </summary>
        public DueDateFilterItem SelectedDueDateFilter
        {
            get => _selectedDueDateFilter;
            set => SetProperty(ref _selectedDueDateFilter, value);
        }

        /// <summary>
        /// قائمة الحسابات
        /// </summary>
        public ObservableCollection<Account> Accounts
        {
            get => _accounts;
            set => SetProperty(ref _accounts, value);
        }

        /// <summary>
        /// الحساب المحدد
        /// </summary>
        public Account SelectedAccount
        {
            get => _selectedAccount;
            set => SetProperty(ref _selectedAccount, value);
        }

        /// <summary>
        /// حالة المهمة المحددة للنافذة
        /// </summary>
        public TaskStatusItem SelectedTaskStatusForDialog
        {
            get => _selectedTaskStatusForDialog;
            set
            {
                if (SetProperty(ref _selectedTaskStatusForDialog, value) && CurrentTask != null && value != null)
                {
                    CurrentTask.Status = (TaskItemStatus)value.Id;
                }
            }
        }

        /// <summary>
        /// أولوية المهمة المحددة للنافذة
        /// </summary>
        public TaskPriorityItem SelectedTaskPriorityForDialog
        {
            get => _selectedTaskPriorityForDialog;
            set
            {
                if (SetProperty(ref _selectedTaskPriorityForDialog, value) && CurrentTask != null && value != null)
                {
                    CurrentTask.Priority = (TaskPriority)value.Id;
                }
            }
        }

        /// <summary>
        /// المستخدم المحدد للنافذة
        /// </summary>
        public User SelectedUserForDialog
        {
            get => _selectedUserForDialog;
            set
            {
                if (SetProperty(ref _selectedUserForDialog, value) && CurrentTask != null)
                {
                    CurrentTask.AssignedToUserId = value?.Id;
                    CurrentTask.AssignedToUser = value;
                }
            }
        }

        /// <summary>
        /// الحساب المحدد للنافذة
        /// </summary>
        public Account SelectedAccountForDialog
        {
            get => _selectedAccountForDialog;
            set
            {
                if (SetProperty(ref _selectedAccountForDialog, value) && CurrentTask != null)
                {
                    CurrentTask.RelatedAccountId = value?.Id;
                    CurrentTask.RelatedAccount = value;
                }
            }
        }

        /// <summary>
        /// قائمة أنماط التكرار
        /// </summary>
        public ObservableCollection<RecurrencePatternItem> RecurrencePatterns
        {
            get => _recurrencePatterns;
            set => SetProperty(ref _recurrencePatterns, value);
        }

        /// <summary>
        /// نمط التكرار المحدد للنافذة
        /// </summary>
        public RecurrencePatternItem SelectedRecurrencePatternForDialog
        {
            get => _selectedRecurrencePatternForDialog;
            set
            {
                if (SetProperty(ref _selectedRecurrencePatternForDialog, value) && CurrentTask != null && value != null)
                {
                    CurrentTask.RecurrencePattern = (RecurrencePattern)value.Id;
                }
            }
        }

        /// <summary>
        /// أمر إضافة مهمة
        /// </summary>
        public ICommand AddTaskCommand { get; }

        /// <summary>
        /// أمر تعديل مهمة
        /// </summary>
        public ICommand EditTaskCommand { get; }

        /// <summary>
        /// أمر حذف مهمة
        /// </summary>
        public ICommand DeleteTaskCommand { get; }

        /// <summary>
        /// أمر إكمال مهمة
        /// </summary>
        public ICommand CompleteTaskCommand { get; }

        /// <summary>
        /// أمر تطبيق التصفية
        /// </summary>
        public ICommand ApplyFilterCommand { get; }

        /// <summary>
        /// أمر تحديث البيانات
        /// </summary>
        public ICommand RefreshCommand { get; }

        /// <summary>
        /// أمر حفظ المهمة
        /// </summary>
        public ICommand SaveTaskCommand { get; }

        /// <summary>
        /// أمر إلغاء نافذة المهمة
        /// </summary>
        public ICommand CancelTaskDialogCommand { get; }

        public TasksViewModel()
        {
            // تهيئة الخدمات
            _taskService = new TaskService(App.DbContext);

            // تهيئة الأوامر
            AddTaskCommand = new RelayCommand(AddTask);
            EditTaskCommand = new RelayCommand<TaskItem>(EditTask);
            DeleteTaskCommand = new RelayCommand<TaskItem>(DeleteTask);
            CompleteTaskCommand = new RelayCommand<TaskItem>(CompleteTask);
            ApplyFilterCommand = new RelayCommand(ApplyFilter);
            RefreshCommand = new RelayCommand(LoadTasks);
            SaveTaskCommand = new RelayCommand(SaveTask);
            CancelTaskDialogCommand = new RelayCommand(CancelTaskDialog);

            // تهيئة البيانات
            InitializeData();
        }

        /// <summary>
        /// تهيئة البيانات
        /// </summary>
        private async void InitializeData()
        {
            try
            {
                // تهيئة حالات المهام
                TaskStatuses = new ObservableCollection<TaskStatusItem>
                {
                    new TaskStatusItem { Id = -1, Name = "الكل" },
                    new TaskStatusItem { Id = (int)TaskItemStatus.New, Name = "جديدة" },
                    new TaskStatusItem { Id = (int)TaskItemStatus.InProgress, Name = "قيد التنفيذ" },
                    new TaskStatusItem { Id = (int)TaskItemStatus.OnHold, Name = "معلقة" },
                    new TaskStatusItem { Id = (int)TaskItemStatus.Completed, Name = "مكتملة" },
                    new TaskStatusItem { Id = (int)TaskItemStatus.Cancelled, Name = "ملغاة" }
                };

                // تهيئة أولويات المهام
                TaskPriorities = new ObservableCollection<TaskPriorityItem>
                {
                    new TaskPriorityItem { Id = -1, Name = "الكل" },
                    new TaskPriorityItem { Id = (int)TaskPriority.Low, Name = "منخفضة" },
                    new TaskPriorityItem { Id = (int)TaskPriority.Medium, Name = "متوسطة" },
                    new TaskPriorityItem { Id = (int)TaskPriority.High, Name = "عالية" },
                    new TaskPriorityItem { Id = (int)TaskPriority.Critical, Name = "حرجة" }
                };

                // تهيئة تصفية تاريخ الاستحقاق
                DueDateFilters = new ObservableCollection<DueDateFilterItem>
                {
                    new DueDateFilterItem { Id = 0, Name = "الكل" },
                    new DueDateFilterItem { Id = 1, Name = "اليوم" },
                    new DueDateFilterItem { Id = 2, Name = "هذا الأسبوع" },
                    new DueDateFilterItem { Id = 3, Name = "هذا الشهر" },
                    new DueDateFilterItem { Id = 4, Name = "متأخرة" }
                };

                // تهيئة أنماط التكرار
                RecurrencePatterns = new ObservableCollection<RecurrencePatternItem>
                {
                    new RecurrencePatternItem { Id = (int)RecurrencePattern.Daily, Name = "يومي" },
                    new RecurrencePatternItem { Id = (int)RecurrencePattern.Weekly, Name = "أسبوعي" },
                    new RecurrencePatternItem { Id = (int)RecurrencePattern.Monthly, Name = "شهري" },
                    new RecurrencePatternItem { Id = (int)RecurrencePattern.Quarterly, Name = "ربع سنوي" },
                    new RecurrencePatternItem { Id = (int)RecurrencePattern.Yearly, Name = "سنوي" }
                };

                // تحديد القيم الافتراضية
                SelectedTaskStatus = TaskStatuses.First();
                SelectedTaskPriority = TaskPriorities.First();
                SelectedDueDateFilter = DueDateFilters.First();

                // تحميل المستخدمين
                var users = await App.DbContext.Users.AsNoTracking().ToListAsync();
                users.Insert(0, new User { Id = 0, FullName = "الكل" });
                Users = new ObservableCollection<User>(users);
                SelectedUser = Users.First();

                // تحميل الحسابات
                var accounts = await App.DbContext.Accounts.AsNoTracking().ToListAsync();
                accounts.Insert(0, new Account { Id = 0, Name = "بدون حساب" });
                Accounts = new ObservableCollection<Account>(accounts);
                SelectedAccount = Accounts.First();

                // تحميل المهام
                await LoadTasksAsync();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TasksViewModel.InitializeData");
            }
        }

        /// <summary>
        /// تحميل المهام
        /// </summary>
        private async System.Threading.Tasks.Task LoadTasksAsync()
        {
            try
            {
                var tasks = await _taskService.GetAllTasksAsync();
                Tasks = new ObservableCollection<TaskItem>(tasks);
                FilteredTasks = new ObservableCollection<TaskItem>(tasks);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TasksViewModel.LoadTasksAsync");
            }
        }

        /// <summary>
        /// تحميل المهام (للاستخدام مع الأوامر)
        /// </summary>
        private async void LoadTasks(object parameter)
        {
            await LoadTasksAsync();
        }

        /// <summary>
        /// تطبيق التصفية
        /// </summary>
        private void ApplyFilter(object parameter)
        {
            try
            {
                if (Tasks == null)
                {
                    FilteredTasks = new ObservableCollection<TaskItem>();
                    return;
                }

                var filteredTasks = Tasks.AsEnumerable();

                // تصفية حسب الحالة
                if (SelectedTaskStatus != null && SelectedTaskStatus.Id >= 0)
                {
                    filteredTasks = filteredTasks.Where(t => t.Status == (TaskItemStatus)SelectedTaskStatus.Id);
                }

                // تصفية حسب الأولوية
                if (SelectedTaskPriority != null && SelectedTaskPriority.Id >= 0)
                {
                    filteredTasks = filteredTasks.Where(t => t.Priority == (TaskPriority)SelectedTaskPriority.Id);
                }

                // تصفية حسب المستخدم
                if (SelectedUser != null && SelectedUser.Id > 0)
                {
                    filteredTasks = filteredTasks.Where(t => t.AssignedToUserId == SelectedUser.Id);
                }

                // تصفية حسب تاريخ الاستحقاق
                if (SelectedDueDateFilter != null)
                {
                    var today = DateTime.Today;
                    var tomorrow = today.AddDays(1);
                    var weekStart = today.AddDays(-(int)today.DayOfWeek);
                    var weekEnd = weekStart.AddDays(7);
                    var monthStart = new DateTime(today.Year, today.Month, 1);
                    var monthEnd = monthStart.AddMonths(1);

                    switch (SelectedDueDateFilter.Id)
                    {
                        case 1: // اليوم
                            filteredTasks = filteredTasks.Where(t => t.DueDate >= today && t.DueDate < tomorrow);
                            break;
                        case 2: // هذا الأسبوع
                            filteredTasks = filteredTasks.Where(t => t.DueDate >= weekStart && t.DueDate < weekEnd);
                            break;
                        case 3: // هذا الشهر
                            filteredTasks = filteredTasks.Where(t => t.DueDate >= monthStart && t.DueDate < monthEnd);
                            break;
                        case 4: // متأخرة
                            filteredTasks = filteredTasks.Where(t => t.DueDate < today && t.Status != TaskItemStatus.Completed && t.Status != TaskItemStatus.Cancelled);
                            break;
                    }
                }

                FilteredTasks = new ObservableCollection<TaskItem>(filteredTasks);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TasksViewModel.ApplyFilter");
            }
        }

        /// <summary>
        /// إضافة مهمة
        /// </summary>
        private void AddTask(object parameter)
        {
            try
            {
                // تهيئة مهمة جديدة
                CurrentTask = new TaskItem
                {
                    Title = "",
                    Description = "",
                    CreatedDate = DateTime.Now,
                    DueDate = DateTime.Now.AddDays(1),
                    Status = TaskItemStatus.New,
                    Priority = TaskPriority.Medium,
                    CreatedByUserId = 1, // استخدام معرف المستخدم الحالي
                    CompletionPercentage = 0,
                    IsRecurring = false
                };

                // تحديد القيم الافتراضية للنافذة
                SelectedTaskStatusForDialog = TaskStatuses.FirstOrDefault(s => s.Id == (int)CurrentTask.Status);
                SelectedTaskPriorityForDialog = TaskPriorities.FirstOrDefault(p => p.Id == (int)CurrentTask.Priority);
                SelectedUserForDialog = null;
                SelectedAccountForDialog = Accounts.First();
                SelectedRecurrencePatternForDialog = RecurrencePatterns.First();

                // فتح النافذة
                TaskDialogTitle = "إضافة مهمة جديدة";
                IsTaskDialogOpen = true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TasksViewModel.AddTask");
            }
        }

        /// <summary>
        /// تعديل مهمة
        /// </summary>
        private async void EditTask(TaskItem task)
        {
            try
            {
                if (task == null)
                {
                    return;
                }

                // تحميل المهمة بالكامل
                var fullTask = await _taskService.GetTaskByIdAsync(task.Id);
                if (fullTask == null)
                {
                    MessageBox.Show("لم يتم العثور على المهمة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // نسخ المهمة لتجنب التعديل المباشر
                CurrentTask = new TaskItem
                {
                    Id = fullTask.Id,
                    Title = fullTask.Title,
                    Description = fullTask.Description,
                    CreatedDate = fullTask.CreatedDate,
                    DueDate = fullTask.DueDate,
                    CompletedDate = fullTask.CompletedDate,
                    Status = fullTask.Status,
                    Priority = fullTask.Priority,
                    AssignedToUserId = fullTask.AssignedToUserId,
                    AssignedToUser = fullTask.AssignedToUser,
                    CreatedByUserId = fullTask.CreatedByUserId,
                    CreatedByUser = fullTask.CreatedByUser,
                    RelatedAccountId = fullTask.RelatedAccountId,
                    RelatedAccount = fullTask.RelatedAccount,
                    RelatedJournalEntryId = fullTask.RelatedJournalEntryId,
                    RelatedInvoiceId = fullTask.RelatedInvoiceId,
                    CompletionPercentage = fullTask.CompletionPercentage,
                    IsRecurring = fullTask.IsRecurring,
                    RecurrencePattern = fullTask.RecurrencePattern,
                    NextRecurrenceDate = fullTask.NextRecurrenceDate,
                    Notes = fullTask.Notes
                };

                // تحديد القيم للنافذة
                SelectedTaskStatusForDialog = TaskStatuses.FirstOrDefault(s => s.Id == (int)CurrentTask.Status);
                SelectedTaskPriorityForDialog = TaskPriorities.FirstOrDefault(p => p.Id == (int)CurrentTask.Priority);
                SelectedUserForDialog = Users.FirstOrDefault(u => u.Id == CurrentTask.AssignedToUserId);
                SelectedAccountForDialog = Accounts.FirstOrDefault(a => a.Id == CurrentTask.RelatedAccountId);
                SelectedRecurrencePatternForDialog = CurrentTask.RecurrencePattern.HasValue
                    ? RecurrencePatterns.FirstOrDefault(r => r.Id == (int)CurrentTask.RecurrencePattern.Value)
                    : RecurrencePatterns.First();

                // فتح النافذة
                TaskDialogTitle = "تعديل المهمة";
                IsTaskDialogOpen = true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TasksViewModel.EditTask");
            }
        }

        /// <summary>
        /// حذف مهمة
        /// </summary>
        private async void DeleteTask(TaskItem task)
        {
            try
            {
                if (task == null)
                {
                    return;
                }

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المهمة '{task.Title}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var success = await _taskService.DeleteTaskAsync(task.Id);
                    if (success)
                    {
                        Tasks.Remove(task);
                        FilteredTasks.Remove(task);
                        MessageBox.Show("تم حذف المهمة بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل حذف المهمة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TasksViewModel.DeleteTask");
            }
        }

        /// <summary>
        /// إكمال مهمة
        /// </summary>
        private async void CompleteTask(TaskItem task)
        {
            try
            {
                if (task == null)
                {
                    return;
                }

                // تحديث حالة المهمة
                task.Status = task.IsCompleted ? TaskItemStatus.Completed : TaskItemStatus.InProgress;
                task.CompletionPercentage = task.IsCompleted ? 100 : Math.Max(0, task.CompletionPercentage);
                task.CompletedDate = task.IsCompleted ? DateTime.Now : null;

                var success = await _taskService.UpdateTaskAsync(task);
                if (!success)
                {
                    MessageBox.Show("فشل تحديث حالة المهمة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }

                // تحديث القائمة
                await LoadTasksAsync();
                ApplyFilter(null);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TasksViewModel.CompleteTask");
            }
        }

        /// <summary>
        /// حفظ المهمة
        /// </summary>
        private async void SaveTask(object parameter)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(CurrentTask.Title))
                {
                    MessageBox.Show("يرجى إدخال عنوان المهمة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                bool success;
                if (CurrentTask.Id == 0)
                {
                    // إضافة مهمة جديدة
                    success = await _taskService.AddTaskAsync(CurrentTask);
                    if (success)
                    {
                        MessageBox.Show("تمت إضافة المهمة بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل إضافة المهمة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }
                else
                {
                    // تحديث مهمة موجودة
                    success = await _taskService.UpdateTaskAsync(CurrentTask);
                    if (success)
                    {
                        MessageBox.Show("تم تحديث المهمة بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل تحديث المهمة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }

                // إغلاق النافذة وتحديث القائمة
                IsTaskDialogOpen = false;
                await LoadTasksAsync();
                ApplyFilter(null);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TasksViewModel.SaveTask");
            }
        }

        /// <summary>
        /// إلغاء نافذة المهمة
        /// </summary>
        private void CancelTaskDialog(object parameter)
        {
            IsTaskDialogOpen = false;
        }
    }

    /// <summary>
    /// عنصر حالة المهمة
    /// </summary>
    public class TaskStatusItem
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    /// <summary>
    /// عنصر أولوية المهمة
    /// </summary>
    public class TaskPriorityItem
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    /// <summary>
    /// عنصر تصفية تاريخ الاستحقاق
    /// </summary>
    public class DueDateFilterItem
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    /// <summary>
    /// عنصر نمط التكرار
    /// </summary>
    public class RecurrencePatternItem
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
}
