using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace SmartAccountPro.Views
{
    /// <summary>
    /// شاشة إدخال قيد محاسبي
    /// </summary>
    public partial class JournalEntryView : UserControl
    {
        public JournalEntryView()
        {
            InitializeComponent();
            SetupEventHandlers();
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            AddItemButton.Click += AddItemButton_Click;
            DeleteItemButton.Click += DeleteItemButton_Click;
            SaveButton.Click += SaveButton_Click;
            SaveAndPostButton.Click += SaveAndPostButton_Click;
            CancelButton.Click += CancelButton_Click;
        }

        /// <summary>
        /// معالج حدث النقر على زر إضافة بند
        /// </summary>
        private void AddItemButton_Click(object sender, RoutedEventArgs e)
        {
            // هنا يمكن إضافة كود لإضافة بند جديد إلى الجدول
            MessageBox.Show("سيتم إضافة بند جديد", "إضافة بند", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// معالج حدث النقر على زر حذف بند
        /// </summary>
        private void DeleteItemButton_Click(object sender, RoutedEventArgs e)
        {
            if (JournalItemsDataGrid.SelectedItem == null)
            {
                MessageBox.Show("الرجاء اختيار بند لحذفه", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBoxResult result = MessageBox.Show(
                "هل أنت متأكد من حذف البند المحدد؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // هنا يمكن إضافة كود لحذف البند المحدد
                MessageBox.Show("تم حذف البند بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// معالج حدث النقر على زر حفظ
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateEntry())
            {
                return;
            }

            // هنا يمكن إضافة كود لحفظ القيد
            MessageBox.Show("تم حفظ القيد بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// معالج حدث النقر على زر حفظ وترحيل
        /// </summary>
        private void SaveAndPostButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateEntry())
            {
                return;
            }

            // هنا يمكن إضافة كود لحفظ وترحيل القيد
            MessageBox.Show("تم حفظ وترحيل القيد بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// معالج حدث النقر على زر إلغاء
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBoxResult result = MessageBox.Show(
                "هل أنت متأكد من إلغاء القيد؟ سيتم فقدان جميع البيانات المدخلة.",
                "تأكيد الإلغاء",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // هنا يمكن إضافة كود للعودة إلى الشاشة السابقة
                MessageBox.Show("تم إلغاء القيد", "إلغاء", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات القيد
        /// </summary>
        private bool ValidateEntry()
        {
            // هنا يمكن إضافة كود للتحقق من صحة بيانات القيد
            // مثال بسيط للتوضيح فقط
            if (JournalItemsDataGrid.Items.Count == 0)
            {
                MessageBox.Show("يجب إضافة بند واحد على الأقل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // التحقق من توازن القيد
            if (TotalDebitText.Text != TotalCreditText.Text)
            {
                MessageBox.Show("يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }
    }
}
