using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Data;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Xps;
using System.Windows.Xps.Packaging;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لإنشاء التقارير المالية
    /// </summary>
    public class ReportGenerator
    {
        private readonly AppDbContext _context;
        private readonly string _reportsPath;

        /// <summary>
        /// إنشاء مولد التقارير
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public ReportGenerator(AppDbContext context)
        {
            _context = context;
            _reportsPath = Path.Combine(App.AppDataPath, "Reports");

            // إنشاء مجلد التقارير إذا لم يكن موجوداً
            if (!Directory.Exists(_reportsPath))
            {
                Directory.CreateDirectory(_reportsPath);
            }
        }

        /// <summary>
        /// إنشاء تقرير ميزان المراجعة
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>مستند التقرير</returns>
        public async Task<FlowDocument> GenerateTrialBalanceReportAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ReportGenerator.GenerateTrialBalanceReport", async () =>
                {
                    // الحصول على بيانات ميزان المراجعة
                    var trialBalanceData = await GetTrialBalanceDataAsync(fromDate, toDate);

                    // إنشاء مستند التقرير
                    var document = new FlowDocument();
                    document.PagePadding = new Thickness(50);
                    document.ColumnGap = 0;
                    document.ColumnWidth = 700;

                    // إضافة عنوان التقرير
                    var titleParagraph = new Paragraph(new Run("تقرير ميزان المراجعة"))
                    {
                        FontSize = 24,
                        FontWeight = FontWeights.Bold,
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 20)
                    };
                    document.Blocks.Add(titleParagraph);

                    // إضافة معلومات التقرير
                    var infoParagraph = new Paragraph()
                    {
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 20)
                    };
                    infoParagraph.Inlines.Add(new Run($"الفترة من: {fromDate:yyyy-MM-dd} إلى: {toDate:yyyy-MM-dd}"));
                    document.Blocks.Add(infoParagraph);

                    // إنشاء جدول ميزان المراجعة
                    var table = new Table();
                    table.CellSpacing = 0;
                    table.BorderBrush = Brushes.Black;
                    table.BorderThickness = new Thickness(1);

                    // إضافة أعمدة الجدول
                    table.Columns.Add(new TableColumn() { Width = new GridLength(200) });
                    table.Columns.Add(new TableColumn() { Width = new GridLength(150) });
                    table.Columns.Add(new TableColumn() { Width = new GridLength(150) });
                    table.Columns.Add(new TableColumn() { Width = new GridLength(150) });
                    table.Columns.Add(new TableColumn() { Width = new GridLength(150) });

                    // إضافة مجموعة رأس الجدول
                    var headerRowGroup = new TableRowGroup();
                    table.RowGroups.Add(headerRowGroup);

                    // إضافة صف رأس الجدول
                    var headerRow = new TableRow();
                    headerRow.Background = Brushes.LightGray;
                    headerRow.FontWeight = FontWeights.Bold;

                    // إضافة خلايا رأس الجدول
                    headerRow.Cells.Add(CreateTableCell("الحساب", TextAlignment.Center));
                    headerRow.Cells.Add(CreateTableCell("مدين", TextAlignment.Center));
                    headerRow.Cells.Add(CreateTableCell("دائن", TextAlignment.Center));
                    headerRow.Cells.Add(CreateTableCell("رصيد مدين", TextAlignment.Center));
                    headerRow.Cells.Add(CreateTableCell("رصيد دائن", TextAlignment.Center));

                    headerRowGroup.Rows.Add(headerRow);

                    // إضافة مجموعة بيانات الجدول
                    var dataRowGroup = new TableRowGroup();
                    table.RowGroups.Add(dataRowGroup);

                    // إضافة صفوف البيانات
                    decimal totalDebit = 0;
                    decimal totalCredit = 0;
                    decimal totalDebitBalance = 0;
                    decimal totalCreditBalance = 0;

                    foreach (var item in trialBalanceData)
                    {
                        var dataRow = new TableRow();

                        dataRow.Cells.Add(CreateTableCell(item.AccountName, TextAlignment.Right));
                        dataRow.Cells.Add(CreateTableCell(item.Debit.ToString("N2"), TextAlignment.Left));
                        dataRow.Cells.Add(CreateTableCell(item.Credit.ToString("N2"), TextAlignment.Left));
                        dataRow.Cells.Add(CreateTableCell(item.DebitBalance > 0 ? item.DebitBalance.ToString("N2") : "", TextAlignment.Left));
                        dataRow.Cells.Add(CreateTableCell(item.CreditBalance > 0 ? item.CreditBalance.ToString("N2") : "", TextAlignment.Left));

                        dataRowGroup.Rows.Add(dataRow);

                        totalDebit += item.Debit;
                        totalCredit += item.Credit;
                        totalDebitBalance += item.DebitBalance;
                        totalCreditBalance += item.CreditBalance;
                    }

                    // إضافة صف المجموع
                    var totalRow = new TableRow();
                    totalRow.Background = Brushes.LightGray;
                    totalRow.FontWeight = FontWeights.Bold;

                    totalRow.Cells.Add(CreateTableCell("المجموع", TextAlignment.Right));
                    totalRow.Cells.Add(CreateTableCell(totalDebit.ToString("N2"), TextAlignment.Left));
                    totalRow.Cells.Add(CreateTableCell(totalCredit.ToString("N2"), TextAlignment.Left));
                    totalRow.Cells.Add(CreateTableCell(totalDebitBalance.ToString("N2"), TextAlignment.Left));
                    totalRow.Cells.Add(CreateTableCell(totalCreditBalance.ToString("N2"), TextAlignment.Left));

                    dataRowGroup.Rows.Add(totalRow);

                    // إضافة الجدول إلى المستند
                    document.Blocks.Add(table);

                    // إضافة معلومات إضافية
                    var footerParagraph = new Paragraph()
                    {
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 20, 0, 0)
                    };
                    footerParagraph.Inlines.Add(new Run($"تم إنشاء التقرير في: {DateTime.Now:yyyy-MM-dd HH:mm:ss}"));
                    document.Blocks.Add(footerParagraph);

                    return document;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ReportGenerator.GenerateTrialBalanceReport");
                return CreateErrorDocument("حدث خطأ أثناء إنشاء تقرير ميزان المراجعة.");
            }
        }

        /// <summary>
        /// إنشاء تقرير قائمة الدخل
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>مستند التقرير</returns>
        public async Task<FlowDocument> GenerateIncomeStatementReportAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ReportGenerator.GenerateIncomeStatementReport", async () =>
                {
                    // الحصول على بيانات قائمة الدخل
                    var incomeStatementData = await GetIncomeStatementDataAsync(fromDate, toDate);

                    // إنشاء مستند التقرير
                    var document = new FlowDocument();
                    document.PagePadding = new Thickness(50);
                    document.ColumnGap = 0;
                    document.ColumnWidth = 700;

                    // إضافة عنوان التقرير
                    var titleParagraph = new Paragraph(new Run("تقرير قائمة الدخل"))
                    {
                        FontSize = 24,
                        FontWeight = FontWeights.Bold,
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 20)
                    };
                    document.Blocks.Add(titleParagraph);

                    // إضافة معلومات التقرير
                    var infoParagraph = new Paragraph()
                    {
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 20)
                    };
                    infoParagraph.Inlines.Add(new Run($"الفترة من: {fromDate:yyyy-MM-dd} إلى: {toDate:yyyy-MM-dd}"));
                    document.Blocks.Add(infoParagraph);

                    // إنشاء جدول قائمة الدخل
                    var table = new Table();
                    table.CellSpacing = 0;
                    table.BorderBrush = Brushes.Black;
                    table.BorderThickness = new Thickness(1);

                    // إضافة أعمدة الجدول
                    table.Columns.Add(new TableColumn() { Width = new GridLength(400) });
                    table.Columns.Add(new TableColumn() { Width = new GridLength(150) });
                    table.Columns.Add(new TableColumn() { Width = new GridLength(150) });

                    // إضافة مجموعة رأس الجدول
                    var headerRowGroup = new TableRowGroup();
                    table.RowGroups.Add(headerRowGroup);

                    // إضافة صف رأس الجدول
                    var headerRow = new TableRow();
                    headerRow.Background = Brushes.LightGray;
                    headerRow.FontWeight = FontWeights.Bold;

                    // إضافة خلايا رأس الجدول
                    headerRow.Cells.Add(CreateTableCell("البيان", TextAlignment.Center));
                    headerRow.Cells.Add(CreateTableCell("جزئي", TextAlignment.Center));
                    headerRow.Cells.Add(CreateTableCell("إجمالي", TextAlignment.Center));

                    headerRowGroup.Rows.Add(headerRow);

                    // إضافة مجموعة بيانات الجدول
                    var dataRowGroup = new TableRowGroup();
                    table.RowGroups.Add(dataRowGroup);

                    // إضافة صفوف الإيرادات
                    var revenueHeaderRow = new TableRow();
                    revenueHeaderRow.FontWeight = FontWeights.Bold;
                    revenueHeaderRow.Cells.Add(CreateTableCell("الإيرادات", TextAlignment.Right));
                    revenueHeaderRow.Cells.Add(CreateTableCell("", TextAlignment.Left));
                    revenueHeaderRow.Cells.Add(CreateTableCell("", TextAlignment.Left));
                    dataRowGroup.Rows.Add(revenueHeaderRow);

                    decimal totalRevenue = 0;

                    foreach (var item in incomeStatementData.RevenueItems)
                    {
                        var dataRow = new TableRow();
                        dataRow.Cells.Add(CreateTableCell(item.AccountName, TextAlignment.Right, 20));
                        dataRow.Cells.Add(CreateTableCell(item.Amount.ToString("N2"), TextAlignment.Left));
                        dataRow.Cells.Add(CreateTableCell("", TextAlignment.Left));
                        dataRowGroup.Rows.Add(dataRow);

                        totalRevenue += item.Amount;
                    }

                    var revenueTotalRow = new TableRow();
                    revenueTotalRow.FontWeight = FontWeights.Bold;
                    revenueTotalRow.Cells.Add(CreateTableCell("إجمالي الإيرادات", TextAlignment.Right));
                    revenueTotalRow.Cells.Add(CreateTableCell("", TextAlignment.Left));
                    revenueTotalRow.Cells.Add(CreateTableCell(totalRevenue.ToString("N2"), TextAlignment.Left));
                    dataRowGroup.Rows.Add(revenueTotalRow);

                    // إضافة صف فارغ
                    var emptyRow = new TableRow();
                    emptyRow.Cells.Add(CreateTableCell("", TextAlignment.Right));
                    emptyRow.Cells.Add(CreateTableCell("", TextAlignment.Left));
                    emptyRow.Cells.Add(CreateTableCell("", TextAlignment.Left));
                    dataRowGroup.Rows.Add(emptyRow);

                    // إضافة صفوف المصروفات
                    var expenseHeaderRow = new TableRow();
                    expenseHeaderRow.FontWeight = FontWeights.Bold;
                    expenseHeaderRow.Cells.Add(CreateTableCell("المصروفات", TextAlignment.Right));
                    expenseHeaderRow.Cells.Add(CreateTableCell("", TextAlignment.Left));
                    expenseHeaderRow.Cells.Add(CreateTableCell("", TextAlignment.Left));
                    dataRowGroup.Rows.Add(expenseHeaderRow);

                    decimal totalExpense = 0;

                    foreach (var item in incomeStatementData.ExpenseItems)
                    {
                        var dataRow = new TableRow();
                        dataRow.Cells.Add(CreateTableCell(item.AccountName, TextAlignment.Right, 20));
                        dataRow.Cells.Add(CreateTableCell(item.Amount.ToString("N2"), TextAlignment.Left));
                        dataRow.Cells.Add(CreateTableCell("", TextAlignment.Left));
                        dataRowGroup.Rows.Add(dataRow);

                        totalExpense += item.Amount;
                    }

                    var expenseTotalRow = new TableRow();
                    expenseTotalRow.FontWeight = FontWeights.Bold;
                    expenseTotalRow.Cells.Add(CreateTableCell("إجمالي المصروفات", TextAlignment.Right));
                    expenseTotalRow.Cells.Add(CreateTableCell("", TextAlignment.Left));
                    expenseTotalRow.Cells.Add(CreateTableCell(totalExpense.ToString("N2"), TextAlignment.Left));
                    dataRowGroup.Rows.Add(expenseTotalRow);

                    // إضافة صف فارغ
                    dataRowGroup.Rows.Add(emptyRow.Clone());

                    // إضافة صف صافي الربح أو الخسارة
                    var netIncomeRow = new TableRow();
                    netIncomeRow.FontWeight = FontWeights.Bold;
                    netIncomeRow.Background = Brushes.LightGray;

                    decimal netIncome = totalRevenue - totalExpense;
                    string netIncomeText = netIncome >= 0 ? "صافي الربح" : "صافي الخسارة";

                    netIncomeRow.Cells.Add(CreateTableCell(netIncomeText, TextAlignment.Right));
                    netIncomeRow.Cells.Add(CreateTableCell("", TextAlignment.Left));
                    netIncomeRow.Cells.Add(CreateTableCell(Math.Abs(netIncome).ToString("N2"), TextAlignment.Left));
                    dataRowGroup.Rows.Add(netIncomeRow);

                    // إضافة الجدول إلى المستند
                    document.Blocks.Add(table);

                    // إضافة معلومات إضافية
                    var footerParagraph = new Paragraph()
                    {
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 20, 0, 0)
                    };
                    footerParagraph.Inlines.Add(new Run($"تم إنشاء التقرير في: {DateTime.Now:yyyy-MM-dd HH:mm:ss}"));
                    document.Blocks.Add(footerParagraph);

                    return document;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ReportGenerator.GenerateIncomeStatementReport");
                return CreateErrorDocument("حدث خطأ أثناء إنشاء تقرير قائمة الدخل.");
            }
        }

        /// <summary>
        /// إنشاء خلية جدول
        /// </summary>
        /// <param name="text">النص</param>
        /// <param name="alignment">المحاذاة</param>
        /// <param name="leftPadding">المسافة البادئة من اليسار</param>
        /// <returns>خلية الجدول</returns>
        private TableCell CreateTableCell(string text, TextAlignment alignment, double leftPadding = 0)
        {
            var cell = new TableCell();
            var paragraph = new Paragraph(new Run(text))
            {
                TextAlignment = alignment,
                Margin = new Thickness(leftPadding, 5, 5, 5)
            };
            cell.Blocks.Add(paragraph);
            return cell;
        }

        /// <summary>
        /// إنشاء مستند خطأ
        /// </summary>
        /// <param name="errorMessage">رسالة الخطأ</param>
        /// <returns>مستند الخطأ</returns>
        private FlowDocument CreateErrorDocument(string errorMessage)
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);

            var errorParagraph = new Paragraph(new Run(errorMessage))
            {
                Foreground = Brushes.Red,
                FontSize = 16,
                TextAlignment = TextAlignment.Center
            };
            document.Blocks.Add(errorParagraph);

            return document;
        }

        /// <summary>
        /// الحصول على بيانات ميزان المراجعة
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>بيانات ميزان المراجعة</returns>
        private async Task<List<TrialBalanceItem>> GetTrialBalanceDataAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ReportGenerator.GetTrialBalanceData", async () =>
                {
                    // الحصول على الحسابات
                    var accounts = await _context.Accounts
                        .Where(a => a.IsActive)
                        .OrderBy(a => a.Code)
                        .ToListAsync();

                    // الحصول على بنود القيود المحاسبية
                    var journalItems = await _context.JournalItems
                        .Include(i => i.JournalEntry)
                        .Where(i => i.JournalEntry.EntryDate >= fromDate && i.JournalEntry.EntryDate <= toDate)
                        .ToListAsync();

                    // إنشاء قائمة بيانات ميزان المراجعة
                    var trialBalanceData = new List<TrialBalanceItem>();

                    foreach (var account in accounts)
                    {
                        // حساب المدين والدائن
                        decimal debit = journalItems
                            .Where(i => i.AccountId == account.Id && i.DebitAmount > 0)
                            .Sum(i => i.DebitAmount);

                        decimal credit = journalItems
                            .Where(i => i.AccountId == account.Id && i.CreditAmount > 0)
                            .Sum(i => i.CreditAmount);

                        // حساب الرصيد
                        decimal balance = debit - credit;
                        decimal debitBalance = balance > 0 ? balance : 0;
                        decimal creditBalance = balance < 0 ? -balance : 0;

                        // إضافة البيانات إلى القائمة
                        trialBalanceData.Add(new TrialBalanceItem
                        {
                            AccountId = account.Id,
                            AccountCode = account.Code,
                            AccountName = account.Name,
                            Debit = debit,
                            Credit = credit,
                            DebitBalance = debitBalance,
                            CreditBalance = creditBalance
                        });
                    }

                    return trialBalanceData;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ReportGenerator.GetTrialBalanceData");
                return new List<TrialBalanceItem>();
            }
        }

        /// <summary>
        /// الحصول على بيانات قائمة الدخل
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>بيانات قائمة الدخل</returns>
        private async Task<IncomeStatementData> GetIncomeStatementDataAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("ReportGenerator.GetIncomeStatementData", async () =>
                {
                    // الحصول على الحسابات
                    var revenueAccounts = await _context.Accounts
                        .Where(a => a.IsActive && a.Type == AccountType.Revenue)
                        .OrderBy(a => a.Code)
                        .ToListAsync();

                    var expenseAccounts = await _context.Accounts
                        .Where(a => a.IsActive && a.Type == AccountType.Expense)
                        .OrderBy(a => a.Code)
                        .ToListAsync();

                    // الحصول على بنود القيود المحاسبية
                    var journalItems = await _context.JournalItems
                        .Include(i => i.JournalEntry)
                        .Include(i => i.Account)
                        .Where(i => i.JournalEntry.EntryDate >= fromDate && i.JournalEntry.EntryDate <= toDate)
                        .ToListAsync();

                    // إنشاء بيانات قائمة الدخل
                    var incomeStatementData = new IncomeStatementData();

                    // إضافة بيانات الإيرادات
                    foreach (var account in revenueAccounts)
                    {
                        // حساب المدين والدائن
                        decimal debit = journalItems
                            .Where(i => i.AccountId == account.Id && i.DebitAmount > 0)
                            .Sum(i => i.DebitAmount);

                        decimal credit = journalItems
                            .Where(i => i.AccountId == account.Id && i.CreditAmount > 0)
                            .Sum(i => i.CreditAmount);

                        // حساب الرصيد (الإيرادات تكون دائنة)
                        decimal amount = credit - debit;

                        // إضافة البيانات إلى القائمة
                        if (amount != 0)
                        {
                            incomeStatementData.RevenueItems.Add(new IncomeStatementItem
                            {
                                AccountId = account.Id,
                                AccountCode = account.Code,
                                AccountName = account.Name,
                                Amount = amount
                            });
                        }
                    }

                    // إضافة بيانات المصروفات
                    foreach (var account in expenseAccounts)
                    {
                        // حساب المدين والدائن
                        decimal debit = journalItems
                            .Where(i => i.AccountId == account.Id && i.DebitAmount > 0)
                            .Sum(i => i.DebitAmount);

                        decimal credit = journalItems
                            .Where(i => i.AccountId == account.Id && i.CreditAmount > 0)
                            .Sum(i => i.CreditAmount);

                        // حساب الرصيد (المصروفات تكون مدينة)
                        decimal amount = debit - credit;

                        // إضافة البيانات إلى القائمة
                        if (amount != 0)
                        {
                            incomeStatementData.ExpenseItems.Add(new IncomeStatementItem
                            {
                                AccountId = account.Id,
                                AccountCode = account.Code,
                                AccountName = account.Name,
                                Amount = amount
                            });
                        }
                    }

                    return incomeStatementData;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "ReportGenerator.GetIncomeStatementData");
                return new IncomeStatementData();
            }
        }
    }

    /// <summary>
    /// عنصر ميزان المراجعة
    /// </summary>
    public class TrialBalanceItem
    {
        /// <summary>
        /// معرف الحساب
        /// </summary>
        public int AccountId { get; set; }

        /// <summary>
        /// رمز الحساب
        /// </summary>
        public string AccountCode { get; set; } = string.Empty;

        /// <summary>
        /// اسم الحساب
        /// </summary>
        public string AccountName { get; set; } = string.Empty;

        /// <summary>
        /// المدين
        /// </summary>
        public decimal Debit { get; set; }

        /// <summary>
        /// الدائن
        /// </summary>
        public decimal Credit { get; set; }

        /// <summary>
        /// رصيد مدين
        /// </summary>
        public decimal DebitBalance { get; set; }

        /// <summary>
        /// رصيد دائن
        /// </summary>
        public decimal CreditBalance { get; set; }
    }

    /// <summary>
    /// بيانات قائمة الدخل
    /// </summary>
    public class IncomeStatementData
    {
        /// <summary>
        /// بنود الإيرادات
        /// </summary>
        public List<IncomeStatementItem> RevenueItems { get; set; } = new List<IncomeStatementItem>();

        /// <summary>
        /// بنود المصروفات
        /// </summary>
        public List<IncomeStatementItem> ExpenseItems { get; set; } = new List<IncomeStatementItem>();
    }

    /// <summary>
    /// عنصر قائمة الدخل
    /// </summary>
    public class IncomeStatementItem
    {
        /// <summary>
        /// معرف الحساب
        /// </summary>
        public int AccountId { get; set; }

        /// <summary>
        /// رمز الحساب
        /// </summary>
        public string AccountCode { get; set; } = string.Empty;

        /// <summary>
        /// اسم الحساب
        /// </summary>
        public string AccountName { get; set; } = string.Empty;

        /// <summary>
        /// المبلغ
        /// </summary>
        public decimal Amount { get; set; }
    }
}
