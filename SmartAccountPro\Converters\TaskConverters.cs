using SmartAccountPro.Core.Models;
using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace SmartAccountPro.Converters
{
    /// <summary>
    /// محول حالة المهمة إلى نص
    /// </summary>
    public class TaskStatusToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is TaskItemStatus status)
            {
                switch (status)
                {
                    case TaskItemStatus.New:
                        return "جديدة";
                    case TaskItemStatus.InProgress:
                        return "قيد التنفيذ";
                    case TaskItemStatus.OnHold:
                        return "معلقة";
                    case TaskItemStatus.Completed:
                        return "مكتملة";
                    case TaskItemStatus.Cancelled:
                        return "ملغاة";
                    default:
                        return "غير معروفة";
                }
            }
            return "غير معروفة";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول أولوية المهمة إلى نص
    /// </summary>
    public class TaskPriorityToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is TaskPriority priority)
            {
                switch (priority)
                {
                    case TaskPriority.Low:
                        return "منخفضة";
                    case TaskPriority.Medium:
                        return "متوسطة";
                    case TaskPriority.High:
                        return "عالية";
                    case TaskPriority.Critical:
                        return "حرجة";
                    default:
                        return "غير معروفة";
                }
            }
            return "غير معروفة";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول أولوية المهمة إلى لون
    /// </summary>
    public class TaskPriorityToBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is TaskPriority priority)
            {
                switch (priority)
                {
                    case TaskPriority.Low:
                        return new SolidColorBrush(Color.FromRgb(76, 175, 80)); // أخضر
                    case TaskPriority.Medium:
                        return new SolidColorBrush(Color.FromRgb(33, 150, 243)); // أزرق
                    case TaskPriority.High:
                        return new SolidColorBrush(Color.FromRgb(255, 152, 0)); // برتقالي
                    case TaskPriority.Critical:
                        return new SolidColorBrush(Color.FromRgb(244, 67, 54)); // أحمر
                    default:
                        return new SolidColorBrush(Colors.Gray);
                }
            }
            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول حالة المهمة إلى لون
    /// </summary>
    public class TaskStatusToBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is TaskItemStatus status)
            {
                switch (status)
                {
                    case TaskItemStatus.New:
                        return new SolidColorBrush(Color.FromRgb(33, 150, 243)); // أزرق
                    case TaskItemStatus.InProgress:
                        return new SolidColorBrush(Color.FromRgb(156, 39, 176)); // بنفسجي
                    case TaskItemStatus.OnHold:
                        return new SolidColorBrush(Color.FromRgb(255, 152, 0)); // برتقالي
                    case TaskItemStatus.Completed:
                        return new SolidColorBrush(Color.FromRgb(76, 175, 80)); // أخضر
                    case TaskItemStatus.Cancelled:
                        return new SolidColorBrush(Color.FromRgb(158, 158, 158)); // رمادي
                    default:
                        return new SolidColorBrush(Colors.Gray);
                }
            }
            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول اكتمال المهمة إلى تنسيق النص
    /// </summary>
    public class TaskCompletedToTextDecorationConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isCompleted && isCompleted)
            {
                return TextDecorations.Strikethrough;
            }
            return null;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول التاريخ إلى نص
    /// </summary>
    public class DateToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DateTime date)
            {
                // تنسيق التاريخ بالعربية
                return date.ToString("yyyy/MM/dd", new CultureInfo("ar-SA"));
            }
            return string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
