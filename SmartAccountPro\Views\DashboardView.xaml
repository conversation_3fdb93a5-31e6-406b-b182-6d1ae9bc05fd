<UserControl x:Class="SmartAccountPro.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SmartAccountPro.Views"
             xmlns:vm="clr-namespace:SmartAccountPro.ViewModels"
             xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <UserControl.DataContext>
        <vm:DashboardViewModel />
    </UserControl.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان اللوحة -->
        <Border Grid.Row="0"
                Background="{DynamicResource PrimaryColor}"
                CornerRadius="0,0,20,20"
                Margin="20,0,20,20">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="2" BlurRadius="10" Opacity="0.2" Color="#000000"/>
            </Border.Effect>
            <Grid Margin="20,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="لوحة التحكم"
                               FontSize="28"
                               FontWeight="Bold"
                               Foreground="White">
                        <TextBlock.Effect>
                            <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.3" Color="#000000"/>
                        </TextBlock.Effect>
                    </TextBlock>
                    <TextBlock Text="مرحباً بك في نظام المحاسبة الذكي"
                               FontSize="14"
                               Foreground="#E1F5FE"
                               Margin="0,5,0,0"/>
                </StackPanel>

                <Border Grid.Column="1"
                        Background="#FFFFFF20"
                        CornerRadius="10"
                        Padding="15,8">
                    <TextBlock Text="{Binding CurrentDate, StringFormat={}{0:yyyy/MM/dd}}"
                               FontSize="16"
                               Foreground="White"
                               VerticalAlignment="Center"/>
                </Border>
            </Grid>
        </Border>

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- بطاقات الإحصائيات -->
                <Grid Grid.Row="0" Margin="20,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- بطاقة الإيرادات -->
                    <Border Grid.Column="0"
                            Background="#4CAF50"
                            CornerRadius="15"
                            Margin="5"
                            Padding="15">
                        <Border.Effect>
                            <DropShadowEffect ShadowDepth="2" BlurRadius="8" Opacity="0.2" Color="#000000"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <TextBlock Grid.Row="0"
                                       Text="&#xE8EC;"
                                       FontFamily="Segoe MDL2 Assets"
                                       Foreground="White"
                                       FontSize="24"
                                       Margin="0,0,0,10"/>
                            <TextBlock Grid.Row="1"
                                       Text="الإيرادات"
                                       Foreground="White"
                                       FontSize="16"
                                       FontWeight="Bold"/>
                            <TextBlock Grid.Row="2"
                                       Text="{Binding TotalRevenue, StringFormat={}{0:N0} ر.س}"
                                       Foreground="White"
                                       FontSize="22"
                                       FontWeight="Bold"
                                       Margin="0,10,0,0"/>
                        </Grid>
                    </Border>

                    <!-- بطاقة المصروفات -->
                    <Border Grid.Column="1"
                            Background="#F44336"
                            CornerRadius="15"
                            Margin="5"
                            Padding="15">
                        <Border.Effect>
                            <DropShadowEffect ShadowDepth="2" BlurRadius="8" Opacity="0.2" Color="#000000"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <TextBlock Grid.Row="0"
                                       Text="&#xE8CE;"
                                       FontFamily="Segoe MDL2 Assets"
                                       Foreground="White"
                                       FontSize="24"
                                       Margin="0,0,0,10"/>
                            <TextBlock Grid.Row="1"
                                       Text="المصروفات"
                                       Foreground="White"
                                       FontSize="16"
                                       FontWeight="Bold"/>
                            <TextBlock Grid.Row="2"
                                       Text="{Binding TotalExpenses, StringFormat={}{0:N0} ر.س}"
                                       Foreground="White"
                                       FontSize="22"
                                       FontWeight="Bold"
                                       Margin="0,10,0,0"/>
                        </Grid>
                    </Border>

                    <!-- بطاقة الأرباح -->
                    <Border Grid.Column="2"
                            Background="#1A73E8"
                            CornerRadius="15"
                            Margin="5"
                            Padding="15">
                        <Border.Effect>
                            <DropShadowEffect ShadowDepth="2" BlurRadius="8" Opacity="0.2" Color="#000000"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <TextBlock Grid.Row="0"
                                       Text="&#xE8FB;"
                                       FontFamily="Segoe MDL2 Assets"
                                       Foreground="White"
                                       FontSize="24"
                                       Margin="0,0,0,10"/>
                            <TextBlock Grid.Row="1"
                                       Text="صافي الربح"
                                       Foreground="White"
                                       FontSize="16"
                                       FontWeight="Bold"/>
                            <TextBlock Grid.Row="2"
                                       Text="{Binding NetProfit, StringFormat={}{0:N0} ر.س}"
                                       Foreground="White"
                                       FontSize="22"
                                       FontWeight="Bold"
                                       Margin="0,10,0,0"/>
                        </Grid>
                    </Border>

                    <!-- بطاقة الفواتير -->
                    <Border Grid.Column="3"
                            Background="#FF9800"
                            CornerRadius="15"
                            Margin="5"
                            Padding="15">
                        <Border.Effect>
                            <DropShadowEffect ShadowDepth="2" BlurRadius="8" Opacity="0.2" Color="#000000"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <TextBlock Grid.Row="0"
                                       Text="&#xE71D;"
                                       FontFamily="Segoe MDL2 Assets"
                                       Foreground="White"
                                       FontSize="24"
                                       Margin="0,0,0,10"/>
                            <TextBlock Grid.Row="1"
                                       Text="عدد الفواتير"
                                       Foreground="White"
                                       FontSize="16"
                                       FontWeight="Bold"/>
                            <TextBlock Grid.Row="2"
                                       Text="{Binding InvoiceCount}"
                                       Foreground="White"
                                       FontSize="22"
                                       FontWeight="Bold"
                                       Margin="0,10,0,0"/>
                        </Grid>
                    </Border>
                </Grid>

                <!-- الرسومات البيانية -->
                <Grid Grid.Row="1" Margin="20,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- رسم بياني للإيرادات والمصروفات -->
                    <Border Grid.Column="0"
                            Background="White"
                            BorderBrush="#E0E0E0"
                            BorderThickness="1"
                            CornerRadius="15"
                            Margin="5"
                            Padding="20">
                        <Border.Effect>
                            <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <StackPanel Grid.Row="0" Orientation="Horizontal">
                                <TextBlock Text="&#xE9D9;"
                                           FontFamily="Segoe MDL2 Assets"
                                           Foreground="{DynamicResource PrimaryColor}"
                                           FontSize="20"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                                <TextBlock Text="الإيرادات والمصروفات الشهرية"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Margin="0,0,0,10"/>
                            </StackPanel>
                            <!-- الرسم البياني للإيرادات والمصروفات -->
                            <lvc:CartesianChart Grid.Row="1"
                                                Series="{Binding RevenueExpenseSeries}"
                                                XAxes="{Binding XAxes}"
                                                YAxes="{Binding YAxes}"
                                                Margin="0,10,0,0"
                                                Height="250"/>
                        </Grid>
                    </Border>

                    <!-- رسم بياني دائري للمصروفات -->
                    <Border Grid.Column="1"
                            Background="White"
                            BorderBrush="#E0E0E0"
                            BorderThickness="1"
                            CornerRadius="15"
                            Margin="5"
                            Padding="20">
                        <Border.Effect>
                            <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <StackPanel Grid.Row="0" Orientation="Horizontal">
                                <TextBlock Text="&#xE9D2;"
                                           FontFamily="Segoe MDL2 Assets"
                                           Foreground="{DynamicResource PrimaryColor}"
                                           FontSize="20"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                                <TextBlock Text="توزيع المصروفات"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Margin="0,0,0,10"/>
                            </StackPanel>
                            <!-- الرسم البياني الدائري لتوزيع المصروفات -->
                            <lvc:PieChart Grid.Row="1"
                                          Series="{Binding ExpenseDistributionSeries}"
                                          Margin="0,10,0,0"
                                          Height="250"
                                          InitialRotation="-90"/>
                        </Grid>
                    </Border>
                </Grid>

                <!-- أزرار الوصول السريع -->
                <Border Grid.Row="2"
                        Margin="20,20,20,10"
                        Background="White"
                        BorderBrush="#E0E0E0"
                        BorderThickness="1"
                        CornerRadius="15"
                        Padding="20">
                    <Border.Effect>
                        <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
                    </Border.Effect>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0"
                                   Text="الوصول السريع"
                                   FontSize="18"
                                   FontWeight="Bold"
                                   Margin="0,0,0,15"/>

                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0"
                                    Margin="5"
                                    Padding="15"
                                    Style="{StaticResource ModernButton}"
                                    Command="{Binding CreateJournalEntryCommand}">
                                <StackPanel>
                                    <TextBlock Text="&#xE8A5;"
                                               FontFamily="Segoe MDL2 Assets"
                                               FontSize="24"
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,8"/>
                                    <TextBlock Text="إنشاء قيد محاسبي"
                                               HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="1"
                                    Margin="5"
                                    Padding="15"
                                    Style="{StaticResource GreenButton}"
                                    Command="{Binding CreateInvoiceCommand}">
                                <StackPanel>
                                    <TextBlock Text="&#xE71D;"
                                               FontFamily="Segoe MDL2 Assets"
                                               FontSize="24"
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,8"/>
                                    <TextBlock Text="إنشاء فاتورة جديدة"
                                               HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="2"
                                    Margin="5"
                                    Padding="15"
                                    Style="{StaticResource OrangeButton}"
                                    Command="{Binding ViewReportsCommand}">
                                <StackPanel>
                                    <TextBlock Text="&#xE9D9;"
                                               FontFamily="Segoe MDL2 Assets"
                                               FontSize="24"
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,8"/>
                                    <TextBlock Text="عرض التقارير"
                                               HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="3"
                                    Margin="5"
                                    Padding="15"
                                    Style="{StaticResource PurpleButton}"
                                    Command="{Binding ManageAccountsCommand}">
                                <StackPanel>
                                    <TextBlock Text="&#xE825;"
                                               FontFamily="Segoe MDL2 Assets"
                                               FontSize="24"
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,8"/>
                                    <TextBlock Text="إدارة الحسابات"
                                               HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                    </Grid>
                </Border>
            </Grid>
        </ScrollViewer>
    </Grid>
</UserControl>
