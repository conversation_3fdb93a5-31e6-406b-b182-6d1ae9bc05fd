using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using SmartAccountPro.ViewModels;

namespace SmartAccountPro.Views
{
    /// <summary>
    /// شاشة تسجيل الدخول
    /// </summary>
    public partial class LoginView : UserControl
    {
        private LoginViewModel _viewModel;

        public LoginView()
        {
            InitializeComponent();

            // الحصول على نموذج العرض
            _viewModel = DataContext as LoginViewModel;

            if (_viewModel != null)
            {
                // تهيئة نموذج العرض
                _viewModel.Initialize();

                // ربط حدث تغيير النص في حقل اسم المستخدم
                UsernameTextBox.TextChanged += UsernameTextBox_TextChanged;

                // ربط حدث تغيير النص في حقل كلمة المرور
                PasswordBox.PasswordChanged += PasswordBox_PasswordChanged;

                // ربط حدث النقر على زر تسجيل الدخول
                LoginButton.Click += LoginButton_Click;
            }
        }

        /// <summary>
        /// معالج حدث تغيير النص في حقل اسم المستخدم
        /// </summary>
        private void UsernameTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateUsername();
        }

        /// <summary>
        /// معالج حدث تغيير النص في حقل كلمة المرور
        /// </summary>
        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            ValidatePassword();
        }

        /// <summary>
        /// معالج حدث النقر على زر تسجيل الدخول
        /// </summary>
        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صحة البيانات
            ValidateUsername();
            ValidatePassword();
        }

        /// <summary>
        /// التحقق من صحة اسم المستخدم
        /// </summary>
        private void ValidateUsername()
        {
            if (_viewModel == null)
                return;

            // الحصول على أخطاء التحقق
            var errors = _viewModel.GetValidationErrors("Username");

            if (errors.Count > 0)
            {
                // عرض رسالة الخطأ
                UsernameErrorText.Text = errors[0];
                UsernameErrorText.Visibility = Visibility.Visible;
                UsernameBorder.BorderBrush = new SolidColorBrush(Colors.Red);
            }
            else
            {
                // إخفاء رسالة الخطأ
                UsernameErrorText.Visibility = Visibility.Collapsed;
                UsernameBorder.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#CCCCCC"));
            }
        }

        /// <summary>
        /// التحقق من صحة كلمة المرور
        /// </summary>
        private void ValidatePassword()
        {
            if (_viewModel == null)
                return;

            // الحصول على أخطاء التحقق
            var errors = _viewModel.GetValidationErrors("Password");

            if (errors.Count > 0)
            {
                // عرض رسالة الخطأ
                PasswordErrorText.Text = errors[0];
                PasswordErrorText.Visibility = Visibility.Visible;
                PasswordBorder.BorderBrush = new SolidColorBrush(Colors.Red);
            }
            else
            {
                // إخفاء رسالة الخطأ
                PasswordErrorText.Visibility = Visibility.Collapsed;
                PasswordBorder.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#CCCCCC"));
            }
        }
    }
}
