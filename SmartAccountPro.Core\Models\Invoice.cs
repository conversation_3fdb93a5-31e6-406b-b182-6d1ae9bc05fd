using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// يمثل فاتورة في النظام
    /// </summary>
    public class Invoice
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(20)]
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// نوع الفاتورة: مبيعات أو مشتريات
        /// </summary>
        public InvoiceType Type { get; set; }

        /// <summary>
        /// تاريخ الفاتورة
        /// </summary>
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ الاستحقاق
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// العميل (في حالة فاتورة المبيعات)
        /// </summary>
        public int? CustomerId { get; set; }
        public virtual Customer Customer { get; set; }

        /// <summary>
        /// المورد (في حالة فاتورة المشتريات)
        /// </summary>
        public int? SupplierId { get; set; }
        public virtual Supplier Supplier { get; set; }

        /// <summary>
        /// ملاحظات الفاتورة
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// إجمالي الفاتورة قبل الضريبة والخصم
        /// </summary>
        public decimal Subtotal { get; set; }

        /// <summary>
        /// نسبة الخصم
        /// </summary>
        public decimal DiscountPercentage { get; set; }

        /// <summary>
        /// قيمة الخصم
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// نسبة الضريبة
        /// </summary>
        public decimal TaxPercentage { get; set; }

        /// <summary>
        /// قيمة الضريبة
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// إجمالي الفاتورة بعد الضريبة والخصم
        /// </summary>
        public decimal Total { get; set; }

        /// <summary>
        /// المبلغ المدفوع
        /// </summary>
        public decimal PaidAmount { get; set; }

        /// <summary>
        /// المبلغ المتبقي
        /// </summary>
        public decimal RemainingAmount { get; set; }

        /// <summary>
        /// حالة الفاتورة
        /// </summary>
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;

        /// <summary>
        /// هل تم ترحيل الفاتورة محاسبياً
        /// </summary>
        public bool IsPosted { get; set; }

        /// <summary>
        /// رقم القيد المحاسبي المرتبط بالفاتورة
        /// </summary>
        public int? JournalEntryId { get; set; }
        public virtual JournalEntry JournalEntry { get; set; }

        /// <summary>
        /// المستخدم الذي أنشأ الفاتورة
        /// </summary>
        public int CreatedByUserId { get; set; }
        public virtual User CreatedByUser { get; set; }

        /// <summary>
        /// تاريخ إنشاء الفاتورة
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث للفاتورة
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// بنود الفاتورة
        /// </summary>
        public virtual ICollection<InvoiceItem> Items { get; set; }

        public Invoice()
        {
            Items = new HashSet<InvoiceItem>();
        }
    }

    /// <summary>
    /// يمثل بند في فاتورة
    /// </summary>
    public class InvoiceItem
    {
        public int Id { get; set; }

        public int InvoiceId { get; set; }
        public virtual Invoice Invoice { get; set; }

        /// <summary>
        /// المنتج
        /// </summary>
        public int ProductId { get; set; }
        public virtual Product Product { get; set; }

        /// <summary>
        /// وصف البند
        /// </summary>
        [MaxLength(255)]
        public string Description { get; set; }

        /// <summary>
        /// الكمية
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// سعر الوحدة
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// نسبة الخصم
        /// </summary>
        public decimal DiscountPercentage { get; set; }

        /// <summary>
        /// قيمة الخصم
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// نسبة الضريبة
        /// </summary>
        public decimal TaxPercentage { get; set; }

        /// <summary>
        /// قيمة الضريبة
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// إجمالي البند
        /// </summary>
        public decimal Total { get; set; }
    }

    /// <summary>
    /// نوع الفاتورة
    /// </summary>
    public enum InvoiceType
    {
        /// <summary>
        /// فاتورة مبيعات
        /// </summary>
        Sales = 1,
        
        /// <summary>
        /// فاتورة مشتريات
        /// </summary>
        Purchase = 2
    }

    /// <summary>
    /// حالة الفاتورة
    /// </summary>
    public enum InvoiceStatus
    {
        /// <summary>
        /// مسودة
        /// </summary>
        Draft = 1,
        
        /// <summary>
        /// معتمدة
        /// </summary>
        Approved = 2,
        
        /// <summary>
        /// مدفوعة جزئياً
        /// </summary>
        PartiallyPaid = 3,
        
        /// <summary>
        /// مدفوعة بالكامل
        /// </summary>
        Paid = 4,
        
        /// <summary>
        /// ملغاة
        /// </summary>
        Cancelled = 5
    }
}
