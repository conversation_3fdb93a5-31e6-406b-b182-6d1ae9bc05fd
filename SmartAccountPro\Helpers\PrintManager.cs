using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Xps;
using System.Windows.Xps.Packaging;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة الطباعة
    /// </summary>
    public static class PrintManager
    {
        /// <summary>
        /// طباعة مستند
        /// </summary>
        /// <param name="document">المستند</param>
        /// <returns>نجاح العملية</returns>
        public static bool PrintDocument(FlowDocument document)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("PrintManager.PrintDocument", () =>
                {
                    // إنشاء حوار الطباعة
                    var printDialog = new System.Windows.Controls.PrintDialog();

                    // عرض حوار الطباعة
                    if (printDialog.ShowDialog() == true)
                    {
                        // تعيين إعدادات الطباعة
                        document.PageHeight = printDialog.PrintableAreaHeight;
                        document.PageWidth = printDialog.PrintableAreaWidth;
                        document.PagePadding = new Thickness(50);
                        document.ColumnGap = 0;
                        document.ColumnWidth = printDialog.PrintableAreaWidth;

                        // إنشاء مستند XPS
                        var paginator = ((IDocumentPaginatorSource)document).DocumentPaginator;
                        paginator.PageSize = new Size(printDialog.PrintableAreaWidth, printDialog.PrintableAreaHeight);

                        // طباعة المستند
                        printDialog.PrintDocument(paginator, "طباعة مستند");

                        // إضافة إشعار
                        NotificationManager.Instance.AddNotification(
                            "تمت الطباعة",
                            "تمت طباعة المستند بنجاح.",
                            NotificationType.Success);

                        return true;
                    }

                    return false;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "PrintManager.PrintDocument");
                return false;
            }
        }

        /// <summary>
        /// طباعة عنصر واجهة المستخدم
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="description">وصف المستند</param>
        /// <returns>نجاح العملية</returns>
        public static bool PrintElement(FrameworkElement element, string description)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("PrintManager.PrintElement", () =>
                {
                    // إنشاء حوار الطباعة
                    var printDialog = new System.Windows.Controls.PrintDialog();

                    // عرض حوار الطباعة
                    if (printDialog.ShowDialog() == true)
                    {
                        // حفظ حالة العنصر
                        var originalWidth = element.Width;
                        var originalHeight = element.Height;

                        // تعيين حجم العنصر
                        element.Width = printDialog.PrintableAreaWidth;
                        element.Height = printDialog.PrintableAreaHeight;
                        element.Measure(new Size(printDialog.PrintableAreaWidth, printDialog.PrintableAreaHeight));
                        element.Arrange(new Rect(0, 0, printDialog.PrintableAreaWidth, printDialog.PrintableAreaHeight));

                        // طباعة العنصر
                        printDialog.PrintVisual(element, description);

                        // استعادة حالة العنصر
                        element.Width = originalWidth;
                        element.Height = originalHeight;
                        element.Measure(new Size(originalWidth, originalHeight));
                        element.Arrange(new Rect(0, 0, originalWidth, originalHeight));

                        // إضافة إشعار
                        NotificationManager.Instance.AddNotification(
                            "تمت الطباعة",
                            "تمت طباعة العنصر بنجاح.",
                            NotificationType.Success);

                        return true;
                    }

                    return false;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "PrintManager.PrintElement");
                return false;
            }
        }

        /// <summary>
        /// تصدير مستند إلى ملف PDF
        /// </summary>
        /// <param name="document">المستند</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>نجاح العملية</returns>
        public static bool ExportToPdf(FlowDocument document, string filePath)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("PrintManager.ExportToPdf", () =>
                {
                    // إنشاء مسار الملف إذا لم يكن موجوداً
                    var directory = Path.GetDirectoryName(filePath);
                    if (!Directory.Exists(directory) && !string.IsNullOrEmpty(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // تعيين إعدادات المستند
                    document.PageHeight = 1056; // A4 height in pixels at 96 DPI
                    document.PageWidth = 816; // A4 width in pixels at 96 DPI
                    document.PagePadding = new Thickness(50);
                    document.ColumnGap = 0;
                    document.ColumnWidth = 716; // Page width - padding

                    // إنشاء مستند XPS
                    var xpsDocument = new XpsDocument(filePath, FileAccess.ReadWrite);
                    var xpsWriter = XpsDocument.CreateXpsDocumentWriter(xpsDocument);
                    var paginator = ((IDocumentPaginatorSource)document).DocumentPaginator;
                    xpsWriter.Write(paginator);
                    xpsDocument.Close();

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم التصدير",
                        $"تم تصدير المستند إلى ملف PDF بنجاح: {Path.GetFileName(filePath)}",
                        NotificationType.Success);

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "PrintManager.ExportToPdf");
                return false;
            }
        }

        /// <summary>
        /// تصدير عنصر واجهة المستخدم إلى ملف صورة
        /// </summary>
        /// <param name="element">العنصر</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>نجاح العملية</returns>
        public static bool ExportToImage(FrameworkElement element, string filePath)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("PrintManager.ExportToImage", () =>
                {
                    // إنشاء مسار الملف إذا لم يكن موجوداً
                    var directory = Path.GetDirectoryName(filePath);
                    if (!Directory.Exists(directory) && !string.IsNullOrEmpty(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // حفظ حالة العنصر
                    var originalWidth = element.Width;
                    var originalHeight = element.Height;

                    // تعيين حجم العنصر
                    element.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                    var size = element.DesiredSize;
                    element.Arrange(new Rect(0, 0, size.Width, size.Height));

                    // إنشاء صورة
                    var renderTargetBitmap = new RenderTargetBitmap(
                        (int)size.Width, (int)size.Height, 96, 96, PixelFormats.Pbgra32);
                    renderTargetBitmap.Render(element);

                    // حفظ الصورة
                    var extension = Path.GetExtension(filePath).ToLower();
                    BitmapEncoder encoder;

                    switch (extension)
                    {
                        case ".png":
                            encoder = new PngBitmapEncoder();
                            break;
                        case ".jpg":
                        case ".jpeg":
                            encoder = new JpegBitmapEncoder();
                            break;
                        case ".bmp":
                            encoder = new BmpBitmapEncoder();
                            break;
                        case ".gif":
                            encoder = new GifBitmapEncoder();
                            break;
                        case ".tiff":
                            encoder = new TiffBitmapEncoder();
                            break;
                        default:
                            encoder = new PngBitmapEncoder();
                            break;
                    }

                    encoder.Frames.Add(BitmapFrame.Create(renderTargetBitmap));

                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        encoder.Save(fileStream);
                    }

                    // استعادة حالة العنصر
                    element.Width = originalWidth;
                    element.Height = originalHeight;
                    element.Measure(new Size(originalWidth, originalHeight));
                    element.Arrange(new Rect(0, 0, originalWidth, originalHeight));

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم التصدير",
                        $"تم تصدير العنصر إلى ملف صورة بنجاح: {Path.GetFileName(filePath)}",
                        NotificationType.Success);

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "PrintManager.ExportToImage");
                return false;
            }
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        /// <param name="document">المستند</param>
        /// <returns>نجاح العملية</returns>
        public static bool PrintPreview(FlowDocument document)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("PrintManager.PrintPreview", () =>
                {
                    // إنشاء نافذة معاينة الطباعة
                    var printPreviewWindow = new Window
                    {
                        Title = "معاينة الطباعة",
                        Width = 800,
                        Height = 600,
                        WindowStartupLocation = WindowStartupLocation.CenterScreen
                    };

                    // إنشاء عارض المستند
                    var documentViewer = new DocumentViewer();
                    printPreviewWindow.Content = documentViewer;

                    // تعيين إعدادات المستند
                    document.PageHeight = 1056; // A4 height in pixels at 96 DPI
                    document.PageWidth = 816; // A4 width in pixels at 96 DPI
                    document.PagePadding = new Thickness(50);
                    document.ColumnGap = 0;
                    document.ColumnWidth = 716; // Page width - padding

                    // إنشاء مستند XPS
                    var paginator = ((IDocumentPaginatorSource)document).DocumentPaginator;
                    documentViewer.Document = paginator;

                    // عرض نافذة معاينة الطباعة
                    printPreviewWindow.ShowDialog();

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "PrintManager.PrintPreview");
                return false;
            }
        }
    }
}
