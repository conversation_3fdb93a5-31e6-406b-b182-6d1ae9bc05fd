using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة للعمليات المتعلقة بالشبكة
    /// </summary>
    public static class NetworkHelper
    {
        /// <summary>
        /// الحصول على عنوان IP المحلي
        /// </summary>
        /// <returns>عنوان IP المحلي</returns>
        public static string GetLocalIPAddress()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("NetworkHelper.GetLocalIPAddress", () =>
                {
                    // الحصول على اسم المضيف
                    string hostName = Dns.GetHostName();

                    // الحصول على معلومات المضيف
                    var hostEntry = Dns.GetHostEntry(hostName);

                    // البحث عن عنوان IPv4
                    var ipAddress = hostEntry.AddressList
                        .FirstOrDefault(ip => ip.AddressFamily == AddressFamily.InterNetwork);

                    // إرجاع عنوان IP
                    return ipAddress?.ToString() ?? "127.0.0.1";
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NetworkHelper.GetLocalIPAddress");
                return "127.0.0.1";
            }
        }

        /// <summary>
        /// التحقق من اتصال الإنترنت
        /// </summary>
        /// <returns>حالة الاتصال</returns>
        public static bool IsInternetConnected()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("NetworkHelper.IsInternetConnected", () =>
                {
                    // التحقق من اتصال الشبكة
                    if (!NetworkInterface.GetIsNetworkAvailable())
                    {
                        return false;
                    }

                    // محاولة الاتصال بخادم Google
                    using (var ping = new Ping())
                    {
                        var reply = ping.Send("*******", 2000);
                        return reply?.Status == IPStatus.Success;
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NetworkHelper.IsInternetConnected");
                return false;
            }
        }

        /// <summary>
        /// الحصول على عنوان MAC للجهاز
        /// </summary>
        /// <returns>عنوان MAC</returns>
        public static string GetMacAddress()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("NetworkHelper.GetMacAddress", () =>
                {
                    // الحصول على واجهات الشبكة
                    var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();

                    // البحث عن واجهة الشبكة النشطة
                    var activeInterface = networkInterfaces
                        .FirstOrDefault(ni => ni.OperationalStatus == OperationalStatus.Up &&
                                             ni.NetworkInterfaceType != NetworkInterfaceType.Loopback);

                    // إرجاع عنوان MAC
                    if (activeInterface != null)
                    {
                        var macBytes = activeInterface.GetPhysicalAddress().GetAddressBytes();
                        return string.Join("-", macBytes.Select(b => b.ToString("X2")));
                    }

                    return "00-00-00-00-00-00";
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NetworkHelper.GetMacAddress");
                return "00-00-00-00-00-00";
            }
        }

        /// <summary>
        /// الحصول على معلومات الشبكة
        /// </summary>
        /// <returns>معلومات الشبكة</returns>
        public static NetworkInfo GetNetworkInfo()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("NetworkHelper.GetNetworkInfo", () =>
                {
                    // إنشاء معلومات الشبكة
                    var networkInfo = new NetworkInfo
                    {
                        IsNetworkAvailable = NetworkInterface.GetIsNetworkAvailable(),
                        IsInternetConnected = IsInternetConnected(),
                        LocalIPAddress = GetLocalIPAddress(),
                        MacAddress = GetMacAddress(),
                        HostName = Dns.GetHostName()
                    };

                    // الحصول على واجهات الشبكة
                    var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();

                    // إضافة معلومات واجهات الشبكة
                    foreach (var networkInterface in networkInterfaces)
                    {
                        // تجاهل واجهات الشبكة غير النشطة
                        if (networkInterface.OperationalStatus != OperationalStatus.Up)
                        {
                            continue;
                        }

                        // الحصول على إحصائيات واجهة الشبكة
                        var statistics = networkInterface.GetIPStatistics();

                        // إنشاء معلومات واجهة الشبكة
                        var interfaceInfo = new NetworkInterfaceInfo
                        {
                            Name = networkInterface.Name,
                            Description = networkInterface.Description,
                            Type = networkInterface.NetworkInterfaceType.ToString(),
                            Status = networkInterface.OperationalStatus.ToString(),
                            Speed = networkInterface.Speed,
                            BytesSent = statistics.BytesSent,
                            BytesReceived = statistics.BytesReceived
                        };

                        // الحصول على عناوين IP
                        var properties = networkInterface.GetIPProperties();
                        foreach (var address in properties.UnicastAddresses)
                        {
                            if (address.Address.AddressFamily == AddressFamily.InterNetwork)
                            {
                                interfaceInfo.IPAddresses.Add(address.Address.ToString());
                            }
                        }

                        // إضافة معلومات واجهة الشبكة
                        networkInfo.Interfaces.Add(interfaceInfo);
                    }

                    return networkInfo;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NetworkHelper.GetNetworkInfo");
                return new NetworkInfo
                {
                    IsNetworkAvailable = false,
                    IsInternetConnected = false,
                    LocalIPAddress = "127.0.0.1",
                    MacAddress = "00-00-00-00-00-00",
                    HostName = "localhost"
                };
            }
        }

        /// <summary>
        /// التحقق من توفر منفذ
        /// </summary>
        /// <param name="port">رقم المنفذ</param>
        /// <returns>توفر المنفذ</returns>
        public static bool IsPortAvailable(int port)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("NetworkHelper.IsPortAvailable", () =>
                {
                    // التحقق من صحة رقم المنفذ
                    if (port < 1 || port > 65535)
                    {
                        return false;
                    }

                    // محاولة الاستماع على المنفذ
                    using (var socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp))
                    {
                        try
                        {
                            socket.Bind(new IPEndPoint(IPAddress.Loopback, port));
                            socket.Listen(1);
                            return true;
                        }
                        catch (SocketException)
                        {
                            return false;
                        }
                        finally
                        {
                            socket.Close();
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NetworkHelper.IsPortAvailable");
                return false;
            }
        }

        /// <summary>
        /// الحصول على منفذ متاح
        /// </summary>
        /// <param name="startPort">منفذ البداية</param>
        /// <param name="endPort">منفذ النهاية</param>
        /// <returns>رقم المنفذ المتاح</returns>
        public static int GetAvailablePort(int startPort = 8000, int endPort = 9000)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("NetworkHelper.GetAvailablePort", () =>
                {
                    // التحقق من صحة نطاق المنافذ
                    if (startPort < 1 || startPort > 65535 || endPort < 1 || endPort > 65535 || startPort > endPort)
                    {
                        startPort = 8000;
                        endPort = 9000;
                    }

                    // البحث عن منفذ متاح
                    for (int port = startPort; port <= endPort; port++)
                    {
                        if (IsPortAvailable(port))
                        {
                            return port;
                        }
                    }

                    // لم يتم العثور على منفذ متاح
                    throw new InvalidOperationException($"لم يتم العثور على منفذ متاح في النطاق {startPort}-{endPort}.");
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NetworkHelper.GetAvailablePort");
                return -1;
            }
        }

        /// <summary>
        /// اختبار اتصال بعنوان
        /// </summary>
        /// <param name="host">المضيف</param>
        /// <param name="port">المنفذ</param>
        /// <param name="timeout">مهلة الانتظار (بالمللي ثانية)</param>
        /// <returns>نتيجة الاختبار</returns>
        public static async Task<PingResult> PingHostAsync(string host, int port = 80, int timeout = 5000)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("NetworkHelper.PingHostAsync", async () =>
                {
                    // إنشاء نتيجة الاختبار
                    var result = new PingResult
                    {
                        Host = host,
                        Port = port,
                        IsSuccess = false,
                        ResponseTime = 0
                    };

                    // قياس وقت الاستجابة
                    var stopwatch = new Stopwatch();
                    stopwatch.Start();

                    try
                    {
                        // محاولة الاتصال بالمضيف
                        using (var client = new TcpClient())
                        {
                            // بدء الاتصال
                            var connectTask = client.ConnectAsync(host, port);

                            // انتظار الاتصال أو انتهاء المهلة
                            if (await Task.WhenAny(connectTask, Task.Delay(timeout)) == connectTask)
                            {
                                // تم الاتصال بنجاح
                                result.IsSuccess = true;
                            }
                            else
                            {
                                // انتهت المهلة
                                result.ErrorMessage = "انتهت مهلة الاتصال.";
                            }
                        }
                    }
                    catch (SocketException ex)
                    {
                        // خطأ في الاتصال
                        result.ErrorMessage = $"خطأ في الاتصال: {ex.Message}";
                    }
                    catch (Exception ex)
                    {
                        // خطأ غير متوقع
                        result.ErrorMessage = $"خطأ غير متوقع: {ex.Message}";
                    }
                    finally
                    {
                        // إيقاف قياس الوقت
                        stopwatch.Stop();
                        result.ResponseTime = stopwatch.ElapsedMilliseconds;
                    }

                    return result;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "NetworkHelper.PingHostAsync");
                return new PingResult
                {
                    Host = host,
                    Port = port,
                    IsSuccess = false,
                    ResponseTime = 0,
                    ErrorMessage = $"خطأ غير متوقع: {ex.Message}"
                };
            }
        }
    }

    /// <summary>
    /// معلومات الشبكة
    /// </summary>
    public class NetworkInfo
    {
        /// <summary>
        /// توفر الشبكة
        /// </summary>
        public bool IsNetworkAvailable { get; set; }

        /// <summary>
        /// اتصال الإنترنت
        /// </summary>
        public bool IsInternetConnected { get; set; }

        /// <summary>
        /// عنوان IP المحلي
        /// </summary>
        public string LocalIPAddress { get; set; } = string.Empty;

        /// <summary>
        /// عنوان MAC
        /// </summary>
        public string MacAddress { get; set; } = string.Empty;

        /// <summary>
        /// اسم المضيف
        /// </summary>
        public string HostName { get; set; } = string.Empty;

        /// <summary>
        /// واجهات الشبكة
        /// </summary>
        public List<NetworkInterfaceInfo> Interfaces { get; set; } = new List<NetworkInterfaceInfo>();
    }

    /// <summary>
    /// معلومات واجهة الشبكة
    /// </summary>
    public class NetworkInterfaceInfo
    {
        /// <summary>
        /// اسم واجهة الشبكة
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// وصف واجهة الشبكة
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// نوع واجهة الشبكة
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// حالة واجهة الشبكة
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// سرعة واجهة الشبكة
        /// </summary>
        public long Speed { get; set; }

        /// <summary>
        /// عدد البايتات المرسلة
        /// </summary>
        public long BytesSent { get; set; }

        /// <summary>
        /// عدد البايتات المستلمة
        /// </summary>
        public long BytesReceived { get; set; }

        /// <summary>
        /// عناوين IP
        /// </summary>
        public List<string> IPAddresses { get; set; } = new List<string>();
    }

    /// <summary>
    /// نتيجة اختبار الاتصال
    /// </summary>
    public class PingResult
    {
        /// <summary>
        /// المضيف
        /// </summary>
        public string Host { get; set; } = string.Empty;

        /// <summary>
        /// المنفذ
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// نجاح الاختبار
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// وقت الاستجابة (بالمللي ثانية)
        /// </summary>
        public long ResponseTime { get; set; }

        /// <summary>
        /// رسالة الخطأ
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
}
