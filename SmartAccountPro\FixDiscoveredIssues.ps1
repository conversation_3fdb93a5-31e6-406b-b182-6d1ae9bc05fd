# SmartAccount Pro - سكريبت إصلاح الأخطاء المكتشفة
# Script to Fix Discovered Issues in SmartAccount Pro Project

param(
    [string]$ProjectPath = ".",
    [switch]$FixHighPriority,
    [switch]$FixMediumPriority,
    [switch]$FixLowPriority,
    [switch]$CreateBackup,
    [switch]$DryRun
)

# تعيين القيم الافتراضية
if (-not $PSBoundParameters.ContainsKey('FixHighPriority')) { $FixHighPriority = $true }
if (-not $PSBoundParameters.ContainsKey('CreateBackup')) { $CreateBackup = $true }

# إعداد الألوان
$Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

Write-Host "🔧 SmartAccount Pro - إصلاح الأخطاء المكتشفة" -ForegroundColor $Colors.Header
Write-Host "═══════════════════════════════════════════════════════════" -ForegroundColor $Colors.Header

# دالة إنشاء نسخة احتياطية
function Create-Backup {
    param([string]$SourcePath)
    
    if ($CreateBackup -and -not $DryRun) {
        $backupPath = "$SourcePath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        try {
            Copy-Item $SourcePath $backupPath -Force
            Write-Host "   ✅ تم إنشاء نسخة احتياطية: $backupPath" -ForegroundColor $Colors.Success
            return $true
        }
        catch {
            Write-Host "   ❌ فشل في إنشاء نسخة احتياطية: $($_.Exception.Message)" -ForegroundColor $Colors.Error
            return $false
        }
    }
    return $true
}

# دالة تسجيل العمليات
function Write-FixLog {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { Write-Host "   ❌ $Message" -ForegroundColor $Colors.Error }
        "WARNING" { Write-Host "   ⚠️ $Message" -ForegroundColor $Colors.Warning }
        "SUCCESS" { Write-Host "   ✅ $Message" -ForegroundColor $Colors.Success }
        default { Write-Host "   ℹ️ $Message" -ForegroundColor $Colors.Info }
    }
}

# إصلاح الأخطاء عالية الأولوية
function Fix-HighPriorityIssues {
    Write-Host "`n🚨 إصلاح الأخطاء عالية الأولوية..." -ForegroundColor $Colors.Header
    
    # 1. إنشاء المجلدات المفقودة
    Write-FixLog "إنشاء المجلدات المفقودة..."
    
    $requiredDirectories = @(
        "$ProjectPath\Core",
        "$ProjectPath\Core\Models",
        "$ProjectPath\Core\Services",
        "$ProjectPath\Core\Interfaces",
        "$ProjectPath\Data",
        "$ProjectPath\Data\Entities",
        "$ProjectPath\Data\Repositories",
        "$ProjectPath\ViewModels",
        "$ProjectPath\Views",
        "$ProjectPath\Views\Dialogs",
        "$ProjectPath\Views\Reports",
        "$ProjectPath\Converters",
        "$ProjectPath\Resources",
        "$ProjectPath\Resources\Images",
        "$ProjectPath\Resources\Styles"
    )
    
    foreach ($dir in $requiredDirectories) {
        if (-not (Test-Path $dir)) {
            if (-not $DryRun) {
                try {
                    New-Item -Path $dir -ItemType Directory -Force | Out-Null
                    Write-FixLog "تم إنشاء مجلد: $dir" "SUCCESS"
                }
                catch {
                    Write-FixLog "فشل في إنشاء مجلد: $dir - $($_.Exception.Message)" "ERROR"
                }
            }
            else {
                Write-FixLog "[DRY RUN] سيتم إنشاء مجلد: $dir" "INFO"
            }
        }
        else {
            Write-FixLog "المجلد موجود بالفعل: $dir" "INFO"
        }
    }
    
    # 2. إصلاح أخطاء PowerShell
    Write-FixLog "إصلاح أخطاء PowerShell في DeploymentTestFramework.ps1..."
    
    $psScriptPath = "$ProjectPath\deployment\scripts\DeploymentTestFramework.ps1"
    if (Test-Path $psScriptPath) {
        if (Create-Backup $psScriptPath) {
            try {
                $content = Get-Content $psScriptPath -Raw -Encoding UTF8
                
                # إصلاح مشاكل النصوص العربية مع النقطتين
                $fixes = @{
                    'Write-Log "دقة الشاشة `$resolution: متوافقة"' = 'Write-Log "دقة الشاشة $resolution متوافقة"'
                    'Write-Log "دقة الشاشة `$resolution: غير متوافقة' = 'Write-Log "دقة الشاشة $resolution غير متوافقة'
                    'Write-Log "اللغة `$language: متوافقة"' = 'Write-Log "اللغة $language متوافقة"'
                    'Write-Log "اللغة `$language: غير متوافقة' = 'Write-Log "اللغة $language غير متوافقة'
                    'Write-Log "برنامج مكافحة الفيروسات `$antivirus: متوافق"' = 'Write-Log "برنامج مكافحة الفيروسات $antivirus متوافق"'
                    'Write-Log "برنامج مكافحة الفيروسات `$antivirus: غير متوافق"' = 'Write-Log "برنامج مكافحة الفيروسات $antivirus غير متوافق"'
                    'Write-Log "برنامج مكافحة الفيروسات `$antivirus: غير مثبت"' = 'Write-Log "برنامج مكافحة الفيروسات $antivirus غير مثبت"'
                    'Write-Log "برنامج مكافحة الفيروسات `$antivirus: خطأ في الاختبار' = 'Write-Log "برنامج مكافحة الفيروسات $antivirus خطأ في الاختبار'
                }
                
                $fixedContent = $content
                foreach ($fix in $fixes.GetEnumerator()) {
                    $fixedContent = $fixedContent -replace [regex]::Escape($fix.Key), $fix.Value
                }
                
                # إصلاح أسماء الدوال لتتبع PowerShell conventions
                $functionFixes = @{
                    'function Load-TestConfiguration' = 'function Get-TestConfiguration'
                    'Load-TestConfiguration' = 'Get-TestConfiguration'
                    'function Run-EnvironmentTests' = 'function Start-EnvironmentTests'
                    'Run-EnvironmentTests' = 'Start-EnvironmentTests'
                    'function Generate-HTMLReport' = 'function New-HTMLReport'
                    'Generate-HTMLReport' = 'New-HTMLReport'
                    'function Generate-MarkdownReport' = 'function New-MarkdownReport'
                    'Generate-MarkdownReport' = 'New-MarkdownReport'
                    'function Cleanup-VirtualMachine' = 'function Clear-VirtualMachine'
                    'Cleanup-VirtualMachine' = 'Clear-VirtualMachine'
                    'function Display-FinalResults' = 'function Show-FinalResults'
                    'Display-FinalResults' = 'Show-FinalResults'
                }
                
                foreach ($fix in $functionFixes.GetEnumerator()) {
                    $fixedContent = $fixedContent -replace [regex]::Escape($fix.Key), $fix.Value
                }
                
                # إزالة المتغيرات غير المستخدمة
                $fixedContent = $fixedContent -replace '\$startTime = Get-Date\s*\n\s*Write-Log', 'Write-Log'
                $fixedContent = $fixedContent -replace '\$envStatusClass = if.*\n', ''
                
                if (-not $DryRun) {
                    $fixedContent | Out-File -FilePath $psScriptPath -Encoding UTF8
                    Write-FixLog "تم إصلاح ملف PowerShell بنجاح" "SUCCESS"
                }
                else {
                    Write-FixLog "[DRY RUN] سيتم إصلاح ملف PowerShell" "INFO"
                }
            }
            catch {
                Write-FixLog "فشل في إصلاح ملف PowerShell: $($_.Exception.Message)" "ERROR"
            }
        }
    }
    else {
        Write-FixLog "ملف PowerShell غير موجود: $psScriptPath" "WARNING"
    }
    
    # 3. إنشاء ملفات أساسية مفقودة
    Write-FixLog "إنشاء ملفات أساسية مفقودة..."
    
    # إنشاء ملف AppDbContext.cs
    $appDbContextPath = "$ProjectPath\Data\AppDbContext.cs"
    if (-not (Test-Path $appDbContextPath) -and -not $DryRun) {
        $appDbContextContent = @"
using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        // DbSets will be added here
        public DbSet<User> Users { get; set; } = null!;
        public DbSet<Account> Accounts { get; set; } = null!;
        public DbSet<JournalEntry> JournalEntries { get; set; } = null!;
        public DbSet<Invoice> Invoices { get; set; } = null!;
        public DbSet<Customer> Customers { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // Configure entity relationships and constraints here
        }
    }
}
"@
        try {
            $appDbContextContent | Out-File -FilePath $appDbContextPath -Encoding UTF8
            Write-FixLog "تم إنشاء ملف AppDbContext.cs" "SUCCESS"
        }
        catch {
            Write-FixLog "فشل في إنشاء ملف AppDbContext.cs: $($_.Exception.Message)" "ERROR"
        }
    }
    
    Write-FixLog "انتهى إصلاح الأخطاء عالية الأولوية" "SUCCESS"
}

# إصلاح الأخطاء متوسطة الأولوية
function Fix-MediumPriorityIssues {
    Write-Host "`n⚠️ إصلاح الأخطاء متوسطة الأولوية..." -ForegroundColor $Colors.Header
    
    # 1. إصلاح تحذيرات C#
    Write-FixLog "إصلاح تحذيرات C# في BackgroundTaskManager.cs..."
    
    $bgTaskManagerPath = "$ProjectPath\Helpers\BackgroundTaskManager.cs"
    if (Test-Path $bgTaskManagerPath) {
        if (Create-Backup $bgTaskManagerPath) {
            try {
                $content = Get-Content $bgTaskManagerPath -Raw -Encoding UTF8
                
                # إصلاح new expressions
                $content = $content -replace 'new ConcurrentDictionary<string, BackgroundTask>\(\)', 'new()'
                $content = $content -replace 'new ConcurrentDictionary<string, CancellationTokenSource>\(\)', 'new()'
                $content = $content -replace 'new SemaphoreSlim\(5, 5\)', 'new(5, 5)'
                
                # إزالة using statements غير الضرورية
                $content = $content -replace 'using System\.Text;\r?\n', ''
                $content = $content -replace 'using System\.Windows;\r?\n', ''
                
                if (-not $DryRun) {
                    $content | Out-File -FilePath $bgTaskManagerPath -Encoding UTF8
                    Write-FixLog "تم إصلاح تحذيرات C#" "SUCCESS"
                }
                else {
                    Write-FixLog "[DRY RUN] سيتم إصلاح تحذيرات C#" "INFO"
                }
            }
            catch {
                Write-FixLog "فشل في إصلاح تحذيرات C#: $($_.Exception.Message)" "ERROR"
            }
        }
    }
    
    # 2. تحسين معالجة الاستثناءات في App.xaml.cs
    Write-FixLog "تحسين معالجة الاستثناءات في App.xaml.cs..."
    
    $appXamlPath = "$ProjectPath\App.xaml.cs"
    if (Test-Path $appXamlPath) {
        if (Create-Backup $appXamlPath) {
            try {
                $content = Get-Content $appXamlPath -Raw -Encoding UTF8
                
                # تحسين معالجة الاستثناءات
                $improvedExceptionHandling = @"
                        catch (Exception ex)
                        {
                            // تسجيل الخطأ مع تفاصيل أكثر
                            ExceptionHandler.LogException(ex, "InitializeApplication.DatabaseSchemaUpdate");
                            System.Diagnostics.Debug.WriteLine(`$"[WARNING] خطأ أثناء تعديل بنية جدول المستخدمين: {ex.Message}");
                            
                            // التحقق من نوع الخطأ وإعادة رفعه إذا كان حرجاً
                            if (ex is InvalidOperationException || ex is System.Data.Common.DbException)
                            {
                                // هذه أخطاء قد تكون حرجة، يجب التعامل معها
                                throw new ApplicationException("فشل في تحديث بنية قاعدة البيانات", ex);
                            }
                        }
"@
                
                $content = $content -replace 'catch \(Exception ex\)\s*\{\s*System\.Diagnostics\.Debug\.WriteLine.*?\}', $improvedExceptionHandling
                
                if (-not $DryRun) {
                    $content | Out-File -FilePath $appXamlPath -Encoding UTF8
                    Write-FixLog "تم تحسين معالجة الاستثناءات" "SUCCESS"
                }
                else {
                    Write-FixLog "[DRY RUN] سيتم تحسين معالجة الاستثناءات" "INFO"
                }
            }
            catch {
                Write-FixLog "فشل في تحسين معالجة الاستثناءات: $($_.Exception.Message)" "ERROR"
            }
        }
    }
    
    Write-FixLog "انتهى إصلاح الأخطاء متوسطة الأولوية" "SUCCESS"
}

# إصلاح الأخطاء منخفضة الأولوية
function Fix-LowPriorityIssues {
    Write-Host "`nℹ️ إصلاح الأخطاء منخفضة الأولوية..." -ForegroundColor $Colors.Header
    
    # 1. إضافة XML Documentation
    Write-FixLog "إضافة XML Documentation للطرق العامة..."
    
    # 2. إنشاء ملف .editorconfig
    $editorConfigPath = "$ProjectPath\.editorconfig"
    if (-not (Test-Path $editorConfigPath) -and -not $DryRun) {
        $editorConfigContent = @"
root = true

[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
indent_style = space
indent_size = 4

[*.cs]
# C# formatting rules
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true

[*.ps1]
# PowerShell formatting rules
indent_size = 4

[*.json]
# JSON formatting rules
indent_size = 2

[*.md]
# Markdown formatting rules
trim_trailing_whitespace = false
"@
        try {
            $editorConfigContent | Out-File -FilePath $editorConfigPath -Encoding UTF8
            Write-FixLog "تم إنشاء ملف .editorconfig" "SUCCESS"
        }
        catch {
            Write-FixLog "فشل في إنشاء ملف .editorconfig: $($_.Exception.Message)" "ERROR"
        }
    }
    
    Write-FixLog "انتهى إصلاح الأخطاء منخفضة الأولوية" "SUCCESS"
}

# إنشاء تقرير الإصلاح
function Generate-FixReport {
    Write-Host "`n📋 إنشاء تقرير الإصلاح..." -ForegroundColor $Colors.Header
    
    $reportPath = "$ProjectPath\FixReport-$(Get-Date -Format 'yyyyMMdd-HHmmss').md"
    
    $reportContent = @"
# 🔧 تقرير إصلاح الأخطاء - SmartAccount Pro

## معلومات الإصلاح
- **تاريخ الإصلاح:** $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
- **نوع التشغيل:** $(if ($DryRun) { "محاكاة (Dry Run)" } else { "إصلاح فعلي" })
- **مسار المشروع:** $ProjectPath

## الأخطاء المُصلحة

### الأخطاء عالية الأولوية
$(if ($FixHighPriority) { "✅ تم إصلاحها" } else { "⏭️ تم تخطيها" })
- إنشاء المجلدات المفقودة
- إصلاح أخطاء PowerShell
- إنشاء ملفات أساسية

### الأخطاء متوسطة الأولوية  
$(if ($FixMediumPriority) { "✅ تم إصلاحها" } else { "⏭️ تم تخطيها" })
- إصلاح تحذيرات C#
- تحسين معالجة الاستثناءات

### الأخطاء منخفضة الأولوية
$(if ($FixLowPriority) { "✅ تم إصلاحها" } else { "⏭️ تم تخطيها" })
- إضافة XML Documentation
- إنشاء ملف .editorconfig

## الخطوات التالية
1. اختبار بناء المشروع
2. تشغيل الاختبارات
3. مراجعة الكود المُصلح
4. نشر التحديثات

تم إنشاء هذا التقرير تلقائياً بواسطة سكريبت الإصلاح.
"@
    
    try {
        $reportContent | Out-File -FilePath $reportPath -Encoding UTF8
        Write-FixLog "تم إنشاء تقرير الإصلاح: $reportPath" "SUCCESS"
    }
    catch {
        Write-FixLog "فشل في إنشاء تقرير الإصلاح: $($_.Exception.Message)" "ERROR"
    }
}

# الدالة الرئيسية
function Main {
    try {
        Write-Host "🎯 بدء عملية إصلاح الأخطاء..." -ForegroundColor $Colors.Info
        
        if ($DryRun) {
            Write-Host "⚠️ تشغيل تجريبي - لن يتم تعديل أي ملفات" -ForegroundColor $Colors.Warning
        }
        
        # التحقق من وجود مسار المشروع
        if (-not (Test-Path $ProjectPath)) {
            Write-FixLog "مسار المشروع غير موجود: $ProjectPath" "ERROR"
            return
        }
        
        # إصلاح الأخطاء حسب الأولوية
        if ($FixHighPriority) {
            Fix-HighPriorityIssues
        }
        
        if ($FixMediumPriority) {
            Fix-MediumPriorityIssues
        }
        
        if ($FixLowPriority) {
            Fix-LowPriorityIssues
        }
        
        # إنشاء تقرير الإصلاح
        Generate-FixReport
        
        Write-Host "`n🎉 انتهت عملية إصلاح الأخطاء بنجاح!" -ForegroundColor $Colors.Success
        
        if (-not $DryRun) {
            Write-Host "💡 يُنصح بتشغيل الأوامر التالية:" -ForegroundColor $Colors.Info
            Write-Host "   1. dotnet build - لاختبار بناء المشروع" -ForegroundColor $Colors.Info
            Write-Host "   2. dotnet test - لتشغيل الاختبارات" -ForegroundColor $Colors.Info
            Write-Host "   3. مراجعة الملفات المُعدلة" -ForegroundColor $Colors.Info
        }
        
    }
    catch {
        Write-FixLog "خطأ عام في عملية الإصلاح: $($_.Exception.Message)" "ERROR"
    }
}

# تشغيل السكريبت
Main
