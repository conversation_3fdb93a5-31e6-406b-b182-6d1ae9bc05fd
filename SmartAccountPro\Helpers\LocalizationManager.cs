using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Newtonsoft.Json;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة التعريب والترجمة
    /// </summary>
    public class LocalizationManager
    {
        private static LocalizationManager? _instance;
        private readonly Dictionary<string, Dictionary<string, string>> _translations;
        private readonly string _localizationPath;
        private string _currentCulture;
        private readonly List<string> _supportedCultures;

        /// <summary>
        /// الحصول على النسخة الوحيدة من مدير التعريب
        /// </summary>
        public static LocalizationManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new LocalizationManager();
                }
                return _instance;
            }
        }

        /// <summary>
        /// الحصول على اللغة الحالية
        /// </summary>
        public string CurrentCulture => _currentCulture;

        /// <summary>
        /// الحصول على قائمة اللغات المدعومة
        /// </summary>
        public List<string> SupportedCultures => _supportedCultures;

        /// <summary>
        /// إنشاء نسخة جديدة من مدير التعريب
        /// </summary>
        private LocalizationManager()
        {
            try
            {
                // تحديد مسار ملفات التعريب
                _localizationPath = Path.Combine(App.AppDataPath, "Localization");

                // إنشاء مجلد التعريب إذا لم يكن موجوداً
                if (!Directory.Exists(_localizationPath))
                {
                    Directory.CreateDirectory(_localizationPath);
                }

                // تهيئة قاموس الترجمات
                _translations = new Dictionary<string, Dictionary<string, string>>();

                // تحديد اللغات المدعومة
                _supportedCultures = new List<string> { "ar-SA", "en-US" };

                // تحديد اللغة الافتراضية
                _currentCulture = "ar-SA";

                // تحميل ملفات الترجمة
                LoadTranslations();

                // تعيين اللغة الحالية
                SetCurrentCulture(_currentCulture);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "LocalizationManager.Constructor");
            }
        }

        /// <summary>
        /// تحميل ملفات الترجمة
        /// </summary>
        private void LoadTranslations()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("LocalizationManager.LoadTranslations", () =>
                {
                    // تحميل ملفات الترجمة لكل لغة مدعومة
                    foreach (var culture in _supportedCultures)
                    {
                        // تحديد مسار ملف الترجمة
                        string filePath = Path.Combine(_localizationPath, $"{culture}.json");

                        // التحقق من وجود ملف الترجمة
                        if (!File.Exists(filePath))
                        {
                            // إنشاء ملف ترجمة افتراضي
                            CreateDefaultTranslationFile(culture, filePath);
                        }

                        // قراءة ملف الترجمة
                        string json = File.ReadAllText(filePath, Encoding.UTF8);
                        var translations = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);

                        // إضافة الترجمات إلى القاموس
                        if (translations != null)
                        {
                            _translations[culture] = translations;
                            Debug.WriteLine($"[LOCALIZATION] تم تحميل {translations.Count} ترجمة للغة {culture}.");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "LocalizationManager.LoadTranslations");
            }
        }

        /// <summary>
        /// إنشاء ملف ترجمة افتراضي
        /// </summary>
        /// <param name="culture">رمز اللغة</param>
        /// <param name="filePath">مسار الملف</param>
        private void CreateDefaultTranslationFile(string culture, string filePath)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("LocalizationManager.CreateDefaultTranslationFile", () =>
                {
                    // إنشاء قاموس الترجمات الافتراضية
                    var defaultTranslations = new Dictionary<string, string>();

                    // إضافة ترجمات افتراضية حسب اللغة
                    if (culture == "ar-SA")
                    {
                        defaultTranslations["app.title"] = "برنامج المحاسبة الذكي";
                        defaultTranslations["app.welcome"] = "مرحباً بك في برنامج المحاسبة الذكي";
                        defaultTranslations["login.username"] = "اسم المستخدم";
                        defaultTranslations["login.password"] = "كلمة المرور";
                        defaultTranslations["login.button"] = "تسجيل الدخول";
                        defaultTranslations["login.remember"] = "تذكرني";
                        defaultTranslations["login.forgot"] = "نسيت كلمة المرور؟";
                        defaultTranslations["menu.home"] = "الرئيسية";
                        defaultTranslations["menu.accounts"] = "الحسابات";
                        defaultTranslations["menu.journal"] = "القيود المحاسبية";
                        defaultTranslations["menu.reports"] = "التقارير";
                        defaultTranslations["menu.settings"] = "الإعدادات";
                        defaultTranslations["menu.users"] = "المستخدمين";
                        defaultTranslations["menu.logout"] = "تسجيل الخروج";
                    }
                    else if (culture == "en-US")
                    {
                        defaultTranslations["app.title"] = "Smart Accounting Pro";
                        defaultTranslations["app.welcome"] = "Welcome to Smart Accounting Pro";
                        defaultTranslations["login.username"] = "Username";
                        defaultTranslations["login.password"] = "Password";
                        defaultTranslations["login.button"] = "Login";
                        defaultTranslations["login.remember"] = "Remember me";
                        defaultTranslations["login.forgot"] = "Forgot password?";
                        defaultTranslations["menu.home"] = "Home";
                        defaultTranslations["menu.accounts"] = "Accounts";
                        defaultTranslations["menu.journal"] = "Journal Entries";
                        defaultTranslations["menu.reports"] = "Reports";
                        defaultTranslations["menu.settings"] = "Settings";
                        defaultTranslations["menu.users"] = "Users";
                        defaultTranslations["menu.logout"] = "Logout";
                    }

                    // حفظ الترجمات في ملف
                    string json = JsonConvert.SerializeObject(defaultTranslations, Formatting.Indented);
                    File.WriteAllText(filePath, json, Encoding.UTF8);

                    // إضافة الترجمات إلى القاموس
                    _translations[culture] = defaultTranslations;

                    Debug.WriteLine($"[LOCALIZATION] تم إنشاء ملف ترجمة افتراضي للغة {culture}.");
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "LocalizationManager.CreateDefaultTranslationFile");
            }
        }

        /// <summary>
        /// تعيين اللغة الحالية
        /// </summary>
        /// <param name="culture">رمز اللغة</param>
        /// <returns>نجاح العملية</returns>
        public bool SetCurrentCulture(string culture)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("LocalizationManager.SetCurrentCulture", () =>
                {
                    // التحقق من دعم اللغة
                    if (!_supportedCultures.Contains(culture))
                    {
                        Debug.WriteLine($"[LOCALIZATION] اللغة {culture} غير مدعومة.");
                        return false;
                    }

                    // تعيين اللغة الحالية
                    _currentCulture = culture;

                    // تعيين ثقافة التطبيق
                    Thread.CurrentThread.CurrentCulture = new CultureInfo(culture);
                    Thread.CurrentThread.CurrentUICulture = new CultureInfo(culture);

                    // تعيين اتجاه النص
                    if (culture.StartsWith("ar"))
                    {
                        Application.Current.MainWindow.FlowDirection = FlowDirection.RightToLeft;
                    }
                    else
                    {
                        Application.Current.MainWindow.FlowDirection = FlowDirection.LeftToRight;
                    }

                    // حفظ إعداد اللغة
                    Properties.Settings.Default.Language = culture;
                    Properties.Settings.Default.Save();

                    // إضافة إشعار
                    NotificationManager.Instance.AddNotification(
                        "تم تغيير اللغة",
                        $"تم تغيير لغة التطبيق إلى {GetCultureDisplayName(culture)}.",
                        NotificationType.Info);

                    Debug.WriteLine($"[LOCALIZATION] تم تعيين اللغة الحالية إلى {culture}.");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "LocalizationManager.SetCurrentCulture");
                return false;
            }
        }

        /// <summary>
        /// الحصول على ترجمة نص
        /// </summary>
        /// <param name="key">مفتاح النص</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>النص المترجم</returns>
        public string GetString(string key, string? defaultValue = null)
        {
            try
            {
                // التحقق من وجود ترجمات للغة الحالية
                if (!_translations.ContainsKey(_currentCulture))
                {
                    return defaultValue ?? key;
                }

                // البحث عن الترجمة
                if (_translations[_currentCulture].TryGetValue(key, out string? translation))
                {
                    return translation;
                }

                // إرجاع القيمة الافتراضية أو المفتاح
                return defaultValue ?? key;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "LocalizationManager.GetString");
                return defaultValue ?? key;
            }
        }

        /// <summary>
        /// إضافة ترجمة جديدة
        /// </summary>
        /// <param name="key">مفتاح النص</param>
        /// <param name="value">النص المترجم</param>
        /// <param name="culture">رمز اللغة</param>
        /// <returns>نجاح العملية</returns>
        public bool AddTranslation(string key, string value, string culture)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("LocalizationManager.AddTranslation", () =>
                {
                    // التحقق من دعم اللغة
                    if (!_supportedCultures.Contains(culture))
                    {
                        Debug.WriteLine($"[LOCALIZATION] اللغة {culture} غير مدعومة.");
                        return false;
                    }

                    // التحقق من وجود ترجمات للغة
                    if (!_translations.ContainsKey(culture))
                    {
                        _translations[culture] = new Dictionary<string, string>();
                    }

                    // إضافة الترجمة
                    _translations[culture][key] = value;

                    // حفظ الترجمات
                    SaveTranslations(culture);

                    Debug.WriteLine($"[LOCALIZATION] تم إضافة ترجمة جديدة: {key} = {value} للغة {culture}.");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "LocalizationManager.AddTranslation");
                return false;
            }
        }

        /// <summary>
        /// حفظ الترجمات
        /// </summary>
        /// <param name="culture">رمز اللغة</param>
        /// <returns>نجاح العملية</returns>
        private bool SaveTranslations(string culture)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return PerformanceHelper.MeasureExecutionTime("LocalizationManager.SaveTranslations", () =>
                {
                    // التحقق من وجود ترجمات للغة
                    if (!_translations.ContainsKey(culture))
                    {
                        Debug.WriteLine($"[LOCALIZATION] لا توجد ترجمات للغة {culture}.");
                        return false;
                    }

                    // تحديد مسار ملف الترجمة
                    string filePath = Path.Combine(_localizationPath, $"{culture}.json");

                    // حفظ الترجمات في ملف
                    string json = JsonConvert.SerializeObject(_translations[culture], Formatting.Indented);
                    File.WriteAllText(filePath, json, Encoding.UTF8);

                    Debug.WriteLine($"[LOCALIZATION] تم حفظ {_translations[culture].Count} ترجمة للغة {culture}.");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "LocalizationManager.SaveTranslations");
                return false;
            }
        }

        /// <summary>
        /// الحصول على اسم اللغة
        /// </summary>
        /// <param name="culture">رمز اللغة</param>
        /// <returns>اسم اللغة</returns>
        public string GetCultureDisplayName(string culture)
        {
            try
            {
                // الحصول على معلومات الثقافة
                var cultureInfo = new CultureInfo(culture);
                
                // إرجاع اسم اللغة
                return cultureInfo.NativeName;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "LocalizationManager.GetCultureDisplayName");
                return culture;
            }
        }

        /// <summary>
        /// الحصول على قائمة اللغات المدعومة مع أسمائها
        /// </summary>
        /// <returns>قائمة اللغات المدعومة</returns>
        public Dictionary<string, string> GetSupportedCulturesWithNames()
        {
            try
            {
                // إنشاء قاموس اللغات
                var cultures = new Dictionary<string, string>();
                
                // إضافة اللغات المدعومة
                foreach (var culture in _supportedCultures)
                {
                    cultures[culture] = GetCultureDisplayName(culture);
                }
                
                return cultures;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "LocalizationManager.GetSupportedCulturesWithNames");
                return new Dictionary<string, string>();
            }
        }

        /// <summary>
        /// تهيئة مدير التعريب
        /// </summary>
        public static void Initialize()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                PerformanceHelper.MeasureExecutionTime("LocalizationManager.Initialize", () =>
                {
                    // قراءة إعداد اللغة المحفوظ
                    string savedCulture = Properties.Settings.Default.Language;

                    if (!string.IsNullOrEmpty(savedCulture))
                    {
                        // تعيين اللغة المحفوظة
                        Instance.SetCurrentCulture(savedCulture);
                    }
                    else
                    {
                        // تعيين اللغة الافتراضية
                        Instance.SetCurrentCulture("ar-SA");
                    }

                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "LocalizationManager.Initialize");
            }
        }
    }
}
