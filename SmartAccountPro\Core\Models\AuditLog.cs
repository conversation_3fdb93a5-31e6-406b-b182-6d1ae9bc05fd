using System;
using System.ComponentModel.DataAnnotations;

namespace SmartAccountPro.Core.Models
{
    /// <summary>
    /// نموذج سجل المراجعة
    /// </summary>
    public class AuditLog
    {
        /// <summary>
        /// معرف السجل
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// نوع العملية
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// نوع الكيان
        /// </summary>
        [Required]
        [StringLength(100)]
        public string EntityType { get; set; } = string.Empty;

        /// <summary>
        /// معرف الكيان
        /// </summary>
        public int EntityId { get; set; }

        /// <summary>
        /// القيم القديمة (JSON)
        /// </summary>
        public string? OldValues { get; set; }

        /// <summary>
        /// القيم الجديدة (JSON)
        /// </summary>
        public string? NewValues { get; set; }

        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        [StringLength(100)]
        public string? UserName { get; set; }

        /// <summary>
        /// عنوان IP
        /// </summary>
        [StringLength(45)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// معلومات المتصفح
        /// </summary>
        [StringLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// تاريخ العملية
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// هل العملية نجحت
        /// </summary>
        public bool IsSuccess { get; set; } = true;

        /// <summary>
        /// رسالة الخطأ (إن وجدت)
        /// </summary>
        [StringLength(1000)]
        public string? ErrorMessage { get; set; }
    }
}
