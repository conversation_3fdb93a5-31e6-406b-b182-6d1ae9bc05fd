using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Data;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using System.Linq;
using System.Collections.Generic;

namespace SmartAccountPro
{
    /// <summary>
    /// اختبار بسيط للوظائف الأساسية
    /// </summary>
    public static class SimpleTest
    {
        /// <summary>
        /// تشغيل اختبار بسيط
        /// </summary>
        public static async Task RunBasicTestAsync()
        {
            Console.WriteLine("🔍 بدء الاختبار البسيط...");
            
            try
            {
                // اختبار إنشاء قاعدة بيانات مؤقتة
                string tempDbPath = Path.Combine(Path.GetTempPath(), "TestDB.db");
                if (File.Exists(tempDbPath))
                    File.Delete(tempDbPath);

                var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
                optionsBuilder.UseSqlite($"Data Source={tempDbPath}");

                using var context = new AppDbContext(optionsBuilder.Options);
                
                // إنشاء قاعدة البيانات
                await context.Database.EnsureCreatedAsync();
                Console.WriteLine("✅ تم إنشاء قاعدة البيانات");

                // اختبار إضافة مستخدم
                var user = new User
                {
                    Username = "testuser",
                    FullName = "مستخدم تجريبي",
                    Email = "<EMAIL>",
                    PasswordHash = "hash123",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                context.Users.Add(user);
                await context.SaveChangesAsync();
                Console.WriteLine("✅ تم إضافة مستخدم تجريبي");

                // اختبار البحث
                var foundUser = await context.Users.FirstOrDefaultAsync(u => u.Username == "testuser");
                if (foundUser != null)
                {
                    Console.WriteLine($"✅ تم العثور على المستخدم: {foundUser.FullName}");
                }

                // تنظيف
                File.Delete(tempDbPath);
                Console.WriteLine("✅ تم تنظيف الملفات المؤقتة");

                Console.WriteLine("🎉 الاختبار البسيط نجح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"تفاصيل: {ex.StackTrace}");
            }
        }
    }
}
