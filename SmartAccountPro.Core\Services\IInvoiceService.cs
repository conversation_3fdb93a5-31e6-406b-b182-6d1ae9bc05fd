using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartAccountPro.Core.Models;

namespace SmartAccountPro.Core.Services
{
    /// <summary>
    /// واجهة خدمة الفواتير
    /// </summary>
    public interface IInvoiceService
    {
        /// <summary>
        /// الحصول على جميع الفواتير
        /// </summary>
        Task<IEnumerable<Invoice>> GetAllInvoicesAsync();

        /// <summary>
        /// الحصول على فواتير المبيعات
        /// </summary>
        Task<IEnumerable<Invoice>> GetSalesInvoicesAsync();

        /// <summary>
        /// الحصول على فواتير المشتريات
        /// </summary>
        Task<IEnumerable<Invoice>> GetPurchaseInvoicesAsync();

        /// <summary>
        /// الحصول على الفواتير في فترة معينة
        /// </summary>
        Task<IEnumerable<Invoice>> GetInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate, InvoiceType? type = null);

        /// <summary>
        /// الحصول على فواتير عميل معين
        /// </summary>
        Task<IEnumerable<Invoice>> GetInvoicesByCustomerAsync(int customerId);

        /// <summary>
        /// الحصول على فواتير مورد معين
        /// </summary>
        Task<IEnumerable<Invoice>> GetInvoicesBySupplierAsync(int supplierId);

        /// <summary>
        /// الحصول على فاتورة بواسطة المعرف
        /// </summary>
        Task<Invoice> GetInvoiceByIdAsync(int id);

        /// <summary>
        /// الحصول على فاتورة بواسطة الرقم
        /// </summary>
        Task<Invoice> GetInvoiceByNumberAsync(string invoiceNumber);

        /// <summary>
        /// إنشاء فاتورة جديدة
        /// </summary>
        Task<Invoice> CreateInvoiceAsync(Invoice invoice);

        /// <summary>
        /// تحديث فاتورة
        /// </summary>
        Task<bool> UpdateInvoiceAsync(Invoice invoice);

        /// <summary>
        /// حذف فاتورة
        /// </summary>
        Task<bool> DeleteInvoiceAsync(int id);

        /// <summary>
        /// اعتماد فاتورة
        /// </summary>
        Task<bool> ApproveInvoiceAsync(int id);

        /// <summary>
        /// إلغاء فاتورة
        /// </summary>
        Task<bool> CancelInvoiceAsync(int id);

        /// <summary>
        /// ترحيل فاتورة محاسبياً
        /// </summary>
        Task<bool> PostInvoiceAsync(int id, int userId);

        /// <summary>
        /// إضافة دفعة على فاتورة
        /// </summary>
        Task<bool> AddPaymentToInvoiceAsync(int id, decimal amount, DateTime paymentDate, string referenceNumber, int userId);

        /// <summary>
        /// حساب إجماليات الفاتورة
        /// </summary>
        Invoice CalculateInvoiceTotals(Invoice invoice);

        /// <summary>
        /// إنشاء رقم فاتورة جديد
        /// </summary>
        Task<string> GenerateInvoiceNumberAsync(InvoiceType type);
    }
}
