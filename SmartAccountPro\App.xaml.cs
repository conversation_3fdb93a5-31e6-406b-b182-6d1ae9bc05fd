﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Data;
using SmartAccountPro.Helpers;

namespace SmartAccountPro
{
    /// <summary>
    /// منطق التفاعل لـ App.xaml
    /// </summary>
    public partial class App : Application
    {
        /// <summary>
        /// مسار قاعدة البيانات
        /// </summary>
        public static string DatabasePath { get; private set; } = string.Empty;

        /// <summary>
        /// مسار ملفات التطبيق
        /// </summary>
        public static string AppDataPath { get; private set; } = string.Empty;

        /// <summary>
        /// سياق قاعدة البيانات
        /// </summary>
        public static AppDbContext DbContext { get; private set; } = null!;

        /// <summary>
        /// مدير قاعدة البيانات
        /// </summary>
        public static DatabaseManager DbManager { get; private set; } = null!;

        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // إعداد معالج الاستثناءات العام
                ExceptionHandler.SetupGlobalExceptionHandling();

                // تهيئة مساعد قياس الأداء
                PerformanceHelper.StartTimer("ApplicationStartup");

                base.OnStartup(e);

                // تشغيل الاختبار البسيط أولاً
                await SimpleTest.RunBasicTestAsync();

                InitializeApplication();

                // عرض شاشة تسجيل الدخول
                var loginWindow = new Views.LoginWindow();
                loginWindow.Show();

                // إيقاف مؤقت قياس الأداء
                long startupTime = PerformanceHelper.StopTimer("ApplicationStartup");
                System.Diagnostics.Debug.WriteLine($"[PERFORMANCE] وقت بدء التطبيق: {startupTime}ms");
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                ExceptionHandler.LogException(ex, "App.OnStartup");

                // عرض رسالة خطأ ودية للمستخدم
                MessageBox.Show(
                    ExceptionHandler.GetFriendlyErrorMessage(ex),
                    "خطأ في بدء التطبيق",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                // إغلاق التطبيق
                Shutdown();
            }
        }

        /// <summary>
        /// تهيئة التطبيق
        /// </summary>
        private async void InitializeApplication()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                await PerformanceHelper.MeasureExecutionTimeAsync("InitializeApplication", async () =>
                {
                    // إنشاء مجلد بيانات التطبيق إذا لم يكن موجوداً
                    AppDataPath = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                        "SmartAccountPro");

                    if (!Directory.Exists(AppDataPath))
                    {
                        Directory.CreateDirectory(AppDataPath);
                    }

                    // تحديد مسار قاعدة البيانات
                    DatabasePath = Path.Combine(AppDataPath, "SmartAccountPro.db");

                    try
                    {
                        // تهيئة سياق قاعدة البيانات
                        var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
                        optionsBuilder.UseSqlite($"Data Source={DatabasePath}");

                        // إضافة خيارات لتجاهل قيود NOT NULL عند الحاجة
                        optionsBuilder.EnableSensitiveDataLogging();

                        DbContext = new AppDbContext(optionsBuilder.Options);

                        // التأكد من إنشاء قاعدة البيانات
                        DbContext.Database.EnsureCreated();

                        // تعديل قاعدة البيانات لجعل حقل PhoneNumber قابل للقيم الفارغة
                        try
                        {
                            // محاولة تنفيذ استعلام SQL مباشر لتعديل بنية الجدول
                            await DbContext.Database.ExecuteSqlRawAsync(
                                "PRAGMA foreign_keys=off; " +
                                "BEGIN TRANSACTION; " +
                                "CREATE TABLE Users_temp AS SELECT * FROM Users; " +
                                "DROP TABLE Users; " +
                                "CREATE TABLE Users (" +
                                "Id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                                "Username TEXT NOT NULL, " +
                                "PasswordHash TEXT NOT NULL, " +
                                "FullName TEXT NOT NULL, " +
                                "Email TEXT, " +
                                "PhoneNumber TEXT, " + // تم إزالة قيد NOT NULL
                                "Role TEXT NOT NULL, " +
                                "IsActive INTEGER NOT NULL, " +
                                "LastLoginDate TEXT, " +
                                "CreatedDate TEXT NOT NULL, " +
                                "ModifiedDate TEXT); " +
                                "INSERT INTO Users SELECT * FROM Users_temp; " +
                                "DROP TABLE Users_temp; " +
                                "COMMIT; " +
                                "PRAGMA foreign_keys=on;");
                        }
                        catch (Exception ex)
                        {
                            // تسجيل الخطأ ولكن استمر في التنفيذ
                            System.Diagnostics.Debug.WriteLine($"[WARNING] خطأ أثناء تعديل بنية جدول المستخدمين: {ex.Message}");
                            // تجاهل الأخطاء هنا لأن الجدول قد لا يكون موجوداً بعد
                            // سيتم إنشاؤه بالشكل الصحيح في الخطوة التالية
                        }

                        // تهيئة مدير قاعدة البيانات
                        DbManager = new DatabaseManager(DbContext);
                        await DbManager.InitializeDatabaseAsync();
                    }
                    catch (Exception dbEx)
                    {
                        MessageBox.Show(
                            ExceptionHandler.GetFriendlyErrorMessage(dbEx),
                            "خطأ في قاعدة البيانات",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);

                        ExceptionHandler.HandleException(dbEx, "InitializeApplication.Database");
                    }
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "InitializeApplication");
            }
        }
    }
}
