{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro\\SmartAccountPro.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Core\\SmartAccountPro.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Core\\SmartAccountPro.Core.csproj", "projectName": "SmartAccountPro.Core", "projectPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Core\\SmartAccountPro.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": {"target": "Package", "version": "[6.0.27, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Data\\SmartAccountPro.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Data\\SmartAccountPro.Data.csproj", "projectName": "SmartAccountPro.Data", "projectPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Data\\SmartAccountPro.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Data\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Core\\SmartAccountPro.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Core\\SmartAccountPro.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.27, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro\\SmartAccountPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro\\SmartAccountPro.csproj", "projectName": "SmartAccountPro", "projectPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro\\SmartAccountPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Core\\SmartAccountPro.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Core\\SmartAccountPro.Core.csproj"}, "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Data\\SmartAccountPro.Data.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\789\\SmartAccountPro.Data\\SmartAccountPro.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"EPPlus": {"target": "Package", "version": "[8.0.5, )"}, "LiveChartsCore.SkiaSharpView.WPF": {"target": "Package", "version": "[2.0.0-rc5.4, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}