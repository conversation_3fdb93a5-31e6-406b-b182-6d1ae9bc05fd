using Microsoft.EntityFrameworkCore;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SmartAccountPro.Data.Services
{
    /// <summary>
    /// خدمة إدارة المهام
    /// </summary>
    public class TaskService
    {
        private readonly AppDbContext _dbContext;

        public TaskService(AppDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// الحصول على جميع المهام
        /// </summary>
        public async Task<List<TaskItem>> GetAllTasksAsync(bool includeDeleted = false)
        {
            try
            {
                var query = _dbContext.Tasks.AsQueryable();

                if (!includeDeleted)
                {
                    query = query.Where(t => !t.IsDeleted);
                }

                return await query
                    .Include(t => t.AssignedToUser)
                    .Include(t => t.CreatedByUser)
                    .Include(t => t.RelatedAccount)
                    .OrderByDescending(t => t.Priority)
                    .ThenBy(t => t.DueDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TaskService.GetAllTasksAsync");
                return new List<TaskItem>();
            }
        }

        /// <summary>
        /// الحصول على المهام حسب المستخدم المسؤول
        /// </summary>
        public async Task<List<TaskItem>> GetTasksByAssignedUserAsync(int userId, bool includeDeleted = false)
        {
            try
            {
                var query = _dbContext.Tasks
                    .Where(t => t.AssignedToUserId == userId);

                if (!includeDeleted)
                {
                    query = query.Where(t => !t.IsDeleted);
                }

                return await query
                    .Include(t => t.AssignedToUser)
                    .Include(t => t.CreatedByUser)
                    .Include(t => t.RelatedAccount)
                    .OrderByDescending(t => t.Priority)
                    .ThenBy(t => t.DueDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TaskService.GetTasksByAssignedUserAsync");
                return new List<TaskItem>();
            }
        }

        /// <summary>
        /// الحصول على المهام المتأخرة
        /// </summary>
        public async Task<List<TaskItem>> GetOverdueTasksAsync()
        {
            try
            {
                return await _dbContext.Tasks
                    .Where(t => !t.IsDeleted && t.Status != TaskItemStatus.Completed && t.Status != TaskItemStatus.Cancelled && t.DueDate < DateTime.Now)
                    .Include(t => t.AssignedToUser)
                    .Include(t => t.CreatedByUser)
                    .OrderByDescending(t => t.Priority)
                    .ThenBy(t => t.DueDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TaskService.GetOverdueTasksAsync");
                return new List<TaskItem>();
            }
        }

        /// <summary>
        /// الحصول على المهام المستحقة اليوم
        /// </summary>
        public async Task<List<TaskItem>> GetTasksDueTodayAsync()
        {
            try
            {
                var today = DateTime.Today;
                var tomorrow = today.AddDays(1);

                return await _dbContext.Tasks
                    .Where(t => !t.IsDeleted && t.Status != TaskItemStatus.Completed && t.Status != TaskItemStatus.Cancelled && t.DueDate >= today && t.DueDate < tomorrow)
                    .Include(t => t.AssignedToUser)
                    .Include(t => t.CreatedByUser)
                    .OrderByDescending(t => t.Priority)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TaskService.GetTasksDueTodayAsync");
                return new List<TaskItem>();
            }
        }

        /// <summary>
        /// الحصول على مهمة بواسطة المعرف
        /// </summary>
        public async Task<TaskItem> GetTaskByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Tasks
                    .Include(t => t.AssignedToUser)
                    .Include(t => t.CreatedByUser)
                    .Include(t => t.RelatedAccount)
                    .Include(t => t.RelatedJournalEntry)
                    .FirstOrDefaultAsync(t => t.Id == id && !t.IsDeleted);
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TaskService.GetTaskByIdAsync");
                return null;
            }
        }

        /// <summary>
        /// إضافة مهمة جديدة
        /// </summary>
        public async Task<bool> AddTaskAsync(TaskItem task)
        {
            try
            {
                task.CreatedDate = DateTime.Now;

                if (task.IsRecurring && task.RecurrencePattern.HasValue)
                {
                    task.NextRecurrenceDate = CalculateNextRecurrenceDate(task.DueDate, task.RecurrencePattern.Value);
                }

                _dbContext.Tasks.Add(task);
                await _dbContext.SaveChangesAsync();

                // إنشاء إشعار للمستخدم المسؤول
                if (task.AssignedToUserId.HasValue)
                {
                    var notification = new Notification
                    {
                        Title = "مهمة جديدة",
                        Content = $"تم تعيين مهمة جديدة لك: {task.Title}",
                        Type = NotificationType.Task,
                        CreatedAt = DateTime.Now,
                        UserId = task.AssignedToUserId.Value,
                        EntityId = task.Id,
                        EntityType = "Task"
                    };

                    _dbContext.Notifications.Add(notification);
                    await _dbContext.SaveChangesAsync();

                    // إشعار المستخدم
                }

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TaskService.AddTaskAsync");
                return false;
            }
        }

        /// <summary>
        /// تحديث مهمة
        /// </summary>
        public async Task<bool> UpdateTaskAsync(TaskItem task)
        {
            try
            {
                var existingTask = await _dbContext.Tasks.FindAsync(task.Id);
                if (existingTask == null || existingTask.IsDeleted)
                {
                    return false;
                }

                // حفظ المستخدم المسؤول السابق للتحقق من التغييرات
                var previousAssignedUserId = existingTask.AssignedToUserId;

                // تحديث الخصائص
                existingTask.Title = task.Title;
                existingTask.Description = task.Description;
                existingTask.DueDate = task.DueDate;
                existingTask.Status = task.Status;
                existingTask.Priority = task.Priority;
                existingTask.AssignedToUserId = task.AssignedToUserId;
                existingTask.RelatedAccountId = task.RelatedAccountId;
                existingTask.RelatedJournalEntryId = task.RelatedJournalEntryId;
                existingTask.RelatedInvoiceId = task.RelatedInvoiceId;
                existingTask.CompletionPercentage = task.CompletionPercentage;
                existingTask.IsRecurring = task.IsRecurring;
                existingTask.RecurrencePattern = task.RecurrencePattern;
                existingTask.NextRecurrenceDate = task.NextRecurrenceDate;
                existingTask.Notes = task.Notes;

                // تحديث تاريخ الإكمال إذا تم تغيير الحالة إلى مكتملة
                if (task.Status == TaskItemStatus.Completed && existingTask.CompletedDate == null)
                {
                    existingTask.CompletedDate = DateTime.Now;
                    existingTask.CompletionPercentage = 100;

                    // إنشاء إشعار للمستخدم المنشئ
                    var notification = new Notification
                    {
                        Title = "تم إكمال مهمة",
                        Content = $"تم إكمال المهمة: {existingTask.Title}",
                        Type = NotificationType.Task,
                        CreatedAt = DateTime.Now,
                        UserId = existingTask.CreatedByUserId,
                        EntityId = existingTask.Id,
                        EntityType = "Task"
                    };

                    _dbContext.Notifications.Add(notification);

                    // إشعار المستخدم

                    // إنشاء مهمة جديدة إذا كانت متكررة
                    if (existingTask.IsRecurring && existingTask.RecurrencePattern.HasValue && existingTask.NextRecurrenceDate.HasValue)
                    {
                        await CreateRecurringTaskAsync(existingTask);
                    }
                }
                else if (task.Status != TaskItemStatus.Completed)
                {
                    existingTask.CompletedDate = null;
                }

                // إذا تم تغيير المستخدم المسؤول، إنشاء إشعار للمستخدم الجديد
                if (task.AssignedToUserId.HasValue && task.AssignedToUserId != previousAssignedUserId)
                {
                    var notification = new Notification
                    {
                        Title = "تم تعيين مهمة لك",
                        Content = $"تم تعيين المهمة: {existingTask.Title} لك",
                        Type = NotificationType.Task,
                        CreatedAt = DateTime.Now,
                        UserId = task.AssignedToUserId.Value,
                        EntityId = existingTask.Id,
                        EntityType = "Task"
                    };

                    _dbContext.Notifications.Add(notification);

                    // إشعار المستخدم
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TaskService.UpdateTaskAsync");
                return false;
            }
        }

        /// <summary>
        /// حذف مهمة
        /// </summary>
        public async Task<bool> DeleteTaskAsync(int id)
        {
            try
            {
                var task = await _dbContext.Tasks.FindAsync(id);
                if (task == null)
                {
                    return false;
                }

                task.IsDeleted = true;
                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TaskService.DeleteTaskAsync");
                return false;
            }
        }

        /// <summary>
        /// إنشاء مهمة متكررة جديدة
        /// </summary>
        private async Task<bool> CreateRecurringTaskAsync(TaskItem completedTask)
        {
            try
            {
                if (!completedTask.IsRecurring || !completedTask.RecurrencePattern.HasValue || !completedTask.NextRecurrenceDate.HasValue)
                {
                    return false;
                }

                var newTask = new TaskItem
                {
                    Title = completedTask.Title,
                    Description = completedTask.Description,
                    CreatedDate = DateTime.Now,
                    DueDate = completedTask.NextRecurrenceDate.Value,
                    Status = TaskItemStatus.New,
                    Priority = completedTask.Priority,
                    AssignedToUserId = completedTask.AssignedToUserId,
                    CreatedByUserId = completedTask.CreatedByUserId,
                    RelatedAccountId = completedTask.RelatedAccountId,
                    RelatedJournalEntryId = completedTask.RelatedJournalEntryId,
                    RelatedInvoiceId = completedTask.RelatedInvoiceId,
                    CompletionPercentage = 0,
                    IsRecurring = true,
                    RecurrencePattern = completedTask.RecurrencePattern,
                    NextRecurrenceDate = CalculateNextRecurrenceDate(completedTask.NextRecurrenceDate.Value, completedTask.RecurrencePattern.Value),
                    Notes = completedTask.Notes,
                    IsDeleted = false
                };

                _dbContext.Tasks.Add(newTask);
                await _dbContext.SaveChangesAsync();

                // إنشاء إشعار للمستخدم المسؤول
                if (newTask.AssignedToUserId.HasValue)
                {
                    var notification = new Notification
                    {
                        Title = "تم إنشاء مهمة متكررة",
                        Content = $"تم إنشاء مهمة متكررة جديدة: {newTask.Title}",
                        Type = NotificationType.Task,
                        CreatedAt = DateTime.Now,
                        UserId = newTask.AssignedToUserId.Value,
                        EntityId = newTask.Id,
                        EntityType = "Task"
                    };

                    _dbContext.Notifications.Add(notification);
                    await _dbContext.SaveChangesAsync();

                    // إشعار المستخدم
                }

                return true;
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "TaskService.CreateRecurringTaskAsync");
                return false;
            }
        }

        /// <summary>
        /// حساب تاريخ التكرار التالي
        /// </summary>
        private DateTime CalculateNextRecurrenceDate(DateTime currentDate, RecurrencePattern pattern)
        {
            switch (pattern)
            {
                case RecurrencePattern.Daily:
                    return currentDate.AddDays(1);
                case RecurrencePattern.Weekly:
                    return currentDate.AddDays(7);
                case RecurrencePattern.Monthly:
                    return currentDate.AddMonths(1);
                case RecurrencePattern.Quarterly:
                    return currentDate.AddMonths(3);
                case RecurrencePattern.Yearly:
                    return currentDate.AddYears(1);
                default:
                    return currentDate.AddMonths(1);
            }
        }
    }
}
