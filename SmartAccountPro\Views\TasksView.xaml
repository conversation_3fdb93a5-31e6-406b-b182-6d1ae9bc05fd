<UserControl x:Class="SmartAccountPro.Views.TasksView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SmartAccountPro.Views"
             xmlns:vm="clr-namespace:SmartAccountPro.ViewModels"
             xmlns:converters="clr-namespace:SmartAccountPro.Converters"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <UserControl.DataContext>
        <vm:TasksViewModel />
    </UserControl.DataContext>

    <UserControl.Resources>
        <converters:TaskStatusToStringConverter x:Key="TaskStatusToStringConverter" />
        <converters:TaskPriorityToStringConverter x:Key="TaskPriorityToStringConverter" />
        <converters:TaskPriorityToBrushConverter x:Key="TaskPriorityToBrushConverter" />
        <converters:TaskStatusToBrushConverter x:Key="TaskStatusToBrushConverter" />
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
        <converters:DateToStringConverter x:Key="DateToStringConverter" />

        <Style x:Key="TaskCardStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource CardBackgroundColor}" />
            <Setter Property="BorderBrush" Value="{DynamicResource BorderColor}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="5" />
            <Setter Property="Margin" Value="0,0,0,10" />
            <Setter Property="Padding" Value="15" />
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.2" />
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="{DynamicResource BackgroundColor}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <Border Grid.Row="0"
                Background="{DynamicResource PrimaryColor}"
                CornerRadius="10"
                Padding="20,15"
                Margin="20,20,20,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="2" BlurRadius="10" Opacity="0.2" Color="#000000"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="&#xE7C1;"
                               FontFamily="Segoe MDL2 Assets"
                               FontSize="24"
                               Foreground="White"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة المهام"
                               FontSize="24"
                               FontWeight="Bold"
                               Foreground="White">
                        <TextBlock.Effect>
                            <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.3" Color="#000000"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </StackPanel>

                <StackPanel Grid.Column="1"
                            Orientation="Horizontal">
                    <Button Command="{Binding AddTaskCommand}"
                            Style="{StaticResource GreenButton}"
                            Margin="0,0,10,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE710;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="مهمة جديدة"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button Command="{Binding RefreshCommand}"
                            Style="{StaticResource ModernButton}"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE72C;"
                                       FontFamily="Segoe MDL2 Assets"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- أدوات التصفية -->
        <Border Grid.Row="1"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="10"
                Padding="20,15"
                Margin="20,0,20,15">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- حالة المهمة -->
                <StackPanel Grid.Column="0"
                            Margin="0,0,20,0">
                    <TextBlock Text="الحالة"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,5"/>
                    <Border BorderBrush="#DDDDDD"
                            BorderThickness="1"
                            CornerRadius="4">
                        <ComboBox Width="120"
                                  ItemsSource="{Binding TaskStatuses}"
                                  SelectedItem="{Binding SelectedTaskStatus}"
                                  DisplayMemberPath="Name"
                                  BorderThickness="0"
                                  Background="Transparent"
                                  Padding="8,5"/>
                    </Border>
                </StackPanel>

                <!-- أولوية المهمة -->
                <StackPanel Grid.Column="1"
                            Margin="0,0,20,0">
                    <TextBlock Text="الأولوية"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,5"/>
                    <Border BorderBrush="#DDDDDD"
                            BorderThickness="1"
                            CornerRadius="4">
                        <ComboBox Width="120"
                                  ItemsSource="{Binding TaskPriorities}"
                                  SelectedItem="{Binding SelectedTaskPriority}"
                                  DisplayMemberPath="Name"
                                  BorderThickness="0"
                                  Background="Transparent"
                                  Padding="8,5"/>
                    </Border>
                </StackPanel>

                <!-- المستخدم المسؤول -->
                <StackPanel Grid.Column="2"
                            Margin="0,0,20,0">
                    <TextBlock Text="المسؤول"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,5"/>
                    <Border BorderBrush="#DDDDDD"
                            BorderThickness="1"
                            CornerRadius="4">
                        <ComboBox Width="150"
                                  ItemsSource="{Binding Users}"
                                  SelectedItem="{Binding SelectedUser}"
                                  DisplayMemberPath="FullName"
                                  BorderThickness="0"
                                  Background="Transparent"
                                  Padding="8,5"/>
                    </Border>
                </StackPanel>

                <!-- تاريخ الاستحقاق -->
                <StackPanel Grid.Column="3"
                            Margin="0,0,20,0">
                    <TextBlock Text="تاريخ الاستحقاق"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,5"/>
                    <Border BorderBrush="#DDDDDD"
                            BorderThickness="1"
                            CornerRadius="4">
                        <ComboBox Width="150"
                                  ItemsSource="{Binding DueDateFilters}"
                                  SelectedItem="{Binding SelectedDueDateFilter}"
                                  DisplayMemberPath="Name"
                                  BorderThickness="0"
                                  Background="Transparent"
                                  Padding="8,5"/>
                    </Border>
                </StackPanel>

                <!-- زر التصفية -->
                <Button Grid.Column="5"
                        Command="{Binding ApplyFilterCommand}"
                        Style="{StaticResource ModernButton}"
                        VerticalAlignment="Bottom"
                        Padding="12,8">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="&#xE71C;"
                                   FontFamily="Segoe MDL2 Assets"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                        <TextBlock Text="تطبيق التصفية"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- قائمة المهام -->
        <Border Grid.Row="2"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="10"
                Padding="15"
                Margin="20,0,20,20">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="1" BlurRadius="5" Opacity="0.1" Color="#000000"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="&#xE8F9;"
                               FontFamily="Segoe MDL2 Assets"
                               Foreground="{DynamicResource PrimaryColor}"
                               FontSize="18"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>
                    <TextBlock Text="قائمة المهام"
                               FontWeight="Bold"
                               FontSize="16"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <Grid Grid.Row="1">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding FilteredTasks}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource TaskCardStyle}" Margin="0,0,0,15">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <!-- رأس المهمة -->
                                            <Grid Grid.Row="0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <StackPanel Grid.Column="0" Orientation="Horizontal">
                                                    <CheckBox IsChecked="{Binding IsCompleted, Mode=TwoWay}"
                                                              Command="{Binding DataContext.CompleteTaskCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                                              CommandParameter="{Binding}"
                                                              VerticalAlignment="Center"
                                                              Margin="0,0,10,0"/>

                                                    <TextBlock Text="{Binding Title}"
                                                               FontSize="16"
                                                               FontWeight="SemiBold"
                                                               Foreground="#333333"
                                                               TextDecorations="{Binding IsCompleted, Converter={StaticResource TaskCompletedToTextDecorationConverter}}"
                                                               VerticalAlignment="Center"/>
                                                </StackPanel>

                                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                                    <Border Background="{Binding Priority, Converter={StaticResource TaskPriorityToBrushConverter}}"
                                                            CornerRadius="4"
                                                            Padding="8,3"
                                                            Margin="0,0,8,0">
                                                        <TextBlock Text="{Binding Priority, Converter={StaticResource TaskPriorityToStringConverter}}"
                                                                   Foreground="White"
                                                                   FontSize="12"
                                                                   FontWeight="SemiBold"/>
                                                    </Border>

                                                    <Border Background="{Binding Status, Converter={StaticResource TaskStatusToBrushConverter}}"
                                                            CornerRadius="4"
                                                            Padding="8,3"
                                                            Margin="0,0,8,0">
                                                        <TextBlock Text="{Binding Status, Converter={StaticResource TaskStatusToStringConverter}}"
                                                                   Foreground="White"
                                                                   FontSize="12"
                                                                   FontWeight="SemiBold"/>
                                                    </Border>

                                                    <Button Content="&#xE70F;"
                                                            FontFamily="Segoe MDL2 Assets"
                                                            Command="{Binding DataContext.EditTaskCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                                            CommandParameter="{Binding}"
                                                            Style="{StaticResource IconButton}"
                                                            ToolTip="تعديل المهمة"
                                                            Margin="0,0,5,0"/>

                                                    <Button Content="&#xE74D;"
                                                            FontFamily="Segoe MDL2 Assets"
                                                            Command="{Binding DataContext.DeleteTaskCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                                            CommandParameter="{Binding}"
                                                            Style="{StaticResource IconButton}"
                                                            ToolTip="حذف المهمة"/>
                                                </StackPanel>
                                            </Grid>

                                            <!-- وصف المهمة -->
                                            <TextBlock Grid.Row="1"
                                                       Text="{Binding Description}"
                                                       Foreground="#666666"
                                                       TextWrapping="Wrap"
                                                       Margin="0,10,0,10"/>

                                            <!-- تفاصيل المهمة -->
                                            <Border Grid.Row="2"
                                                    Background="#F8F9FA"
                                                    CornerRadius="6"
                                                    Padding="10,8">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- المستخدم المسؤول -->
                                                    <StackPanel Grid.Column="0"
                                                                Orientation="Horizontal"
                                                                Margin="0,0,15,0">
                                                        <TextBlock Text="&#xE77B;"
                                                                   FontFamily="Segoe MDL2 Assets"
                                                                   Foreground="#666666"
                                                                   Margin="0,0,5,0"/>
                                                        <TextBlock Text="{Binding AssignedToUser.FullName}"
                                                                   Foreground="#666666"
                                                                   FontWeight="SemiBold"/>
                                                    </StackPanel>

                                                    <!-- تاريخ الاستحقاق -->
                                                    <StackPanel Grid.Column="1"
                                                                Orientation="Horizontal"
                                                                Margin="0,0,15,0">
                                                        <TextBlock Text="&#xE787;"
                                                                   FontFamily="Segoe MDL2 Assets"
                                                                   Foreground="#666666"
                                                                   Margin="0,0,5,0"/>
                                                        <TextBlock Text="{Binding DueDate, Converter={StaticResource DateToStringConverter}}"
                                                                   Foreground="#666666"
                                                                   FontWeight="SemiBold"/>
                                                    </StackPanel>

                                                    <!-- الحساب المرتبط -->
                                                    <StackPanel Grid.Column="2"
                                                                Orientation="Horizontal"
                                                                Margin="0,0,15,0"
                                                                Visibility="{Binding HasRelatedAccount, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                        <TextBlock Text="&#xE825;"
                                                                   FontFamily="Segoe MDL2 Assets"
                                                                   Foreground="#666666"
                                                                   Margin="0,0,5,0"/>
                                                        <TextBlock Text="{Binding RelatedAccount.Name}"
                                                                   Foreground="#666666"
                                                                   FontWeight="SemiBold"/>
                                                    </StackPanel>

                                                    <!-- نسبة الإكمال -->
                                                    <StackPanel Grid.Column="4"
                                                                Orientation="Horizontal">
                                                        <TextBlock Text="{Binding CompletionPercentage, StringFormat={}{0}%}"
                                                                   Foreground="#666666"
                                                                   FontWeight="SemiBold"
                                                                   Margin="0,0,8,0"/>
                                                        <ProgressBar Value="{Binding CompletionPercentage}"
                                                                     Maximum="100"
                                                                     Width="100"
                                                                     Height="12">
                                                            <ProgressBar.Resources>
                                                                <Style TargetType="Border">
                                                                    <Setter Property="CornerRadius" Value="6"/>
                                                                </Style>
                                                            </ProgressBar.Resources>
                                                        </ProgressBar>
                                                    </StackPanel>
                                                </Grid>
                                            </Border>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>

                    <!-- رسالة عند عدم وجود مهام -->
                    <Border Background="#F8F9FA"
                            CornerRadius="8"
                            Padding="20,15"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Visibility="{Binding HasNoTasks, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                            <TextBlock Text="&#xE9CE;"
                                       FontFamily="Segoe MDL2 Assets"
                                       FontSize="48"
                                       Foreground="#BBBBBB"
                                       HorizontalAlignment="Center"
                                       Margin="0,0,0,10"/>
                            <TextBlock Text="لا توجد مهام للعرض"
                                       FontSize="18"
                                       Foreground="#777777"
                                       HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Grid>
        </Border>

        <!-- نافذة إضافة/تعديل مهمة -->
        <Border Grid.Row="0"
                Grid.RowSpan="3"
                Background="#80000000"
                Visibility="{Binding IsTaskDialogOpen, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Border Width="600"
                    Background="White"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1"
                    CornerRadius="10"
                    Padding="20">
                <Border.Effect>
                    <DropShadowEffect ShadowDepth="2" BlurRadius="10" Opacity="0.2" Color="#000000"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان النافذة -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="&#xE7C1;"
                                   FontFamily="Segoe MDL2 Assets"
                                   Foreground="{DynamicResource PrimaryColor}"
                                   FontSize="20"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
                        <TextBlock Text="{Binding TaskDialogTitle}"
                                   FontSize="20"
                                   FontWeight="Bold"
                                   Foreground="#333333"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- نموذج المهمة -->
                    <ScrollViewer Grid.Row="1"
                                  VerticalScrollBarVisibility="Auto"
                                  MaxHeight="400">
                        <StackPanel>
                            <!-- عنوان المهمة -->
                            <TextBlock Text="عنوان المهمة *"
                                       FontWeight="SemiBold"
                                       Margin="0,0,0,5"/>
                            <TextBox Text="{Binding CurrentTask.Title, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="0,0,0,15"/>

                            <!-- وصف المهمة -->
                            <TextBlock Text="وصف المهمة"
                                       FontWeight="SemiBold"
                                       Margin="0,0,0,5"/>
                            <TextBox Text="{Binding CurrentTask.Description}"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     Height="80"
                                     Margin="0,0,0,15"/>

                            <!-- تاريخ الاستحقاق -->
                            <TextBlock Text="تاريخ الاستحقاق *"
                                       FontWeight="SemiBold"
                                       Margin="0,0,0,5"/>
                            <DatePicker SelectedDate="{Binding CurrentTask.DueDate}"
                                        Margin="0,0,0,15"/>

                            <!-- حالة المهمة -->
                            <TextBlock Text="حالة المهمة *"
                                       FontWeight="SemiBold"
                                       Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding TaskStatuses}"
                                      SelectedItem="{Binding SelectedTaskStatusForDialog}"
                                      DisplayMemberPath="Name"
                                      Margin="0,0,0,15"/>

                            <!-- أولوية المهمة -->
                            <TextBlock Text="أولوية المهمة *"
                                       FontWeight="SemiBold"
                                       Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding TaskPriorities}"
                                      SelectedItem="{Binding SelectedTaskPriorityForDialog}"
                                      DisplayMemberPath="Name"
                                      Margin="0,0,0,15"/>

                            <!-- المستخدم المسؤول -->
                            <TextBlock Text="المستخدم المسؤول"
                                       FontWeight="SemiBold"
                                       Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding Users}"
                                      SelectedItem="{Binding SelectedUserForDialog}"
                                      DisplayMemberPath="FullName"
                                      Margin="0,0,0,15"/>

                            <!-- الحساب المرتبط -->
                            <TextBlock Text="الحساب المرتبط"
                                       FontWeight="SemiBold"
                                       Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding Accounts}"
                                      SelectedItem="{Binding SelectedAccountForDialog}"
                                      DisplayMemberPath="Name"
                                      Margin="0,0,0,15"/>

                            <!-- نسبة الإكمال -->
                            <TextBlock Text="نسبة الإكمال"
                                       FontWeight="SemiBold"
                                       Margin="0,0,0,5"/>
                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Slider Grid.Column="0"
                                        Minimum="0"
                                        Maximum="100"
                                        Value="{Binding CurrentTask.CompletionPercentage}"
                                        TickFrequency="10"
                                        TickPlacement="BottomRight"
                                        IsSnapToTickEnabled="True"/>
                                <TextBlock Grid.Column="1"
                                           Text="{Binding CurrentTask.CompletionPercentage, StringFormat={}{0}%}"
                                           Margin="10,0,0,0"
                                           VerticalAlignment="Center"/>
                            </Grid>

                            <!-- المهمة متكررة -->
                            <CheckBox Content="مهمة متكررة"
                                      IsChecked="{Binding CurrentTask.IsRecurring}"
                                      Margin="0,0,0,10"/>

                            <!-- نمط التكرار -->
                            <StackPanel Visibility="{Binding CurrentTask.IsRecurring, Converter={StaticResource BooleanToVisibilityConverter}}"
                                        Margin="20,0,0,15">
                                <TextBlock Text="نمط التكرار"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding RecurrencePatterns}"
                                          SelectedItem="{Binding SelectedRecurrencePatternForDialog}"
                                          DisplayMemberPath="Name"/>
                            </StackPanel>

                            <!-- ملاحظات -->
                            <TextBlock Text="ملاحظات"
                                       FontWeight="SemiBold"
                                       Margin="0,0,0,5"/>
                            <TextBox Text="{Binding CurrentTask.Notes}"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     Height="60"/>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- أزرار الإجراءات -->
                    <StackPanel Grid.Row="2"
                                Orientation="Horizontal"
                                HorizontalAlignment="Right"
                                Margin="0,15,0,0">
                        <Button Command="{Binding CancelTaskDialogCommand}"
                                Style="{StaticResource ModernButton}"
                                Margin="0,0,10,0"
                                Padding="12,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="&#xE711;"
                                           FontFamily="Segoe MDL2 Assets"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                                <TextBlock Text="إلغاء"
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                        <Button Command="{Binding SaveTaskCommand}"
                                Style="{StaticResource GreenButton}"
                                Padding="12,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="&#xE74E;"
                                           FontFamily="Segoe MDL2 Assets"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                                <TextBlock Text="حفظ"
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
        </Border>
    </Grid>
</UserControl>
