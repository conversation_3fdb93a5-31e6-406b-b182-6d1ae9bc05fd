using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using SmartAccountPro.Core.Models;
using SmartAccountPro.Data;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace SmartAccountPro.Helpers
{
    /// <summary>
    /// فئة مساعدة لإدارة المرفقات
    /// </summary>
    public class AttachmentManager
    {
        private readonly AppDbContext _context;
        private readonly string _attachmentsPath;
        private readonly List<string> _allowedExtensions;
        private readonly long _maxFileSize;

        /// <summary>
        /// إنشاء مدير المرفقات
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public AttachmentManager(AppDbContext context)
        {
            _context = context;
            _attachmentsPath = Path.Combine(App.AppDataPath, "Attachments");
            _allowedExtensions = new List<string> { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".jpeg", ".png", ".gif", ".txt", ".rtf", ".zip", ".rar" };
            _maxFileSize = 10 * 1024 * 1024; // 10 ميجابايت

            // إنشاء مجلد المرفقات إذا لم يكن موجوداً
            if (!Directory.Exists(_attachmentsPath))
            {
                Directory.CreateDirectory(_attachmentsPath);
            }
        }

        /// <summary>
        /// إضافة مرفق
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="description">وصف المرفق</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>المرفق</returns>
        public async Task<Attachment?> AddAttachmentAsync(string entityType, int entityId, string filePath, string description, int userId)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("AttachmentManager.AddAttachment", async () =>
                {
                    // التحقق من وجود الملف
                    if (!File.Exists(filePath))
                    {
                        throw new FileNotFoundException($"الملف غير موجود: {filePath}");
                    }

                    // الحصول على معلومات الملف
                    var fileInfo = new FileInfo(filePath);

                    // التحقق من امتداد الملف
                    string extension = fileInfo.Extension.ToLower();
                    if (!_allowedExtensions.Contains(extension))
                    {
                        throw new InvalidOperationException($"امتداد الملف غير مسموح به: {extension}");
                    }

                    // التحقق من حجم الملف
                    if (fileInfo.Length > _maxFileSize)
                    {
                        throw new InvalidOperationException($"حجم الملف كبير جداً: {FileSystemHelper.FormatFileSize(fileInfo.Length)}. الحد الأقصى: {FileSystemHelper.FormatFileSize(_maxFileSize)}");
                    }

                    // إنشاء معرف فريد للملف
                    string fileId = Guid.NewGuid().ToString();

                    // تحديد مسار الملف الوجهة
                    string destinationDirectory = Path.Combine(_attachmentsPath, entityType, entityId.ToString());
                    string destinationFileName = $"{fileId}{extension}";
                    string destinationPath = Path.Combine(destinationDirectory, destinationFileName);

                    // إنشاء مجلد الوجهة إذا لم يكن موجوداً
                    if (!Directory.Exists(destinationDirectory))
                    {
                        Directory.CreateDirectory(destinationDirectory);
                    }

                    // نسخ الملف
                    File.Copy(filePath, destinationPath);

                    // حساب تجزئة MD5 للملف
                    string md5Hash = FileSystemHelper.CalculateFileMD5(destinationPath);

                    // إنشاء المرفق
                    var attachment = new Attachment
                    {
                        EntityType = entityType,
                        EntityId = entityId,
                        FileName = fileInfo.Name,
                        FileSize = fileInfo.Length,
                        FileType = extension,
                        FilePath = destinationPath,
                        FileHash = md5Hash,
                        Description = description,
                        UploadedAt = DateTime.Now,
                        UploadedBy = userId
                    };

                    // إضافة المرفق إلى قاعدة البيانات
                    _context.Attachments.Add(attachment);
                    await _context.SaveChangesAsync();

                    // تسجيل إضافة المرفق
                    AuditTrailManager.LogAction("إضافة مرفق", entityType, entityId, $"تم إضافة مرفق: {fileInfo.Name}", userId);

                    Debug.WriteLine($"[ATTACHMENT] تم إضافة مرفق: {fileInfo.Name} للكيان {entityType} (ID: {entityId})");
                    return attachment;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AttachmentManager.AddAttachment");
                return null;
            }
        }

        /// <summary>
        /// إضافة مرفق من خلال مربع حوار اختيار الملف
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <param name="description">وصف المرفق</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>المرفق</returns>
        public async Task<Attachment?> AddAttachmentWithDialogAsync(string entityType, int entityId, string description, int userId)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("AttachmentManager.AddAttachmentWithDialog", async () =>
                {
                    // إنشاء مربع حوار اختيار الملف
                    var openFileDialog = new OpenFileDialog
                    {
                        Title = "اختر ملف للإرفاق",
                        Filter = "جميع الملفات المدعومة|*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.jpg;*.jpeg;*.png;*.gif;*.txt;*.rtf;*.zip;*.rar|" +
                                "ملفات PDF (*.pdf)|*.pdf|" +
                                "مستندات Word (*.doc;*.docx)|*.doc;*.docx|" +
                                "جداول Excel (*.xls;*.xlsx)|*.xls;*.xlsx|" +
                                "صور (*.jpg;*.jpeg;*.png;*.gif)|*.jpg;*.jpeg;*.png;*.gif|" +
                                "ملفات نصية (*.txt;*.rtf)|*.txt;*.rtf|" +
                                "ملفات مضغوطة (*.zip;*.rar)|*.zip;*.rar|" +
                                "جميع الملفات (*.*)|*.*",
                        CheckFileExists = true,
                        Multiselect = false
                    };

                    // عرض مربع الحوار
                    if (openFileDialog.ShowDialog() == true)
                    {
                        // إضافة المرفق
                        return await AddAttachmentAsync(entityType, entityId, openFileDialog.FileName, description, userId);
                    }

                    return null;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AttachmentManager.AddAttachmentWithDialog");
                return null;
            }
        }

        /// <summary>
        /// حذف مرفق
        /// </summary>
        /// <param name="attachmentId">معرف المرفق</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نجاح العملية</returns>
        public async Task<bool> DeleteAttachmentAsync(int attachmentId, int userId)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("AttachmentManager.DeleteAttachment", async () =>
                {
                    // البحث عن المرفق
                    var attachment = await _context.Attachments.FindAsync(attachmentId);
                    if (attachment == null)
                    {
                        throw new InvalidOperationException($"المرفق غير موجود: {attachmentId}");
                    }

                    // حذف ملف المرفق
                    if (File.Exists(attachment.FilePath))
                    {
                        File.Delete(attachment.FilePath);
                    }

                    // حذف المرفق من قاعدة البيانات
                    _context.Attachments.Remove(attachment);
                    await _context.SaveChangesAsync();

                    // تسجيل حذف المرفق
                    AuditTrailManager.LogAction("حذف مرفق", attachment.EntityType, attachment.EntityId, $"تم حذف مرفق: {attachment.FileName}", userId);

                    Debug.WriteLine($"[ATTACHMENT] تم حذف مرفق: {attachment.FileName} للكيان {attachment.EntityType} (ID: {attachment.EntityId})");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AttachmentManager.DeleteAttachment");
                return false;
            }
        }

        /// <summary>
        /// الحصول على مرفقات كيان
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <returns>قائمة المرفقات</returns>
        public async Task<List<Attachment>> GetAttachmentsAsync(string entityType, int entityId)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("AttachmentManager.GetAttachments", async () =>
                {
                    // البحث عن المرفقات
                    return await _context.Attachments
                        .Where(a => a.EntityType == entityType && a.EntityId == entityId)
                        .OrderByDescending(a => a.UploadedAt)
                        .ToListAsync();
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AttachmentManager.GetAttachments");
                return new List<Attachment>();
            }
        }

        /// <summary>
        /// فتح مرفق
        /// </summary>
        /// <param name="attachmentId">معرف المرفق</param>
        /// <returns>نجاح العملية</returns>
        public async Task<bool> OpenAttachmentAsync(int attachmentId)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("AttachmentManager.OpenAttachment", async () =>
                {
                    // البحث عن المرفق
                    var attachment = await _context.Attachments.FindAsync(attachmentId);
                    if (attachment == null)
                    {
                        throw new InvalidOperationException($"المرفق غير موجود: {attachmentId}");
                    }

                    // التحقق من وجود الملف
                    if (!File.Exists(attachment.FilePath))
                    {
                        throw new FileNotFoundException($"ملف المرفق غير موجود: {attachment.FilePath}");
                    }

                    // فتح الملف
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = attachment.FilePath,
                        UseShellExecute = true
                    });

                    Debug.WriteLine($"[ATTACHMENT] تم فتح مرفق: {attachment.FileName}");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AttachmentManager.OpenAttachment");
                return false;
            }
        }

        /// <summary>
        /// تصدير مرفق
        /// </summary>
        /// <param name="attachmentId">معرف المرفق</param>
        /// <returns>مسار الملف المصدر</returns>
        public async Task<string?> ExportAttachmentAsync(int attachmentId)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("AttachmentManager.ExportAttachment", async () =>
                {
                    // البحث عن المرفق
                    var attachment = await _context.Attachments.FindAsync(attachmentId);
                    if (attachment == null)
                    {
                        throw new InvalidOperationException($"المرفق غير موجود: {attachmentId}");
                    }

                    // التحقق من وجود الملف
                    if (!File.Exists(attachment.FilePath))
                    {
                        throw new FileNotFoundException($"ملف المرفق غير موجود: {attachment.FilePath}");
                    }

                    // إنشاء مربع حوار حفظ الملف
                    var saveFileDialog = new SaveFileDialog
                    {
                        Title = "حفظ المرفق",
                        FileName = attachment.FileName,
                        Filter = $"الملف الأصلي (*{attachment.FileType})|*{attachment.FileType}|جميع الملفات (*.*)|*.*",
                        DefaultExt = attachment.FileType
                    };

                    // عرض مربع الحوار
                    if (saveFileDialog.ShowDialog() == true)
                    {
                        // نسخ الملف
                        File.Copy(attachment.FilePath, saveFileDialog.FileName, true);

                        Debug.WriteLine($"[ATTACHMENT] تم تصدير مرفق: {attachment.FileName} إلى {saveFileDialog.FileName}");
                        return saveFileDialog.FileName;
                    }

                    return null;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AttachmentManager.ExportAttachment");
                return null;
            }
        }

        /// <summary>
        /// تحديث وصف مرفق
        /// </summary>
        /// <param name="attachmentId">معرف المرفق</param>
        /// <param name="description">الوصف الجديد</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>نجاح العملية</returns>
        public async Task<bool> UpdateAttachmentDescriptionAsync(int attachmentId, string description, int userId)
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("AttachmentManager.UpdateAttachmentDescription", async () =>
                {
                    // البحث عن المرفق
                    var attachment = await _context.Attachments.FindAsync(attachmentId);
                    if (attachment == null)
                    {
                        throw new InvalidOperationException($"المرفق غير موجود: {attachmentId}");
                    }

                    // تحديث الوصف
                    string oldDescription = attachment.Description;
                    attachment.Description = description;

                    // حفظ التغييرات
                    await _context.SaveChangesAsync();

                    // تسجيل تحديث المرفق
                    AuditTrailManager.LogAction("تحديث مرفق", attachment.EntityType, attachment.EntityId, $"تم تحديث وصف مرفق: {attachment.FileName} من '{oldDescription}' إلى '{description}'", userId);

                    Debug.WriteLine($"[ATTACHMENT] تم تحديث وصف مرفق: {attachment.FileName}");
                    return true;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AttachmentManager.UpdateAttachmentDescription");
                return false;
            }
        }

        /// <summary>
        /// التحقق من سلامة المرفقات
        /// </summary>
        /// <returns>نتيجة التحقق</returns>
        public async Task<AttachmentVerificationResult> VerifyAttachmentsAsync()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("AttachmentManager.VerifyAttachments", async () =>
                {
                    // إنشاء نتيجة التحقق
                    var result = new AttachmentVerificationResult
                    {
                        TotalAttachments = 0,
                        ValidAttachments = 0,
                        MissingFiles = new List<Attachment>(),
                        CorruptedFiles = new List<Attachment>()
                    };

                    // الحصول على جميع المرفقات
                    var attachments = await _context.Attachments.ToListAsync();
                    result.TotalAttachments = attachments.Count;

                    // التحقق من كل مرفق
                    foreach (var attachment in attachments)
                    {
                        // التحقق من وجود الملف
                        if (!File.Exists(attachment.FilePath))
                        {
                            result.MissingFiles.Add(attachment);
                            continue;
                        }

                        // التحقق من سلامة الملف
                        string currentHash = FileSystemHelper.CalculateFileMD5(attachment.FilePath);
                        if (currentHash != attachment.FileHash)
                        {
                            result.CorruptedFiles.Add(attachment);
                            continue;
                        }

                        // المرفق صحيح
                        result.ValidAttachments++;
                    }

                    Debug.WriteLine($"[ATTACHMENT] تم التحقق من {result.TotalAttachments} مرفق. صحيح: {result.ValidAttachments}, مفقود: {result.MissingFiles.Count}, تالف: {result.CorruptedFiles.Count}");
                    return result;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AttachmentManager.VerifyAttachments");
                return new AttachmentVerificationResult
                {
                    TotalAttachments = 0,
                    ValidAttachments = 0,
                    MissingFiles = new List<Attachment>(),
                    CorruptedFiles = new List<Attachment>()
                };
            }
        }

        /// <summary>
        /// تنظيف المرفقات غير المستخدمة
        /// </summary>
        /// <returns>عدد المرفقات المحذوفة</returns>
        public async Task<int> CleanupUnusedAttachmentsAsync()
        {
            try
            {
                // قياس وقت تنفيذ العملية
                return await PerformanceHelper.MeasureExecutionTimeAsync("AttachmentManager.CleanupUnusedAttachments", async () =>
                {
                    // الحصول على جميع المرفقات
                    var attachments = await _context.Attachments.ToListAsync();
                    var attachmentPaths = attachments.Select(a => a.FilePath).ToList();

                    // البحث عن جميع ملفات المرفقات
                    var allFiles = Directory.GetFiles(_attachmentsPath, "*.*", SearchOption.AllDirectories);

                    // تحديد الملفات غير المستخدمة
                    var unusedFiles = allFiles.Where(f => !attachmentPaths.Contains(f)).ToList();

                    // حذف الملفات غير المستخدمة
                    foreach (var file in unusedFiles)
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"[ATTACHMENT] فشل حذف ملف غير مستخدم: {file}. الخطأ: {ex.Message}");
                        }
                    }

                    // حذف المجلدات الفارغة
                    foreach (var dir in Directory.GetDirectories(_attachmentsPath, "*", SearchOption.AllDirectories))
                    {
                        try
                        {
                            if (!Directory.EnumerateFileSystemEntries(dir).Any())
                            {
                                Directory.Delete(dir);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"[ATTACHMENT] فشل حذف مجلد فارغ: {dir}. الخطأ: {ex.Message}");
                        }
                    }

                    Debug.WriteLine($"[ATTACHMENT] تم حذف {unusedFiles.Count} ملف غير مستخدم.");
                    return unusedFiles.Count;
                });
            }
            catch (Exception ex)
            {
                ExceptionHandler.HandleException(ex, "AttachmentManager.CleanupUnusedAttachments");
                return -1;
            }
        }
    }

    /// <summary>
    /// نتيجة التحقق من المرفقات
    /// </summary>
    public class AttachmentVerificationResult
    {
        /// <summary>
        /// إجمالي عدد المرفقات
        /// </summary>
        public int TotalAttachments { get; set; }

        /// <summary>
        /// عدد المرفقات الصحيحة
        /// </summary>
        public int ValidAttachments { get; set; }

        /// <summary>
        /// قائمة الملفات المفقودة
        /// </summary>
        public List<Attachment> MissingFiles { get; set; } = new List<Attachment>();

        /// <summary>
        /// قائمة الملفات التالفة
        /// </summary>
        public List<Attachment> CorruptedFiles { get; set; } = new List<Attachment>();
    }
}
