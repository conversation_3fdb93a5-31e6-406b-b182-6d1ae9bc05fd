using System;
using System.Threading.Tasks;

namespace SmartAccountPro
{
    /// <summary>
    /// برنامج تشغيل الاختبارات
    /// </summary>
    class RunTests
    {
        /// <summary>
        /// نقطة دخول تشغيل الاختبارات
        /// </summary>
        static async Task Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("🔍 SmartAccount Pro - اختبار شامل للتطبيق");
            Console.WriteLine("==============================================");

            var tester = new ApplicationTester();
            await tester.RunComprehensiveTestsAsync();

            Console.WriteLine("\nاضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }
    }
}
